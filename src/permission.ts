import router from "@/router";
import local from "@/utils/local";
import routerStore from "@/store/routerStore"
router.beforeEach((to, _from, next) => {
  let token = local.get('t_k');
  let {pathArrs} = routerStore();
  let whiteList = ['/login', '/404', '/401'];
  if (token) {
    if (to.path == "/login") {
      next("/dashboard")
    } else {
      routerStore()
      if (!to.meta.title) {
          if(pathArrs.includes(to.path)){
            next({ path: to.path })
          }
          next({ path: "/error/404" })
      }
      next();
    }
  } else if (whiteList.includes(to.path)) {
    next();
  } else {
    next("/login");
  }
})