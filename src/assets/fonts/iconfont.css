@font-face {
  font-family: "iconfont"; /* Project id 3952184 */
  src: url('iconfont.woff2?t=1678793039147') format('woff2'),
       url('iconfont.woff?t=1678793039147') format('woff'),
       url('iconfont.ttf?t=1678793039147') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-kefu:before {
  content: "\e617";
}

.icon-mima:before {
  content: "\e600";
}

.icon-guanli:before {
  content: "\eb63";
}

.icon-yunying:before {
  content: "\ebd1";
}

.icon-shenhe:before {
  content: "\e715";
}

.icon-zhanghao:before {
  content: "\e71b";
}

.icon-xiaoxi:before {
  content: "\e8be";
}

.icon-data:before {
  content: "\e854";
}

