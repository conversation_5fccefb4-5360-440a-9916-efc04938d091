import axios from 'axios'
import {ElMessage} from 'element-plus'
import local from "@/utils/local" //本地

// 防重复提示机制
let lastSuccessTime = 0;
const SUCCESS_INTERVAL = 1000; // 1秒内不重复显示成功提示

const req = axios.create({
    baseURL: import.meta.env.VITE_API_URL, // 线上：https://api.gxdkkj.xyz
    //测试环境： http://apibeta.csrywlkj.cn
    timeout: 300000 //超时时间
})

req.interceptors.request.use((config) => {
    // 取出token 添加鉴权
    let token = local.get("t_k")
    if (token) {
        config.headers.token = token
    }
    ;
    return config
}, (err) => {
    return Promise.reject(err)
})

req.interceptors.response.use((response) => {
    let { code, message, showTips } = response.data;

    // 判断是否为查询类接口（不需要显示成功提示）
    const isQueryApi = (url: string) => {
        const queryKeywords = [
            'query', 'list', 'page', 'get', 'search', 'find',
            'statistics', 'analyst', 'detail', 'record'
        ];
        return queryKeywords.some(keyword => url.toLowerCase().includes(keyword));
    };

    // 获取请求的URL
    const requestUrl = response.config.url || '';

    if (code == 200) {
        // 如果是查询类接口，不显示成功提示
        if (!isQueryApi(requestUrl)) {
            // 防重复提示：1秒内不重复显示
            const now = Date.now();
            if (now - lastSuccessTime > SUCCESS_INTERVAL) {
                lastSuccessTime = now;
                ElMessage({
                    message: message || "成功",
                    type: "success",
                    offset: window.screen.height / 2,
                });
            }
        }
    }

    if (code == 401 || code == 507) {
        local.clear();
        location.reload();
    }
    if (code == 500) {
        ElMessage.error({
            message,
            offset: window.screen.height / 2, // Adjust this value as needed
        });
    }
    if (code !== undefined && message !== undefined && code !== 200) {
        if (showTips == true) {
            ElMessage.error({
                message,
                offset: window.screen.height / 2, // Adjust this value as needed
            });
        }
    }
    return response;
}, (err) => {
    return Promise.reject(err);
});

export default req