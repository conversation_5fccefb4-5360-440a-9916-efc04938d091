// 从小到大
export function sortChange(array: any) {
    for (let i = 0; i < array.length; i++) {
        let minIndex = i
        for (let k = i + 1; k < array.length; k++) {
            if (array[k] < array[minIndex]) {
                minIndex = k
            }
        }
        [array[i], array[minIndex]] = [array[minIndex], array[i]]
    }
}

export function selectionSort(arr:any) {
  var len = arr.length;
  var minIndex, temp;
  for (var i = 0; i < len - 1; i++) {
      minIndex = i;
      for (var j = i + 1; j < len; j++) {
          if (arr[j] < arr[minIndex]) {     // 寻找最小的数
              minIndex = j;                 // 将最小数的索引保存
          }
      }
      temp = arr[i];
      arr[i] = arr[minIndex];
      arr[minIndex] = temp;
  }
  return arr;
} 

//对象数组排序
export function compare(property:any){
  return function(a:any,b:any){
      var value1 = a[property];
      var value2 = b[property];
      return value2 - value1;//升序,降序为value2 - value1
  }
}
// arr.sort(compare('age'))



