// 腾讯云对象存储
import COS from 'cos-js-sdk-v5'

const cos = new COS({
    SecretId: 'AKID4ziy5WnsLkuqbAamiU4SffT3YuFUVJ4r',
    SecretKey: 'kl1LgzB9ycAau55zmPrRtCLIc0HhyEOO',
    // SecurityToken: t.session_token
});


export function handleFileInUploading(file) {
    cos.putObject({
        Bucket: 'mitang-admin-1317171866', /* 填写自己的 bucket，必须字段 */
        Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
        Key: file.name,              /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */
        StorageClass: 'STANDARD', //上传模式，标准
        Body: file, // 上传文件对象
        SliceSize: 1024 * 1024 * 5,     /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */
        onProgress: function(progressData) {
            console.log(JSON.stringify(progressData));
        }
    }, function(err, data) {
        if (err) {
          console.log('上传失败', err);
        } else {
            
          console.log('上传成功');
        }
    });
  }


