// 验证账号
export let validateAccount = (_rule: any, value: any, callback: any) => {
    if (value.trim() === '') {
        callback(new Error('账号不能为空哦！'))
    } else if (!/^[a-zA-Z0-9\u4e00-\u9fa5]{2,12}$/.test(value)) {
        callback(new Error('账号应为2-12位(字母,数字,中文)'))
    } else {
        callback();
    }
}
// 验证密码
export let validatePassword = (_rule: any, value: any, callback: any) => {
    if (value.trim() === '') {
        callback(new Error('密码不能为空哦！'))
    } else if (!/^[a-zA-Z0-9]{5,12}$/.test(value)) {
        callback(new Error('密码应为5-12位(字母,数字)'))
    } else {
        callback();
    }
}