/**
 * 日期相关操作封装
 */
/* 补零 */
export function padZero(n:any) {
    return n < 10 ? '0' + n : n;
}

/* 获取年月日 */
export function getYMD(separator = '/', t = Date.now()) {
    const date = new Date(typeof separator == 'number' ? separator : t)

    let y = date.getFullYear()
    let m = date.getMonth() + 1
    let d = date.getDate()

    // return y + s + padZero(m) + s + padZero(d)
    return [y, m, d].map(function (v) {
        return padZero(v)  // [2021, 07, 08]
    }).join(typeof separator == 'number' ? '/' : separator)
}

/* 获取时分秒 */
export function getHMS(separator = ':', t = Date.now()) {
    const date = new Date(typeof separator == 'number' ? separator : t)
    let h = date.getHours()
    let m = date.getMinutes()
    let s = date.getSeconds()

    return [h, m, s].map(function (v) {
        return padZero(v)  // [2021, 07, 08]
    }).join(typeof separator == 'number' ? ':' : separator)
}

/* 获取年月日 时分秒 */
export function getYMDHMS(s1:any='-', s2:any=':', t:any) {
    return getYMD(s1, t) + ' ' + getHMS(s2, t)
}

/* 获取星期 */
export function getWeek(t = Date.now()) {
    return '星期' + ['天', '一', '二', '三', '四', '五', '六'][new Date(t).getDay()]
}

export function secondsToHms(d:number) {
  var h = Math.floor(d / 3600);
  var m = Math.floor((d % 3600) / 60);
  var s = Math.floor((d % 3600) % 60);
  var hDisplay = h > 0 ?(h>=10?h:`0${h}`):'00';
  var mDisplay = m > 0 ?(m>=10?m:`0${m}`): '00';
  var sDisplay = s > 0 ?(s>=10?s:`0${s}`): '00';
  return hDisplay+':' + mDisplay+':' + sDisplay;
}


export const time = (time:any)=> {//时间戳转日期
    let date = new Date(parseInt(time) * 1000);
    let y = date.getFullYear();
    let MM:any = date.getMonth() + 1;
    MM = MM < 10 ? ('0' + MM) : MM;
    let d:any = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    let h:any = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    let m:any = date.getMinutes();
    m = m < 10 ? ('0' + m) : m;
    let s:any = date.getSeconds();
    s = s < 10 ? ('0' + s) : s;
    return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
    // return y + '-' + MM + '-' + d;
}

export function TimestampToDate(Timestamp:any) {
    let date1 = new Date(Timestamp);
    return date1.toLocaleDateString().replace(/\//g, "-") + " " + date1.toTimeString().substr(0, 8); 
}


export function convertDateFormat(dateString:any) {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours() % 12 || 12).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

//今天
let  toData = new Date(new Date().toLocaleDateString()).getTime();


//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;

//最近30天
let past30daysStart = toData - 30 * 3600 * 24 * 1000;

let todayEnd = toData + 24 * 60 * 60 * 1000 - 1; //结束时间

export {toData,past7daysStart,todayEnd,past30daysStart}