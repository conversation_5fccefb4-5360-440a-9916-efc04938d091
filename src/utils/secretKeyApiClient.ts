import CryptoJS from 'crypto-js'
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'
import { API_CONFIG } from '@/config/apiConfig'

export class SecretKeyApiClient {
  private secretId: string
  private secretKey: string
  private baseURL: string
  private axiosInstance: AxiosInstance

  constructor() {
    this.secretId = API_CONFIG.secretId
    this.secretKey = API_CONFIG.secretKey
    this.baseURL = API_CONFIG.baseURL
    
    // 创建 axios 实例
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: API_CONFIG.timeout
    })
  }

  /**
   * 计算签名
   */
  private calculateSignature(
    method: string,
    uri: string,
    queryString: string,
    body: string,
    timestamp: number,
    nonce: string
  ): string {
    const stringToSign = [
      method.toUpperCase(),
      uri,
      queryString,
      CryptoJS.SHA256(body).toString(),
      this.secretId,
      timestamp,
      nonce
    ].join('\n')
    
    return CryptoJS.HmacSHA256(stringToSign, this.secretKey).toString()
  }

  /**
   * 发送认证请求
   */
  async request(
    method: string,
    path: string,
    params: Record<string, any> = {},
    data: any = null
  ) {
    const timestamp = Math.floor(Date.now() / 1000)
    const nonce = 'admin' + Math.floor(Math.random() * 10000)

    let uri = path
    let queryString = ''

    // 处理 GET 请求的查询参数
    if (method.toUpperCase() === 'GET' && Object.keys(params).length > 0) {
      queryString = new URLSearchParams(params).toString()
      uri += '?' + queryString
    }

    const body = data ? JSON.stringify(data) : ''
    const signature = this.calculateSignature(method, uri, queryString, body, timestamp, nonce)

    const config: AxiosRequestConfig = {
      method: method as any,
      url: uri,
      headers: {
        'X-Secret-Id': this.secretId,
        'X-Signature': signature,
        'X-Timestamp': timestamp.toString(),
        'X-Nonce': nonce,
        'Content-Type': 'application/json'
      }
    }

    if (data) {
      config.data = data
    }

    if (method.toUpperCase() === 'POST' && Object.keys(params).length > 0) {
      config.data = params
    }

    return this.axiosInstance.request(config)
  }

  /**
   * GET 请求
   */
  async get(path: string, params: Record<string, any> = {}) {
    return this.request('GET', path, params)
  }

  /**
   * POST 请求
   */
  async post(path: string, data: any = {}) {
    return this.request('POST', path, {}, data)
  }

  /**
   * PUT 请求
   */
  async put(path: string, data: any = {}) {
    return this.request('PUT', path, {}, data)
  }

  /**
   * DELETE 请求
   */
  async delete(path: string, params: Record<string, any> = {}) {
    return this.request('DELETE', path, params)
  }
}

// 创建单例实例
export const secretKeyApiClient = new SecretKeyApiClient()
