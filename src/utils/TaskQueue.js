class TaskQueue {
  constructor() {
    this.max = 3; //最大并发数
    this.taskList = [] //用shift方法实现先进先出
    setTimeout(() => { //这里初始化队列后自动执行，后续有新任务添加则需要手动执行。
      this.run()
    })
  }
  addTask(task) {
    this.taskList.push(task);
  }

  run() {
    const length = this.taskList.length;

    if (!length) {
      return;
    }
    const min = Math.min(length, this.max);// 控制并发数量
    for (let i = 0; i < min; i++) {
      this.max--; //开始占用一个任务的空间
      const task = this.taskList.shift();
      task().then(res => {
        console.log(res);
      }).catch(error => {
        console.log(error);
      }).finally(() => {
        this.max++; //任务完成，释放空间
        this.run();//自动进行下一个任务
      })
    }
  }
}
export default  TaskQueue

