// SecretKey API 配置
export const API_CONFIG = {
  // API 基础地址
  baseURL: 'https://tapi.ge0.cc',

  // API 认证密钥配置 - 必须通过环境变量设置
  secretId: import.meta.env.VITE_SECRET_ID,
  secretKey: import.meta.env.VITE_SECRET_KEY,

  // 请求超时时间（毫秒）
  timeout: 30000
}

// 🔐 安全配置说明：
// ⚠️  必须配置环境变量，否则应用无法正常工作！
// 1. 在项目根目录创建 .env.local 文件（不要提交到 Git）
// 2. 在 .env.local 中设置：
//    VITE_SECRET_ID=你的真实SecretId
//    VITE_SECRET_KEY=你的真实SecretKey
// 3. 确保 .env.local 已添加到 .gitignore 中
// 4. 重启开发服务器使配置生效
