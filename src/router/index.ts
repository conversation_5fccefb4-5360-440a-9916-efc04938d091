// 引入路由
import { createRouter, createWebHistory } from "vue-router"
// 引入布局
import Layout from "@/views/layout/index.vue"
// import { MyRouteRecordRaw } from "@/types/router"
// 路由配置
const defroutes = [
    {
        path: "/",
        redirect: "/login",
    },
    {
        path: "/login",
        name: "login",
        component: () => import("@/views/login/login.vue"),
    },
    {
        //首页
        path: "/dashboard",
        name: 'dashboard',
        component: Layout,
        meta: { title: "", path: "" },
        redirect: "/dashboard/home",
        children: [
            {
                path: "home",
                meta: { title: "首页", path: "/dashboard/home" },
                component: () => import("@/views/dashboard/welcome.vue"),
            }
        ]
    },
    {
        //个人中心
        path: "/info",
        name: 'info',
        component: Layout,
        meta: { title: "", path: "" },
        redirect: "/info/edpass",
        children: [
            {
                path: "edpass",
                meta: { title: "个人中心", path: "/info/edpass" },
                component: () => import("@/views/editpassword/index.vue"),
            }
        ]
    },
    // {
    //   path: '/:pathMatch(.*)',
    //   redirect: '/dashboard',
    // },
    {
      path: "/error",
      name: '404',
      component: Layout,
      redirect: "/error/404",
      children: [
          {
              path: "/error/404",
              meta: { title: "404", path: "/error/404" },
              component: () => import("@/views/error/404.vue"),
          }
      ]
  },
]

// 路由模式
let router = createRouter({
    routes: defroutes,
    history: createWebHistory(),
})

export default router