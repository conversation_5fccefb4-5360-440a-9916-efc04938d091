import req from "@/utils/request";

// 男性用户数据
export const queryManUserAnalyst_api = (data:dataDetail) => {
    return req({
        url: "/admin/data_analyst/man_user_analyst/query",
        method: "post",
        data
    })
}
// 充值分组查看
export const queryreChargeAmountAnalyst_api = (data:dataDetail) => {
    return req({
        url: "/admin/data_analyst/recharge_amount_analyst/query",
        method: "post",
        data
    })
}
// 女性用户数据
export const queryWoManUserAnalyst_api = (data:dataDetail) => {
    return req({
        url: "/admin/data_analyst/woman_user_analyst_v2/query",
        method: "post",
        data
    })
}
// 查询留存
export const queryAnimate_api = (data:dataDetail) => {
    return req({
        url: "/admin/data_analyst/animate/query",
        method: "post",
        data
    })
}
// 渠道
export const queryChannelAnalyst_api = (data:dataDetail) => {
    return req({
        url: "/admin/data_analyst/channel_analyst/query",
        method: "post",
        data
    })
}
// 查询渠道列表
export const queryChannelAnalystList_api = (data:dataDetail) => {
    return req({
        url: "/admin/data_analyst/channel_analyst/query_list",
        method: "post",
        data
    })
}
// 添加消耗下载数
export const saveChannelData_api = (data:dataDetail) => {
    return req({
        url: "/admin/data_analyst/channel_data/save",
        method: "post",
        data
    })
}
// 渠道筛选查询
export const queryChannel_api = () => {
    return req({
        url: "/admin/data_analyst/query_channel",
        method: "post",
    })
}
// 渠道筛选
export const query_channel_list = () => {
  return req({
    url: "/admin/admin/common/channel/list",
    method: "post",
  })
}
// 累计ROI列表
export const queryChannel_roi_list = (data:any) => {
  return req({
      url: "/admin/statistics/channel_roi/list",
      method: "post",
      data
  })
}
// 累计ROI列表2
export const queryChannel_roi_list2 = (data:any) => {
    return req({
        url: "/admin/statistics/channel_roi2/list",
        method: "post",
        data
    })
  }
// 更新投放金额
export const queryChannel_roi = (data:any) => {
  return req({
      url: "/admin/statistics/channel_roi/modify/investment",
      method: "post",
      data
  })
}

// 更新投放金额
export const queryNew_channel_details = (data:any) => {
  return req({
      url: "/admin/data_analyst/new_channel_details",
      method: "post",
      data
  })
}

// 更新投放金额
export const queryNewreChargeAmountAnalyst_api = (data:any) => {
  return req({
      url: "/admin/data_analyst/recharge_amount_analyst_v2/query",
      method: "post",
      data
  })
}


export const channel_report_api = (data:any) => {
  return req({
      url: "/admin/data_analyst/channel_report",
      method: "post",
      data
  })
}