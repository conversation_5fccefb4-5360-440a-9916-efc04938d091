import req from "@/utils/request";

// 查询版本信息
export const queryVersion_api = (data:version) => {
  return req({
    url: "/admin/version/controls/query_version",
    method: "post",
    data
  })
}
// 删除版本信息
export const delVersion_api = (data:version) => {
  return req({
    url: "/admin/version/controls/del_version",
    method: "post",
    data
  })
}
// 修改版本信息
export const modifyVersion_api = (data:version) => {
  return req({
    url: "/admin/version/controls/modify_version",
    method: "post",
    data
  })
}
// 新增版本信息
export const addVersion_api = (data:version) => {
  return req({
    url: "/admin/version/controls/add_version",
    method: "post",
    data
  })
}
// 查询渠道
export const queryUserChannel_api = (data:version) => {
  return req({
    url: "/admin/user_channel/manage/query_user_channel",
    method: "post",
    data
  })
}
// 新增渠道
export const saveUserChannel_api = (data:version) => {
  return req({
    url: "/admin/user_channel/manage/save_user_channel",
    method: "post",
    data
  })
}
// 删除渠道
export const delUserChannel_api = (data:version) => {
  return req({
    url: "/admin/user_channel/manage/del_user_channel",
    method: "post",
    data
  })
}
// 修改渠道
export const modifyUserChannel_api = (data:version) => {
  return req({
    url: "/admin/user_channel/manage/modify_user_channel",
    method: "post",
    data
  })
}
// 查询审核包
export const queryAuditPackage_api = (data:version) => {
  return req({
    url: "/admin/version/controls/query_audit_package",
    method: "post",
    data
  })
}
// 删除审核包
export const delAuditPackage_api = (data:version) => {
  return req({
    url: "/admin/version/controls/del_audit_package",
    method: "post",
    data
  })
}
// 新增审核包
export const addAuditPackage_api = (data:version) => {
  return req({
    url: "/admin/version/controls/add_audit_package",
    method: "post",
    data
  })
}
