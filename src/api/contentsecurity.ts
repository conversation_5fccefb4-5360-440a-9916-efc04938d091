import req from "@/utils/request";

// 动态审核查询
export const queryDynamic_api = (data:any) => {
    return req({
        url: "/admin/audit_manage/dynamic/query_dynamic_page",
        method: "post",
        data
    })
}
// 动态编辑
export const modifyDynamic_api = (data:dynamic) => {
    return req({
        url: "/admin/audit_manage/dynamic/modify_dynamic",
        method: "post",
        data
    })
}
// 相册查询
export const queryAlbum_api = (data:any) => {
    return req({
        url: "/admin/audit_manage/album/query_user_album_page",
        method: "post",
        data
    })
}
// 相册编辑
export const modifyAlbum_api = (data:dynamic) => {
    return req({
        url: "/admin/audit_manage/album/modify_user_album",
        method: "post",
        data
    })
}