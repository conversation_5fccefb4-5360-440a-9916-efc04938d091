import req from "@/utils/request";

// 查询快捷语配置
export const query_reqly_api = (data:whitelist) => {
  return req({
      url: "/admin/operation/quick_reply/query_quick_reply",
      method: "post",
      data
  })
}
// 添加快捷语配置
export const add_reqly_api = (data:any) => {
  return req({
      url: "/admin/operation/quick_reply/add_quick_reply",
      method: "post",
      data
  })
}
// 修改快捷语配置
export const modify_reqly_api = (data:whitelist) => {
  return req({
      url: "/admin/operation/quick_reply/modify_quick_reply",
      method: "post",
      data
  })
}
// 删除快捷语配置
export const del_reqly_api = (data:whitelist) => {
  return req({
      url: "/admin/operation/quick_reply/del_quick_reply",
      method: "post",
      data
  })
}
// 女用户评级
export const getfemale_level = (data:whitelist) => {
  return req({
      url: "/admin/user/female_level/list",
      method: "post",
      data
  })
}
// 编辑添加女用户评级
export const save_level = (data:whitelist) => {
  return req({
      url: "/admin/user/female_level/save_or_update",
      method: "post",
      data
  })
}

// 分发常用语
export const queryPeration_management = (data:whitelist) => {
  return req({
      url: "admin/operation/operation_management/query_chat_up_hello_page",
      method: "post",
      data
  })
}
// 修改分发常用语
export const savePeration_management = (data:any) => {
  return req({
      url: "admin/operation/operation_management/save_or_upd_chat_up_hello",
      method: "post",
      data
  })
}
// 修改分发常用语
export const delPeration_management = (data:whitelist) => {
  return req({
      url: "admin/operation/operation_management/del_chat_up_hello",
      method: "post",
      data
  })
}

// 离线
export const offline_push_msg = (data:any) => {
  return req({
      url: "admin/amin/offline_push_msg/send",
      method: "post",
      data
  })
}
// 离线
export const offline_push_sendMsg = (data:any) => {
  return req({
      url: "admin/amin/offline_push_msg/sendMsg",
      method: "post",
      data
  })
}
// 离线
export const add_banner_info = (data:any) => {
  return req({
      url: "admin/admin/banner_config/add",
      method: "post",
      data
  })
}
// 离线
export const modify_banner_info = (data:any) => {
  return req({
      url: "admin/admin/banner_config/modify_info",
      method: "post",
      data
  })
}
// 离线
export const query_banner_list = (data:any) => {
  return req({
      url: "admin/admin/banner_config/query_list",
      method: "post",
      data
  })
}

// 反馈历史
export const query_reedback_record = (data:any) => {
  return req({
      url: "admin/operation/reedback_record/query_reedback_record",
      method: "post",
      data
  })
}


// 删除反馈历史
export const delete_reedback_record = (data:any) => {
  return req({
      url: "admin/operation/operation_management/del_reedback_record",
      method: "post",
      data
  })
}

export const query_recall_data = (data:any) => {
  return req({
      url: "/admin/amin/offline_push_msg/recall/data",
      method: "post",
      data
  })
}

export const query_jm_send_recall_roi = (data:any) => {
  return req({
      url: "/admin/statistics/jm_send_recall_roi/list",
      method: "post",
      data
  })
}

export const query_distribution_record = (data:any) => {
  return req({
      url: "/admin/user/distribution_record/list",
      method: "post",
      data
  })
}
// 首页
export const queryhome = ({mode,applicationId}:any) => {
  return req({
      url: `/admin/home?mode=${mode}&applicationId=${applicationId}`,
    method: "post",
  })
}
// 女用户数据
export const query_woman_data = (data:any) => {
  return req({
      url: "/admin/data_analyst/woman_user_data/query",
      method: "post",
      data
  })
}
// 视频数据
export const queryideo_statistic = () => {
  return req({
      url: "/admin/admin/free_card/video_statistic",
      method: "post",
  })
}
//打招呼数据
export const queryHello = (data:any) => {
  return req({
      url: "admin/operation/user_chatup_hello/query",
      method: "post",
      data
  })
}// 打招呼操作
export const updateHello = (data:any) => {
  return req({
      url: "admin/operation/user_chatup_hello/update",
      method: "post",
      data
  })
}
// 活动查询
export const query_activity_list = (data:any) => {
  return req({
      url: "admin/admin/activity/query_list",
      method: "post",
      data
  })
}

// 活动新增
export const add_activity_list = (data:any) => {
  return req({
      url: "admin/admin/activity/add",
      method: "post",
      data
  })
}
// 活动修改
export const modify_activity_list = (data:any) => {
  return req({
      url: "admin/admin/activity/modify",
      method: "post",
      data
  })
}

// 兑换物品
export const prop_query_list = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_config/query_list",
      method: "post",
      data
  })
}


// 物品新增
export const add_prop_list = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_config/add",
      method: "post",
      data
  })
}


// 物品修改
export const modify_prop_list = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_config/modify",
      method: "post",
      data
  })
}

// 物品修改
export const query_act_list = () => {
  return req({
      url: "admin/admin/activity_prop_config/query_act_list",
      method: "post",
  })
}

// 物品修改
export const query_intimacy_list = (data:any) => {
  return req({
      url: "admin/admin/activity_intimacy_config/query_list",
      method: "post",
      data
  })
}

// 物品新增
export const add_intimacy_list = (data:any) => {
  return req({
      url: "admin/admin/activity_intimacy_config/add",
      method: "post",
      data
  })
}
// 物品修改
export const modify_intimacy_list = (data:any) => {
  return req({
      url: "admin/admin/activity_intimacy_config/modify",
      method: "post",
      data
  })
}

// 礼物墙活动看板
export const gift_receive_list = () => {
  return req({
      url: "admin/admin/activity_data/gift_exchange/list",
      method: "post",
  })
}

// 亲密度活动看板
export const intimacy_receive_list = () => {
  return req({
      url: "admin/admin/activity_data/intimacy_receive/list",
      method: "post",
  })
}

// 查询任务
export const queryUser_daily_task = (data:any={}) => {
  return req({
      url: "admin/user_task/user_daily_task/query",
      method: "post",
      data
  })
}
// 查询任务
export const addUser_daily_task = (data:any) => {
  return req({
      url: "admin/user_task/user_daily_task/add",
      method: "post",
      data
  })
}

// 任务看板
export const daily_taskList_api = (data:any) => {
  return req({
      url: "admin/user_task/user_daily_task/list",
      method: "post",
      data
  })
}

// 历史
export const query_new_userAward_list= (data:any) => {
  return req({
      url: "admin/new_user/new_user_award_info/list",
      method: "post",
      data
  })
}
// 任务看板
export const add_new_userAward_list = (data:any) => {
  return req({
      url: "admin/new_user/new_user_award_info/add",
      method: "post",
      data
  })
}


export const query_man_list_api = (data:any) => {
  return req({
      url: "admin/admin/intimacy_package_data/man/query_list",
      method: "post",
      data
  })
}

export const query_woman_list_api = (data:any) => {
  return req({
      url: "admin/admin/intimacy_package_data/woman/query_list",
      method: "post",
      data
  })
}
export const query_sum_woman_list_api = (data:any) => {
  return req({
      url: "admin/admin/intimacy_package_data/woman/day/query_list",
      method: "post",
      data
  })
}

export const query_man_prop_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/man/query_list",
      method: "post",
      data
  })
}

export const query_woman_prop_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/woman/query_list",
      method: "post",
      data
  })
}

export const query_sum_man_prop_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/man/day/query_list",
      method: "post",
      data
  })
}

export const query_sum_woman_prop_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/woman/day/query_list",
      method: "post",
      data
  })
}

export const man_user_data_api = (data:any) => {
  return req({
      url: "/admin/data_analyst/man_user_data/query",
      method: "post",
      data
  })
}
export const man_video_card_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/man/video_card/query_list",
      method: "post",
      data
  })
}
export const man_chatup_card_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/man/chatup_card/query_list",
      method: "post",
      data
  })
}
export const man_msg_card_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/man/msg_card/query_list",
      method: "post",
      data
  })
}
export const woman_video_card_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/woman/video_card/query_list",
      method: "post",
      data
  })
}

export const woman_msg_card_api = (data:any) => {
  return req({
      url: "admin/admin/activity_prop_data/woman/msg_card/query_list",
      method: "post",
      data
  })
}
export const statistics_api = (data:any) => {
  return req({
      url: "admin/rtc/rtc_manage/statistics",
      method: "post",
      data
  })
}
export const parity_recharge_data_api = (data:any) => {
  return req({
      url: "admin/admin/parity_recharge_data/list",
      method: "post",
      data
  })
}
export const first_package_api = (data:any) => {
  return req({
      url: "admin/admin/new_user_recharge_data/first_package",
      method: "post",
      data
  })
}

export const xin_package_api = (data:any) => {
  return req({
      url: "admin/admin/new_user_recharge_data/xin_package",
      method: "post",
      data
  })
}

export const video_pay_api = (data:any) => {
  return req({
      url: "admin/user/management/query_user_video",
      method: "post",
      data
  })
}

export const video_show_api = (data:any) => {
  return req({
      url: "admin/admin/video_show/query_list",
      method: "post",
      data
  })
}

export const cancelRechargeVideo_api = (data:any) => {
  return req({
      url: "admin/user/management/cancelRechargeVideo",
      method: "post",
      data
  })
}

export const modifyStatus_api = (data:any) => {
  return req({
      url: "admin/admin/video_show/modifyStatus",
      method: "post",
      data
  })
}

export const update_real_voice_api = (data:any) => {
  return req({
      url: "admin/user_real/real_voice/update_real_voice",
      method: "post",
      data
  })
}
export const query_real_voice_api = (data:any) => {
  return req({
      url: "admin/user_real/real_voice/query_real_voice",
      method: "post",
      data
  })
}

export const query_user_quick_reply = (data:any) => {
  return req({
      url: "admin/operation/user_quick_reply_text/query",
      method: "post",
      data
  })
}
export const update_user_quick_reply = (data:any) => {
  return req({
      url: "admin/operation/user_quick_reply_text/update",
      method: "post",
      data
  })
}

export const stock_query_api = (data:any) => {
  return req({
      url: "admin/admin/chatup/word/stock/query_list",
      method: "post",
      data
  })
}
export const stock_modify_api = (data:any) => {
  return req({
      url: "admin/admin/chatup/word/stock/modify_info",
      method: "post",
      data
  })
}
export const stock_add_api = (data:any) => {
  return req({
      url: "admin/admin/chatup/word/stock/add",
      method: "post",
      data
  })
}

export const stock_del_api = (data:any) => {
  return req({
      url: "admin/admin/chatup/word/stock/del",
      method: "post",
      data
  })
}
export const match_list_api = (data:any) => {
  return req({
      url: "admin/heartbeat/match/data/list",
      method: "post",
      data
  })
}

export const query_user_profile_check_api = (data:any) => {
  return req({
      url: "admin/operation/user_profile_check/query",
      method: "post",
      data
  })
}

export const update_user_profile_check_api = (data:any) => {
  return req({
      url: "admin/operation/user_profile_check/update",
      method: "post",
      data
  })
}
export const make_friends_api = (data?:any) => {
  return req({
      url: "/admin/platform/make/friends/data",
      method: "post",
      data
  })
}

export const save_make_freiends_api = (data:any) => {
  return req({
      url: "/admin/platform/make/friends/save/data",
      method: "post",
      data
  })
}

export const freiends_statistics_api = (data:any) => {
  return req({
      url: "/admin/make/friends/data/statistics",
      method: "post",
      data
  })
}
export const freiends_detail_api = (data:any) => {
  return req({
      url: "/admin/make/friends/data/detail",
      method: "post",
      data
  })
}

export const modifyLevel_api = (data:any) => {
  return req({
      url: "/admin/make/friends/data/modifyLevel",
      method: "post",
      data
  })
}

export const label_Info_api = (data:any) => {
  return req({
      url: "/admin/admin/user/label/modify_info",
      method: "post",
      data
  })
}


export const label_query_api = (data:any) => {
  return req({
      url: "/admin/admin/user/label/query_list",
      method: "post",
      data
  })
}

export const label_add_api = (data:any) => {
  return req({
      url: "/admin/admin/user/label/add",
      method: "post",
      data
  })
}

export const label_del_api = (data:any) => {
  return req({
      url: "/admin/admin/user/label/del",
      method: "post",
      data
  })
}

export const friends_level_api = (data:any) => {
  return req({
      url: "/admin/make/friends/data/level",
      method: "post",
      data
  })
}

export const save_or_update_api = (data:any) => {
  return req({
      url: "/admin/make/friends/data/save_or_update",
      method: "post",
      data
  })
}

export const queryRegister = (applicationId: any) => {
  return req({
    url: `/admin/register?applicationId=${applicationId}`,
    method: "post",
  })
}