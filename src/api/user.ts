import req from "@/utils/request";

// 登录
export const login_api = (data: { password: string, account: string }) => {
    return req({
        url: "/admin/user_manage/admin_account/admin_login",
        method: "post",
        data
    })
}
// 增加角色
export const addRoler_api = (data:role) => {
    return req({
        url: "/admin/user_manage/admin_role/add_role",
        method: "post",
        data
    })
}
// 删除角色
export const delRoler_api = (data:role) => {
    return req({
        url: "/admin/user_manage/admin_role/del_role",
        method: "post",
        data
    })
}
// 修改角色
export const modifyRoler_api = (data:role) => {
    return req({
        url: "/admin/user_manage/admin_role/modify_role",
        method: "post",
        data
    })
}
// 分页查询后台角色
export const queryRoler_api = (data:role) => {
    return req({
        url: "/admin/user_manage/admin_role/query_role",
        method: "post",
        data
    })
}
// 权限跟角色绑定修改
export const modifyResource_api = (data:role) => {
    return req({
        url: "/admin/user_manage/admin_role/modify_role_resource",
        method: "post",
        data
    })
}
// 权限跟角色绑定查询
export const queryResource_api = (data:role) => {
    return req({
        url: "/admin/user_manage/admin_role/query_role_resource",
        method: "post",
        data
    })
}
// 查询后台用户
export const queryAminUser_api = (data:userManage) => {
    return req({
        url: "/admin/user_manage/admin_account/query_admin_user_page",
        method: "post",
        data
    })
}
// 添加后台用户
export const addAminUser_api = (data:userManage) => {
    return req({
        url: "/admin/user_manage/admin_account/add_admin_user",
        method: "post",
        data
    })
}
// 修改密码或者手机号
export const modUserInfo_api = (data:userManage) => {
    return req({
        url: "/admin/user_manage/admin_account/modification_admin_user_info",
        method: "post",
        data
    })
}
// 修改管理员的用户权限
export const modUserAuth_api = (data:userManage) => {
    return req({
        url: "/admin/user_manage/admin_account/modification_admin_user_auth",
        method: "post",
        data
    })
}
// 管理员修改用户信息
export const modifyUser_api = (data:userManage) => {
    return req({
        url: "/admin/user_manage/admin_account/manage_modify_user",
        method: "post",
        data
    })
}
// 删除管理端用户
export const delAdminUser_api = (data:userManage) => {
    return req({
        url: "/admin/user_manage/admin_account/del_admin_user",
        method: "post",
        data
    })
}
// 查询权限列表
export const resource_api = (data:roleMenu) => {
    return req({
        url: "/admin/user_manage/admin_resource/resource_list_page",
        method: "post",
        data
    })
}
// 新增权限列表
export const addAuth_api = (data:roleMenu) => {
    return req({
        url: "/admin/user_manage/admin_resource/add_auth_resource",
        method: "post",
        data
    })
}
// 修改权限资源
export const modifyAuth_api = (data:roleMenu) => {
    return req({
        url: "/admin/user_manage/admin_resource/modify_auth_resource",
        method: "post",
        data
    })
}
// 删除权限资源
export const delAuth_api = (data:roleMenu) => {
    return req({
        url: "/admin/user_manage/admin_resource/del_auth_resource",
        method: "post",
        data
    })
}

export const query_permissions_api = (data:userManage) => {
    return req({
        url: "/admin/user_manage/admin_account/permissions",
        method: "post",
        data
    })
}




