import req from "@/utils/request";

// 查询礼物列表
export const queryGifts_api = (data:gifts) => {
    return req({
        url: "/admin/gift_manage/gifts/query_gifts_page",
        method: "post",
        data
    })
}
// 修改礼物接口
export const modifyGifts_api = (data:gifts) => {
    return req({
        url: "/admin/gift_manage/gifts/modify_gifts",
        method: "post",
        data
    })
}
// 新增礼物
export const addGifts_api = (data:gifts) => {
    return req({
        url: "/admin/gift_manage/gifts/add_gifts",
        method: "post",
        data
    })
}
//删除礼物
export const deleteGift_api = (data: { id: string }) => {
    return req({
        url: "/admin/gift_manage/gifts/delete_gifts",
        method: "post",
        data
    })
}

export const giveGift_api = (data: { id: string }) => {
    return req({
        url: "/admin/gift_manage/gifts/give_gifts",
        method: "post",
        data
    })
}