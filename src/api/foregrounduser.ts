import req from "@/utils/request";

// 前台用户详情查询
export const queryUser_api = (data:foregUser) => {
    return req({
        url: "/admin/user/management/query_user",
        method: "post",
        data
    })
}
// 前台用户查询
export const queryUserPage_api = (data:any) => {
    return req({
        url: "/admin/user/management/query_user_page",
        method: "post",
        data
    })
}
// 前台编辑用户
export const modifyUser_api = (data:foregUser) => {
    return req({
        url: "/admin/user/management/modify_user",
        method: "post",
        data
    })
}


// 手动绑定邀请人
export const inviteBind_api = (data:foregUser) => {
    return req({
        url: "/admin/user/management/invite_user_bind",
        method: "post",
        data
    })
}
// 禁言
export const muteUser_api = (data:foregUser) => {
    return req({
        url: "/admin/user/management/mute_user",
        method: "post",
        data
    })
}
// 警告
export const warnUser_api = (data:foregUser) => {
    return req({
        url: "/admin/user/management/warn_user",
        method: "post",
        data
    })
}
// 封禁用户ip
export const ipUser_api = (data:foregUser) => {
    return req({
        url: "/admin/user/management/banned_user_ip",
        method: "post",
        data
    })
}
// 封禁用户设备
export const deviceUser_api = (data:foregUser) => {
    return req({
        url: "/admin/user/management/banned_user_device",
        method: "post",
        data
    })
}
// 举报
export const queryReport_api = (data:any) => {
    return req({
        url: "/admin/audit_manage/report/query_report_page",
        method: "post",
        data
    })
}
// 举报审核
export const modifyReport_api = (data:report) => {
    return req({
        url: "/admin/audit_manage/report/modify_report",
        method: "post",
        data
    })
}


// 举报审核
export const  modifyName_api = (data:report) => {
  return req({
      url: "/admin/user/management/reset_user_nickname",
      method: "post",
      data
  })
}


// 举报审核
export const  freeze_withdrawal_api = (data:report) => {
  return req({
      url: "admin/user/management/freeze_withdrawal",
      method: "post",
      data
  })
}

// 修改头像
export const  modifyAvatar_api = (data:report) => {
  return req({
      url: "admin/user/management/reset_user_avatar",
      method: "post",
      data
  })
}



// 修改收益比例
export const  income_ratio_api = (data:report) => {
  return req({
      url: "admin/user/management/set/income_ratio",
      method: "post",
      data
  })
}


// 限制用户音视频
export const  limitVideo_api = (data:report) => {
  return req({
      url: "admin/user/management/limitVideo",
      method: "post",
      data
  })
}


export const secure_user_bind_api = (data:any) => {
  return req({
      url: "/admin/user/management/secure_user_bind",
      method: "post",
      data
  })
}

// 警告
export const warnUserzb_api = (data:any) => {
    return req({
        url: "/admin/user/management/warn_user",
        method: "post",
        data
    })
}

//解除充值限制
export const jiechupay = (data:any) => {
    return req({
        url: "/admin/user/management/cancelRechargeForbid",
        method: "post",
        data
    })
}