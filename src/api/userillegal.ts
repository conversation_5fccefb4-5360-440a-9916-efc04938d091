import req from "@/utils/request";

// 违规列表
export const illegal_api = (data:any) => {
  return req({
    url: "/admin/user/user_illegal/query_user_illegal",
    method: "post",
    data
  })
}
// 违规详情
export const illegalRecord_api = (data:any) => {
  return req({
    url: "/admin/user/user_illegal/query_user_illegal_record",
    method: "post",
    data
  })
}
// 封禁设备IP分页查询
export const queryBanned_api = (data: userViolation) => {
  return req({
    url: "/admin/banned/info/query_list",
    method: "post",
    data
  })
}
// 封禁设备IP删除
export const delBanned_api = (data: userViolation) => {
  return req({
    url: "/admin/banned/info/del",
    method: "post",
    data
  })
}

// 低质量女性用户列表
export const query_low_woman = (data: userViolation) => {
  return req({
    url: "/admin/user/low_quality_woman/query",
    method: "post",
    data
  })
}
// 忽略
export const ignore_low_woman = (data: userViolation) => {
  return req({
    url: "/admin/user/low_quality_woman/ignore",
    method: "post",
    data
  })
}

// 聊天列表
export const query_chat_list = (data: userViolation) => {
  return req({
    url: "/admin/user/chat_record/query_user_chat_record",
    method: "post",
    data
  })
}
export const query_retention = (data:any) => {
  return req({
    url: "/admin/pay/retention/man/list",
    method: "post",
    data,
  })
}
export const newquery_retention = (data:any) => {
  return req({
    url: "/admin/pay/retention/new/man/list",
    method: "post",
    data
  })
}

// 聊天列表左
export const query_record = (data:userViolation) => {
  return req({
    url: "/admin/user/chat_record/query_chat_record",
    method: "post",
    data
  })
}

// 注册留存列表
export const query_retention_list = (data:retention) => {
  return req({
    url: "/admin/register/retention/list",
    method: "post",
    data
  })
}
// 注册筛选
export const query_channel_list = () => {
  return req({
    url: "/admin/admin/common/channel/list",
    method: "post",
  })
}
// 注册筛选
export const query_payretention_list = (data:retention) => {
  return req({
    url: "/admin/pay/retention/list",
    method: "post",
    data
  })
}
// 注册筛选
export const backpack_used_record = (data:retention) => {
  return req({
    url: "/admin/admin/backpack_used_record/query_list",
    method: "post",
    data
  })
}
// 注册筛选
export const freeze_withdrawal_record_api = (data:any) => {
  return req({
    url: "/admin/admin/freeze_withdrawal_record/list",
    method: "post",
    data
  })
}

