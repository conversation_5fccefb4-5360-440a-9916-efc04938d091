import req from "@/utils/request";

// 查询用户邀请比例分页
export const queryWhitelist_api = (data:whitelist) => {
    return req({
        url: "/admin/user/invite_whitelist/query_whitelist",
        method: "post",
        data
    })
}
// 修改邀请比例
export const modifyWhitelist_api = (data:whitelist) => {
    return req({
        url: "/admin/user/invite_whitelist/modify_whitelist",
        method: "post",
        data
    })
}
// 新增用户邀请比例
export const addWhitelist_api = (data:whitelist) => {
    return req({
        url: "/admin/user/invite_whitelist/add_whitelist",
        method: "post",
        data
    })
}
// 删除用户邀请比例
export const delWhitelist_api = (data:whitelist) => {
    return req({
        url: "/admin/user/invite_whitelist/del_whitelist",
        method: "post",
        data
    })
}
// 分页查询邀请结算
export const querySettlement_api = (data:whitelist) => {
    return req({
        url: "/admin/user/invite_whitelist/query_settlement",
        method: "post",
        data
    })
}
// 分页查询邀请结算
export const modifySettlement_api = (data:whitelist) => {
    return req({
        url: "/admin/user/invite_whitelist/modify_settlement",
        method: "post",
        data
    })
}

// 分页查询邀请结算
export const queryNatureSettlement_api = (data:whitelist) => {
  return req({
      url: "/admin/user/invite_whitelist/query_nature_settlement",
      method: "post",
      data
  })
}
// 分页查询邀请结算
export const modifyNatureSettlement_api = (data:whitelist) => {
  return req({
      url: "/admin/user/invite_whitelist/modify_nature_settlement",
      method: "post",
      data
  })
}
//查看详细邀请
export const settlementDetails_api = (data:whitelist) => {
  return req({
      url: "/admin/user/invite_whitelist/settlement_details_page",
      method: "post",
      data
  })
}
//查看详细白名单
export const settlementNatureDetails_api = (data:whitelist) => {
  return req({
      url: "/admin/user/invite_whitelist/settlement_nature_details_page",
      method: "post",
      data
  })
}
//查看详细白名单
export const query_man_recharge_settlement_api = (data:whitelist) => {
  return req({
      url: "/admin/user/invite_whitelist/query_man_recharge_settlement",
      method: "post",
      data
  })
}
export const modify_man_recharge_settlement_api = (data:any) => {
  return req({
      url: "/admin/user/invite_whitelist/modify_man_recharge_settlement",
      method: "post",
      data
  })
}
export const queryInvite_whitelist_api = (data:any) => {
  return req({
      url: "/admin/user/invite_whitelist/query_man_recharge_settlement_detail",
      method: "post",
      data,
  })
}
export const query_income_activity_api = (data:any) => {
  return req({
      url: "/admin/operation/income_activity/query",
      method: "post",
      data,
  })
}

export const modify_income_activity_api = (data:any) => {
  return req({
      url: "/admin/operation/income_activity/modify",
      method: "post",
      data,
  })
}