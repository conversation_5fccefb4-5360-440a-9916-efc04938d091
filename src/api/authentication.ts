import req from "@/utils/request";

// 分页条件查询人脸认证
export const queryReal_api = (data:any) => {
    return req({
        url: "/admin/user_real/real_avatar/query_real_avatar_Page",
        method: "post",
        data
    })
}
// 审核编辑接口
export const modifyDay_api = (data:any) => {
    return req({
        url: "/admin/user_real/real_avatar/modify_real_avatar",
        method: "post",
        data
    })
}
// 查询实名认证分页
export const queryRealName_api = (data:authentication) => {
    return req({
        url: "/admin/user_real/real_name/query_real_name",
        method: "post",
        data
    })
}

// 查询实名认证分页
export const cancel_api = (data:any) => {
  return req({
      url: "/admin/user_real/real_name/cancel",
      method: "post",
      data
  })
}


export const getMatchmakerApplyInfoList = (data:any) => {
    return req({
        url: "/admin/matchmaker/getMatchmakerApplyInfoList",
        method: "post",
        data
    })
}

export const examineMatchmakerApplyInfo = (data:any) => {
    return req({
        url: "/admin/matchmaker/examineMatchmakerApplyInfo",
        method: "post",
        data
    })
}

export const updateRate = (data:any) => {
    return req({
        url: "/admin/matchmaker/updateRate",
        method: "post",
        data
    })
}
// 快递
export const query_kuaidi_list = (data:any) => {
  return req({
      url: "admin/admin/activity/query_task_list",
      method: "post",
      data
  })
}
// 发快递
export const query_fakuaidi_list = (data:any) => {
  return req({
      url: "admin/admin/activity/task_edit",
      method: "post",
      data
  })
}

//红娘群聊申请列表数据
export const getMatchmakerGroupList = (data:any) => {
    return req({
        url: "/admin/matchmaker/getMatchmakerGroupList",
        method: "post",
        data
    })
}

export const examineMatchmakerGroup = (data:any) => {
    return req({
        url: "/admin/matchmaker/examineMatchmakerGroup",
        method: "post",
        data
    })
}

export const modifyMatchmakerGroup = (data:any) => {
    return req({
        url: "/admin/matchmaker/modifyMatchmakerGroup",
        method: "post",
        data
    })
}
// 视频认证查询
export const query_ensure_video_api = (data:any) => {
    return req({
        url: "/admin/user/management/query_ensure_video",
        method: "post",
        data
    })
}

// 视频认证审核
export const update_ensure_video_api = (data:any) => {
    return req({
        url: "/admin/user/management/cancelEnsureVideo",
        method: "post",
        data
    })
}
