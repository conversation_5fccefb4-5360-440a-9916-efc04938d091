import req from "@/utils/request";

//同心锁
export const tongxinsuo_list_api = (data: any) => {
    return req({
        url: "/admin/user/invite_friends_detail/unlock_contact_record",
        method: "post",
        data
    })
}

//同心锁退款
export const tongxinsuo_tuikuan_api = (data: any) => {
    return req({
        url: "/admin/user/invite_friends_detail/handle_unlock",
        method: "post",
        data
    })
}

//充值套餐列表
export const golds_package_api = (data: any) => {
    return req({
        url: "/admin/journal_account/recharge/golds_package/v2",
        method: "post",
        data
    })
}

//添加充值套餐
export const add_package_api = (data: any) => {
    return req({
        url: "/admin/journal_account/recharge/add_package",
        method: "post",
        data
    })
}

//充值修改套餐
export const modify_package_api = (data: any) => {
    return req({
        url: "/admin/journal_account/recharge/modify_package",
        method: "post",
        data
    })
}

//充值删除套餐
export const del_package_api = (data: any) => {
    return req({
        url: "/admin/journal_account/recharge/del",
        method: "post",
        data
    })
}