import req from "@/utils/request";

// 分页查询钱包
export const queryGoldsPoints_api = (data: goldspoints) => {
  return req({
    url: "/admin/wallet_manage/golds_points/query",
    method: "post",
    data,
  });
};
// 修改积分
export const modifyUserWallet_api = (data: goldspoints) => {
  return req({
    url: "/admin/wallet_manage/golds_points/modify_user_wallet",
    method: "post",
    data,
  });
};
// 积分金币业务类型查询
export const queryBusinessType_api = () => {
  return req({
    url: "/admin/wallet_manage/golds_points/query_business_type",
    method: "post",
  });
};
// 积分金币业务类型查询
export const queryWallet_api = (data: any) => {
  return req({
    url: "/admin/give_manage/golds_points_free/query_user_give_record",
    method: "post",
    data,
  });
};

// 积分金币业务类型查询
export const queryfriendsList_api = (data: any) => {
  return req({
    url: "/admin/user/invite_friends_detail/query_invite_friends_detail",
    method: "post",
    data,
  });
};

// 积分金币业务类型查询
export const queryInvite_whitelist_api = (data: any) => {
  return req({
    url: "/admin/user/invite_whitelist/query_man_recharge_settlement_detail",
    method: "post",
    data,
  });
};

// 补发充值收益
export const reissue_recharge_income_api = (data: any) => {
  return req({
    url: "/admin/user/invite_friends_detail/reissue_recharge_income",
    method: "post",
    data,
  });
};

// 查看补发记录
export const reissue_recharge_record_api = (data: any) => {
  return req({
    url: "/admin/user/invite_friends_detail/reissue_recharge_record",
    method: "post",
    data,
  });
};

// 查看绑定记录
export const bind_applying_api = (data: any) => {
  return req({
    url: "/admin/user/invite_friends_detail/bind_applying",
    method: "post",
    data,
  });
};
// 处理绑定
export const handle_bind_applying_api = (data: any) => {
  return req({
    url: "/admin/user/invite_friends_detail/handle_bind_applying",
    method: "post",
    data,
  });
};


export const queryjbbdtjTotal_api = (data = {}) => {
    return req({
        url: "/admin/wallet_manage/golds_points/query_total",
        method: "post",
        data
    })
};
