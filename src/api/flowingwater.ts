import req from "@/utils/request";

// 查询用户金币流水分页
export const queryGolds_api = (data:flowingwater) => {
    return req({
        url: "/admin/journal_account/golds/query_golds_page",
        method: "post",
        data
    })
}
// 查询用户积分流水分页
export const queryPoints_api = (data:flowingwater) => {
    return req({
        url: "/admin/journal_account/points/query_points_page",
        method: "post",
        data
    })
}
// 充值流水
export const queryRecharge_api = (data:flowingwater) => {
    return req({
        url: "/admin/journal_account/recharge/query_recharge_page",
        method: "post",
        data
    })
}
// 提现查询
export const queryWithdrawal_api = (data:flowingwater) => {
    return req({
        url: "/admin/journal_account/withdrawal/query_withdrawal_page",
        method: "post",
        data
    })
}
// 提现审核
export const withdrawalAudit_api = (data:flowingwater) => {
    return req({
        url: "/admin/journal_account/withdrawal/withdrawal_audit",
        method: "post",
        data
    })
}
// 平台金币流水
export const queryPlatformGolds_api = (data:flowingwater) => {
    return req({
        url: "/admin/journal_account/platform_golds/query",
        method: "post",
        data
    })
}
// 平台积分流水
export const queryPlatformPoints_api = (data:flowingwater) => {
    return req({
        url: "/admin/journal_account/platform_points/query",
        method: "post",
        data
    })
}
//Excel金币
export const queryGoldsExecl_api = (data:flowingwater) => {
  return req({
      url: "/admin/journal_account/golds/query_golds_execl",
      method: "post",
      data
  })
}
//Excel积分
export const queryPointsExecl_api = (data:flowingwater) => {
  return req({
      url: "/admin/journal_account/points/query_points_execl",
      method: "post",
      data
  })
}
//Excel充值
export const queryreChargeExecl_api = (data:flowingwater) => {
  return req({
      url: "/admin/journal_account/recharge/query_recharge_execl",
      method: "post",
      data
  })
}
//Excel提现
export const queryreWithdrawal_Execl_api = (data:flowingwater) => {
  return req({
      url: "/admin/journal_account/withdrawal/query_withdrawal_execl",
      method: "post",
      data
  })
}

//查询提现异常
export const withdrawal_illegal_list = (data:any) => {
  return req({
      url: "/admin/admin/withdrawal_illegal/query_list",
      method: "post",
      data
  })
}


//查询提现异常
export const modify_withdrawal_illegal = (data:any) => {
  return req({
      url: "/admin/admin/withdrawal_illegal/modify",
      method: "post",
      data
  })
}


export const queryWithdrawalTotal_api = (data = {}) => {
    return req({
        url: "/admin/journal_account/withdrawal/query_withdrawal_total",
        method: "post",
        data
    })
}


export const queryrechargeTotal_api = (data = {}) => {
    return req({
        url: "/admin/journal_account/recharge/query_recharge_total",
        method: "post",
        data
    })
}

export const querypointsTotal_api = (data = {}) => {
    return req({
        url: "/admin/journal_account/points/query_points_total",
        method: "post",
        data
    })
}
