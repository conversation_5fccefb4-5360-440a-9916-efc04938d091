<template>
  <div style="height: 100%; width: 100%">
    <el-auto-resizer>
      <template #default="{ height, width }">
        <el-table-v2
          v-loading="Loading"
          :columns="columns"
          :data="tabData"
          :width="width"
          :height="height"
          :element-loading-text="elementLoadingText"
          fixed
        />
      </template>
    </el-auto-resizer>
  </div>
</template>

<script setup >
import { toRefs } from "vue";

let getData = defineProps({
  tabData: {
    type: Array,
  },
  tabHeader: {
    type: Object,
  },
  Loading: {
    type: Boolean,
  },
  elementLoadingText:{
    type: String,
  }
});

let { tabData, tabHeader } = toRefs(getData);
const generateColumns = () =>
  Object.entries(tabHeader.value).map((item) => {
    return {
      key: `${item[0]}`,
      dataKey: `${item[0]}`,
      title: `${item[1].title}`,
      width: 180,
    };
  });
const columns = generateColumns();
</script>

<style scoped></style>
