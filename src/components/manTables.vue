<template>
  <el-table ref="uselistRef" :data="tabData" style="width: 100%" v-loading="Loading" height="748" highlight-current-row stripe>
    <template v-for="v in tabHeader" :key="v.prop">
      <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
        <template slot="header" #header="_scope">
            <span>{{ v.title }}</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="v.showTooltip"
              placement="top-start"
            >
              <el-icon v-if="v.showTool">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
        <template #default="scope">
          <span @click="handleClick(v,scope.row)" :style="v.prop==='connectedNum'||v.prop==='sendLetterPeopleNum'?'cursor: pointer;':''">{{ scope.row[v.prop] }}</span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>

<script setup lang="ts">
import { toRefs } from "vue";

let getData = defineProps({
  tabData: {
    type: Array,
  },
  tabHeader: {
    type: Object,
  },
  Loading: {
    type: Boolean,
  },
});

const emits =defineEmits(['handleShowPeople'])

let { tabData, tabHeader } = toRefs(getData);

const handleClick =(e: any,row:any)=>{
 emits("handleShowPeople",e,row)
  
}
</script>

<style scoped></style>
