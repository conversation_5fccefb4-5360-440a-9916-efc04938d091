<template>
  <el-radio-group
    :model-value="applicationId"
    placeholder="请选择"
    style="width: 320px;"
    @change="handleChange"
    :disabled="disabled || loading"
  >

<!--    <el-radio-button label="com.dongxin.app">本地遇爱</el-radio-button>-->
<!--    <el-radio-button label="com.dingkunianwo.app">念我</el-radio-button>-->
    <el-radio-button
        v-for="app in appOptions"
        :key="app.uriname"
        :label="app.uriname"
    >
      {{ app.name }}
    </el-radio-button>
  </el-radio-group>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { query_permissions_api } from "@/api/user";

const appOptions = ref([]);
const loading = ref(false);

const props = defineProps({
  applicationId: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  skipFirstSelect: {
    type: Boolean,
    default: false,
  }
});

const emits = defineEmits(['changeSelect']);

const handleChange = (e) => {
  // 如果选择的是"全部"选项，传递空字符串
  const valueToEmit = e === 'com.all.app' ? '' : e;
  emits('changeSelect', valueToEmit);
};

onMounted(async () => {
  try {
    loading.value = true;
    const res = await query_permissions_api({ id: 1 });
    appOptions.value = res.data.data.filter(app => app.uriname.includes("com."));

    if (!props.skipFirstSelect && !props.applicationId && appOptions.value.length > 0) {
      emits('changeSelect', appOptions.value[0].uriname);
    }
  } catch (error) {
    console.error('获取应用列表失败:', error);
  } finally {
    loading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.el-radio-group {
font-size: 20px;
}
</style>
