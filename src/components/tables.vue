<template>
  <el-table ref="uselistRef" :data="tabData" style="width: 100%; height: 100%" v-loading="Loading" highlight-current-row stripe>
    <template v-for="v in tabHeader" :key="v.prop">
      <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
        <template #default="scope">
          <span>{{ scope.row[v.prop] }}</span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>

<script setup lang="ts">
import { toRefs } from "vue";

let getData = defineProps({
  tabData: {
    type: Array,
  },
  tabHeader: {
    type: Object,
  },
  Loading: {
    type: Boolean,
  },
});

let { tabData, tabHeader } = toRefs(getData);

</script>

<style scoped></style>
