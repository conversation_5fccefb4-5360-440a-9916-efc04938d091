<template>
  <div>
    <el-upload class="avatar-uploader"  @change="getFile"  :auto-upload="false"   :show-file-list="false">
      <img v-if="imageUrl" :src="imageUrl" class="avatar" />
      <el-icon v-else class="avatar-uploader-icon">
        <Plus />
      </el-icon>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import {reactive } from 'vue'
import axios from 'axios';
import COS from 'cos-js-sdk-v5'
import { Plus } from "@element-plus/icons-vue";
 defineProps({
  imageUrl: {
    type: String,
  },
  handleImagUrl:{
    type:Function
  }

})

const emit = defineEmits(['handleImagUrl'])
let Cos = reactive<any>({
  tmpSecretId: "",
  tmpSecretKey: "",
  sessionToken: "",
  createTime: null,
  expireTime: null
})

const cos = new COS({
  getAuthorization: (_options, callback) => {
    const obj: any = {
      TmpSecretId: Cos.tmpSecretId,
      TmpSecretKey: Cos.tmpSecretKey,
      XCosSecurityToken: Cos.sessionToken,
      StartTime: Cos.createTime, // 开始时间戳，单位秒
      ExpiredTime: Cos.expireTime // 过期时间戳，单位秒
    }
    callback(obj)
  }
})

const getCos = () => {
  axios.post('https://api.gxdkkj.xyz/uaa/access_credentials').then((res: any) => {
    const { data } = res.data
    Cos.tmpSecretId = data.tmpSecretId
    Cos.tmpSecretKey = data.tmpSecretKey
    Cos.sessionToken = data.sessionToken
    Cos.createTime = data.createTime
    Cos.expireTime = data.expireTime
  })
}
getCos()

// 上传图片
const getFile = (e: any) => {
 handleFileInUploading(e)
}

const handleFileInUploading = (file: any) => {
  cos.putObject({
    Bucket: 'prod-1309639790', /* 填写自己的 bucket，必须字段 */
    Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
    Key: 'banner/' + file.name,              /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */
    StorageClass: 'STANDARD', //上传模式，标准
    Body: file.raw, // 上传文件对象
    // SliceSize: 1024 * 1024 * 5,     /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */
    onProgress: function (_progressData: any) {

    }
  }, function (err: any, data: any) {
    if (err) {
      console.log('上传失败', err);
    } else {
      emit('handleImagUrl',`https://${data.Location}`);
    }
  });
}

</script>
<style >
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}
.avatar{
  width: 98px;
  height: 98px;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
}
</style>