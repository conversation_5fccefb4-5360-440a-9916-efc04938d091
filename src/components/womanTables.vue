<template>
  <el-table ref="uselistRef" :data="tabData" style="width: 100%" v-loading="Loading" height="748" highlight-current-row stripe>
    <template v-for="v in tabHeader" :key="v.prop">
      <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
        <template slot="header" #header="_scope">
            <span>{{ v.title }}</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="v.showTooltip"
              placement="top-start"
            >
              <el-icon v-if="v.showTool">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
        <template #default="scope">
          <div v-if="v.isCustom">
          <p>  分子:&nbsp;{{ scope.row.replyMaleMsgCntLess3Minutes }}</p>
          <p>  分母:&nbsp;{{ scope.row.replyMaleMsgCnt }}</p>
          <p>  回复率:&nbsp;{{scope.row.replyMaleMsgCnt!=0? (scope.row.replyMaleMsgCntLess3Minutes/scope.row.replyMaleMsgCnt*100).toFixed(2):0.00}}%</p>
          </div>
          <div v-else-if="v.isreplyReplyMoleculeRate">
          <p>  分子:&nbsp;{{ scope.row.replyReplyMolecule }}</p>
          <p>  分母:&nbsp;{{ scope.row.replyReplyDenominator }}</p>
          <p>  回复率:{{ scope.row[v.prop] }}</p>
          </div>
          <span v-else>{{ scope.row[v.prop] }}</span>
        </template>
      </el-table-column>
    </template>
  </el-table>
</template>

<script setup lang="ts">
import { toRefs } from "vue";

let getData = defineProps({
  tabData: {
    type: Array,
  },
  tabHeader: {
    type: Object,
  },
  Loading: {
    type: Boolean,
  },
});

let { tabData, tabHeader } = toRefs(getData);

</script>

<style scoped></style>
