<template>
    <svg class="icon" :class="size" :style="{fill:color}" aria-hidden="true">
      <use :xlink:href="'#icon-' + name"></use>
    </svg>
</template>

<script setup lang="ts">
interface icon {
    size?:string,
    color?:string,
    name?:string
}

withDefaults(defineProps<icon>(),{
    size:"sm",
    color:"#ccc",
    name:"data"
})
</script>

<style scoped lang="scss">
.sm{
    width: 20px;
    height: 20px;
}

.vd{
    width: 24px;
    height: 24px;
}
</style>
