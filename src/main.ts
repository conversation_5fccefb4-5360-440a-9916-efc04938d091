import { createApp } from 'vue'
import './assets/css/style.css'
import App from './App.vue'
import router from '@/router' // router
import { createPinia } from 'pinia' // pinia
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import * as ECharts from 'echarts' // echarts
import "@/assets/fonts/iconfont.js" // 引入fonts文件
import "./permission"
const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(router)
app.use(createPinia())
app.use(ElementPlus, {
  locale: zhCn,
})
app.config.globalProperties.$ECharts = ECharts
app.mount('#app')
