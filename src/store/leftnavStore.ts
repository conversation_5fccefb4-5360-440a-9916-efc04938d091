import { defineStore } from "pinia"
import { onMounted, onUnmounted, ref } from "vue"

const leftnavStore = defineStore("leftnavStore", () => {
    let w = ref(200)
    let isCollapse = ref(false)

    let wFn = () => {
        // 获取屏幕宽度
        let WIDTH = document.body.clientWidth
        WIDTH < 1000 ? isCollapse.value = true : isCollapse.value = false
        isCollapse.value ? w.value = 64 : w.value = 200
    }

    let onIsCollapse = () => {
        isCollapse.value = !isCollapse.value
        isCollapse.value ? w.value = 64 : w.value = 200
    }

        onMounted(()=>{
            window.addEventListener('resize', wFn)
        })

        onUnmounted(()=>{
            window.removeEventListener('resize', wFn)
        })

    return {
        w,
        isCollapse,
        onIsCollapse,
        wFn,
    }
})

export default leftnavStore