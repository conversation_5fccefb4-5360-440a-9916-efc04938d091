// // 动态路由
// import { defineStore } from "pinia"
// import userStore from "./userStore" //引入用户仓库
// import router, { asyncroutes } from "@/router"
// import { ref } from "vue"

// let permissionStore = defineStore('permissionStore',()=>{
//     // 实例化用户仓库角色
//     let { roles } = userStore()
//     // 定义处理的要显示的数组
//     let meunsArr = ref([])
//     //处理单个路由对象是否拥有权限
//     let hasRole = (route:any,role:any)=>{
//         if(route.meta && route.meta.roles) { //判断有没有权限，有返回true,没有返回false
//             return route.meta.roles.includes(role)
//         }else{
//             // 不需要权限都可以访问  不需要权限的都返回true
//             return true
//         }
//     }
    
//     //处理异步路由数组 把所有路由数组中带有权限的路由过滤出来
//     let filterRouters = (routes: any, role: any)=>{
//         // 定义空数组
//         let arr = []
//         arr = routes.filter((v:any)=>{
//             // 一级路由判断
//             if (hasRole(v, role)){
//                 // 单个路由对象 有包含当前角色的权限
//                 if(v.children){
//                      // 使用递归过滤路由
//                     v.children = filterRouters(v.children,role)
//                 }
//                 return true
//             }else{
//                 // 如果不包含角色, 就不过滤. 直接return false
//                 return false
//             }
//         })
//         return arr
//     }
//     // 调用函数
//     filterRouters(asyncroutes,roles)

//     filterRouters(asyncroutes,roles).forEach((item:any) => {
//         router.addRoute(item)
//     })
    
//     //把需要展示的路由拿出来 把所有isshow为true的数据拿出来
//     let menus = ()=>{
//         return filterRouters(asyncroutes, roles).filter((v:any)=>v.isShow)
//     }

//     // 处理好的要显示的路由数组
//     meunsArr.value = menus();

//     return {
//         meunsArr, //处理好的路由数组
//     }

// })

// export default permissionStore