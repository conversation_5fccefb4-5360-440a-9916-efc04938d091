import { defineStore } from "pinia"
import { login_api } from "@/api/user"; //api接口
import { useRouter } from "vue-router"; //全局路由
import local from "@/utils/local"; //本地存储
import { ElMessage } from 'element-plus'
import { ref } from "vue";

const userStore = defineStore("userStore", () => {
    let $router = useRouter()
    let t_k = ref(local.get("t_k")) //token
    let roles = ref(local.get("role")) //用户角色
    let userid = ref(local.get("id")) //用户id
    let person = ref(local.get("info")) //个人信息
    let catalogueNames = ref<any>(local.get("menu"))
    // 登录
    let login_store = async (loginForm: any) => {
        let res = await login_api({ ...loginForm })
        let { code, data } = res.data

        if (code === 200) {
            // 存储到本地
            local.set('role', data.roleNames)
            local.set('t_k', data.token)
            local.set('menu', data.catalogueNames)
            local.set('id', data.id)
            local.set('info', data.account)
            
            // 存储到仓库
            t_k.value = data.token
            roles.value = data.roleNames
            catalogueNames.value = data.catalogueNames
            userid.value = data.id
            person.value = data.account
            // 跳转到首页
            setTimeout(() => {
                $router.push("/dashboard/home")
                window.location.reload()
                ElMessage({
                    message: '欢迎登录',
                    type: 'success',
                })
            }, 300)
        }
    }

    // 清空存储
    let clear_store = ()=>{
        local.clear();
        t_k.value = "";
        roles.value = "";
        catalogueNames.value = "";
        userid.value = "";
    }


    return {
        t_k,
        roles,
        login_store,
        clear_store,
        catalogueNames,
        userid,
        person
    }
})

export default userStore