import { defineStore } from "pinia"
import userStore from "./userStore"
import local from "@/utils/local"
import router from "@/router"
import { ref } from "vue"
import Layout from "@/views/layout/index.vue"

// 图标映射函数 - 为每个菜单分配独特的图标
const getIconByTitle = (title: string, icon?: string): { icon: string; iconType: string } => {
    // 如果已有图标配置，直接使用
    if (icon && icon.trim()) {
        return { icon, iconType: 'svg' };
    }

    // 为每个菜单分配独特的Element Plus图标
    const iconMap: { [key: string]: string } = {
        // 用户权限管理类
        '后台用户权限管理': 'UserFilled',
        '用户管理': 'User',
        '账号管理': 'Avatar',
        '红娘管理': 'Female',

        // 安全审核类
        '内容安全': 'Shield',
        '认证管理': 'Checked',

        // 数据统计类
        '时段统计': 'TrendCharts',
        '日统计': 'DataAnalysis',
        '数据详情报表': 'DataBoard',
        '交友数据': 'Connection',

        // 财务管理类
        '流水': 'List',
        '钱包管理': 'Wallet',
        '充值提现': 'CreditCard',
        '礼物管理': 'Present',

        // 功能管理类
        '签到管理': 'Calendar',
        '邀请好友': 'Share',
        '房间管理': 'House',
        '渠道管理': 'Guide',

        // 运营管理类
        '运营管理': 'Operation',
        '活动管理': 'Trophy',
        '任务管理': 'DocumentChecked',
        'app包管理': 'Box',

        // 看板类
        '亲密度看板': 'Histogram',
        '亲密度礼包看板': 'PieChart',
        '道具看板': 'Grid'
    };

    // 查找匹配的图标
    if (iconMap[title]) {
        return { icon: iconMap[title], iconType: 'element' };
    }

    // 默认使用自定义图标
    return { icon: 'guanli', iconType: 'svg' };
}
const routerStore = defineStore("routerStore", () => {
    let modules = import.meta.glob('../views/**/*.vue')
    let { catalogueNames } = userStore()
    let meunsArrs = ref([])
    let pathArrs = ref<any>([])
    let route = () => {
        if (!local.get("menu")) return
        let meunsArr = []
        let pathArr: any[] = []
        meunsArr = catalogueNames.map((item: any) => {
            // 子路由数组
            let children = item.menuRanks.map((v: any) => {
              pathArr.push(`${item.path}/${v.menuPath}`);
                return {
                    path: v.menuPath,
                    meta: { title: v.menuTitle, path: `${item.path}/${v.menuPath}`},
                    component: modules[`../views/${v.menuComponent}.vue`]
                }
            })
            // 获取图标信息
            const iconInfo = getIconByTitle(item.title, item.icon);

            // 处理后的路由数组
            const currentRouter = {
                path: item.path,
                name: item.path.substring(1),
                component: Layout,
                redirect: item.redirectionUrl,
                meta: {
                    title: item.title,
                    icon: iconInfo.icon,
                    iconType: iconInfo.iconType
                },
                children
            }
            return currentRouter
        })

        meunsArr.forEach((item: any) => {
            router.addRoute(item)
        });
        meunsArrs.value = meunsArr
        pathArrs.value=pathArr
        // console.log(router.getRoutes ());
        
    }
    route()
    return {
        route,
        meunsArrs,
        pathArrs
    }
})

export default routerStore