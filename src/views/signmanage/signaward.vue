<template>
  <div class="signaward">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button @click="addSign()" :icon="Plus">添加</el-button>
          <el-form ref="queryFormRef" :inline="true" :model="queryForm">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select class="header-select" v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="正常" :value="0" />
                <el-option label="删除" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="signTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" min-width="60" fixed></el-table-column>
        <el-table-column prop="applicationId" label="马甲包" min-width="80" ></el-table-column>
        <el-table-column prop="days" label="天数" min-width="120"></el-table-column>
        <el-table-column prop="awardId" label="奖励物品ID" min-width="120"></el-table-column>
        <el-table-column prop="gender" label="性别" min-width="120">
          <template #default="scope">
            {{ scope.row.gender == 1 ? "男":  scope.row.gender == 2?"女":'' }}
          </template>

        </el-table-column>
        <el-table-column prop="awardNum" label="奖励物品数量" min-width="120"></el-table-column>
        <el-table-column prop="awardType" label="奖励物品类型" min-width="120">
          <template #default="scope">
            {{ scope.row.awardType == 0 ? "礼物" :scope.row.awardType ==1?"金币":scope.row.awardType == 2?"打招呼":scope.row.awardType == 3?"视频体验卡":scope.row.awardType == 4?'魅力值' :'消息卡'}}
          </template>
        </el-table-column>
        <el-table-column prop="awardResourceUrl" label="奖励图片" min-width="120">
          <template #default="scope">
            <el-avatar shape="square" :size="80" fit="cover" :src="scope.row.awardResourceUrl" />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="120">
          <template #default="scope">
            {{ scope.row.status == 0 ? "正常" : "删除" }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="120"></el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template #default="scope">
            <el-button @click="editSign(scope.row)" size="small" type="primary" :icon="Edit" />
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      <el-dialog v-model="isShow" title="编辑奖励">
        <el-form :model="editForm">
          <el-form-item label="ID" v-show="addShow" prop="id">
            <el-input v-model="editForm.id" disabled />
          </el-form-item>
          <el-form-item label="马甲包">
              <majaSelect :applicationId="editForm.applicationId" @changeSelect="changeSelect1" :disabled="addShow"/>
            </el-form-item>
          <el-form-item label="天数" prop="days">
            <el-input v-model="editForm.days" />
          </el-form-item>
          <el-form-item label="奖励物品ID" prop="awardId">
            <el-input v-model="editForm.awardId" />
          </el-form-item>
          <el-form-item label="物品数量" prop="awardNum">
            <el-input v-model="editForm.awardNum" />
          </el-form-item>
          <el-form-item label="性别" prop="gender">
            <el-select  v-model="editForm.gender">
              <el-option label="男" :value="1" />
              <el-option label="女" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="物品类型" prop="awardType">
            <el-select class="header-select" v-model="editForm.awardType" placeholder="请选择">
              <el-option label="礼物" :value="0" />
              <el-option label="金币" :value="1" />
              <el-option label="打招呼" :value="2" />
              <el-option label="视频体验卡" :value="3" />
              <el-option label="魅力值" :value="4" />
              <el-option label="消息卡" :value="5" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select class="header-select" v-model="editForm.status" placeholder="请选择">
              <el-option label="正常" :value="0" />
              <el-option label="删除" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="奖励图片" prop="picUrl">
            <div class="avatar-uploader" @click="upload">
              <input type="file" @change="getFile" ref="upImg" class="upImg">
              <img v-if="editForm.awardResourceUrl" :src="editForm.awardResourceUrl" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </div>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model="editForm.description" autocomplete="off" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isShow = false">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import COS from 'cos-js-sdk-v5'
import axios from 'axios'
import pageHook from "@/hooks/pageHook";
import { Edit, Plus } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { querySignAward_api, modifySignAward_api, addSignAward_api } from "@/api/signmanage"
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(querySubmit);
const queryFormRef = ref<FormInstance>()
let signTab = ref([])
let isShow = ref(false)
let addShow = ref(true)
let editForm = reactive<any>({
  id: null,
  days: null,
  awardId: null,
  awardNum: null,
  awardType: null,
  gender:null,
  status: null,
  description: null,
  awardResourceUrl: null
})
let Cos = reactive<any>({
  tmpSecretId: "",
  tmpSecretKey: "",
  sessionToken: "",
  createTime: null,
  expireTime: null
})
let opNull = ref<any>(null)
let queryForm = reactive<any>({
  status: null,
  applicationId:'com.dongxin.app'
})
let upImg = ref<any>(null)
const getCos = () => {
  axios.post('https://api.gxdkkj.xyz/uaa/access_credentials').then((res: any) => {
    const { data } = res.data
    Cos.tmpSecretId = data.tmpSecretId
    Cos.tmpSecretKey = data.tmpSecretKey
    Cos.sessionToken = data.sessionToken
    Cos.createTime = data.createTime
    Cos.expireTime = data.expireTime
  })
}
// StartTime: Cos.createTime, // 时间戳，单位秒，如：1580000000
// ExpiredTime: Cos.expireTime, // 时间戳，单位秒，如：1580000000
getCos()
const cos = new COS({
  getAuthorization: (_options, callback) => {
    const obj: any = {
      TmpSecretId: Cos.tmpSecretId,
      TmpSecretKey: Cos.tmpSecretKey,
      XCosSecurityToken: Cos.sessionToken,
      StartTime: Cos.createTime, // 开始时间戳，单位秒
      ExpiredTime: Cos.expireTime // 过期时间戳，单位秒
    }
    callback(obj)
  }
})

async function querySignAward() {
  let res = await querySignAward_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId
  })
  let { data, total } = res.data
  signTab.value = data
  totalNum.value = total
}
querySignAward()

// 操作
const editSign = (row: any) => {
  editForm.id = row.id;
  editForm.days = row.days;
  editForm.awardId = row.awardId;
  editForm.awardNum = row.awardNum;
  editForm.awardType = row.awardType;
  editForm.status = row.status;
  editForm.gender = row.gender;
  editForm.description = row.description;
  editForm.applicationId=row.editForm;
  editForm.awardResourceUrl = row.awardResourceUrl;
  addShow.value = true
  isShow.value = true
}
const editSubMit = async () => {
  if (addShow.value == true) {
    await modifySignAward_api({ ...editForm })
    isShow.value = false
    querySignAward()
  } else {
    let { days, awardId, awardNum, awardType, status, description, awardResourceUrl,gender,applicationId } = editForm
    await addSignAward_api({
      days,
      awardId,
      awardNum,
      awardType,
      status,
      gender,
      description,
      awardResourceUrl,
      applicationId
    })
    isShow.value = false
    querySignAward()
  }
}

// 上传图片
const getFile = (e: any) => {
  const file = e.target.files[0];
  handleFileInUploading(file)
}

const upload = () => {
  upImg.value?.click()
}

// cos上传
const handleFileInUploading = (file: any) => {
  cos.putObject({
    Bucket: 'prod-1309639790', /* 填写自己的 bucket，必须字段 */
    Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
    Key: 'gifts/' + file.name,              /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */
    StorageClass: 'STANDARD', //上传模式，标准
    Body: file, // 上传文件对象
    // SliceSize: 1024 * 1024 * 5,     /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */
    onProgress: function (progressData) {
      console.log(JSON.stringify(progressData));
    }
  }, function (err, data) {
    if (err) {
      console.log('上传失败', err);
    } else {
      editForm.awardResourceUrl = `https://${data.Location}`
      // console.log(`https://${data.Location}`);
      // console.log('上传成功', data);
    }
  });
}





const addSign = () => {
  editForm.days = null;
  editForm.awardId = null;
  editForm.awardNum = null;
  editForm.awardType = null;
  editForm.status = null;
  editForm.gender = null;
  editForm.description = null;
  editForm.awardResourceUrl = null;
  editForm.applicationId='com.dongxin.app'
  addShow.value = false
  isShow.value = true
}

// 查询
async function querySubmit() {
  let res = await querySignAward_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let { data, total } = res.data
  signTab.value = data
  totalNum.value = total
  // console.log(res);
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  querySignAward()
}
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  querySubmit();
}
const changeSelect1=(e:string)=>{
  editForm.applicationId=e;
}
</script>

<style lang="scss" scoped>
.signaward {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    display: flex;
    align-items: center;

    .el-form {
      height: 32px;
      margin-left: 16px;
    }
  }

  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }


      .el-form-item__label {
        width: 85px;
      }
    }
  }

  .avatar-uploader {
    width: 100px;
    height: 100px;
    box-sizing: border-box;
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }

    .upImg {
      display: none;
    }


    img {
      width: 100px;
      height: 100px;
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }
  }



  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    text-align: center;
  }
}
</style>