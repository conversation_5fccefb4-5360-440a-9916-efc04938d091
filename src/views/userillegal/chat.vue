<template>
  <div class="chat-page">
    <el-card class="box-card">
    <template #header>
      <el-form ref="queryFormRef" :model="queryForm" :inline="true">
        <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
        </el-form-item>
        <el-form-item label="发送方ID" prop="fromUserId">
          <el-input v-model="queryForm.fromUserId" />
        </el-form-item>
        <el-form-item label="接受方ID" prop="toUserId">
          <el-input v-model="queryForm.toUserId" />
        </el-form-item>
        <el-form-item label="排序" prop="type">
          <el-select v-model="queryForm.type" placeholder="请选择">
            <el-option label="消息数量" :value="1" />
            <el-option label="亲密度" :value="2" />
            <el-option label="最新消息" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间选择">
          <el-date-picker
            v-model="queryForm.date"
            :clearable="false"
            type="daterange"
            unlink-panels
            value-format="x"
            format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="shortcuts"
            size="default"
            @change="timeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleGetList">查询</el-button>
          <el-button @click="onreset()">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <div class="chat-container">
      <div class="chat">
        <div class="chat-content">
          <div class="chat-left">
          <div
            class="chat-left-item"
            v-for="(item, index) in leftList"
            :key="index"
            :class="item.fromUserId == activeId && activeId ? 'active' : ''"
            @click="handelmenu(item)"
          >
            <img :src="item.fromAvatar" />
            <div class="chat-left-item-context">
              <div class="chat-left-item-context-name">
                {{ item.fromUserName }}
                <span>{{ item.fromUserId }}</span>
              </div>
            </div>
            <p class="chat-left-item-context-text" v-if="item.intimacyVal">
              亲密度 ：{{ item.intimacyVal }}
            </p>
          </div>
          </div>
          <div class="chat-main">
          <div class="chat-main-context">
            <div
              style="padding: 10px 20px"
              v-for="(item, index) in chatList"
              :key="index"
            >
              <template v-if="item.toUserId">
                <div class="chat-main-context-time">
                  <span>{{ item.toUserId }} </span> {{ time(item.msgTime) }}
                </div>
                <div class="message received">
                  <img :src="item.toAvatar" />
                  <div class="text" v-if="item.imMsgType === 1 || item.imMsgType === 2">
                    {{ item.content }}
                  </div>
                  <audio controls v-else-if="item.imMsgType === 3">
                    <source :src="item.content" type="audio/mp4" />
                    您的浏览器不支持播放此音频文件。
                  </audio>
                  <el-image
                    v-else-if="item.imMsgType === 4"
                    :src="item.content"
                    style="width: 200px; height: 200px; border-radius: 0"
                    :preview-src-list="avatarList"
                    :initial-index="0"
                    fit="cover"
                    :preview-teleported="true"
                    :hide-on-click-modal="true"
                    @click="resAvatar(item.content)"
                  />
                  <video
                    v-else-if="item.imMsgType === 5"
                    width="200"
                    height="200"
                    controls
                  >
                    <source :src="item.content" type="video/mp4" />
                    您的浏览器不支持播放此视频文件。
                  </video>
                  <el-icon style="color: red;"  v-if="item.status&&item.status!==200 ">
                  <Warning />
                </el-icon>
                <p style="color: #7a7674;"  v-if="item.status&&item.status!==200" >{{item.errorDesc}}</p>
                </div>
              </template>
              <template v-if="item.fromUserId">
                <div class="chat-main-context-time2">
                  {{ time(item.msgTime)
                  }}<span style="padding-left: 5px"> {{ item.fromUserId }} </span>
                </div>

                <div class="message sent">
                  <el-icon style="color: red;"  v-if="item.status&&item.status!==200 " >
                  <Warning />
                </el-icon>
                <p style="color: #7a7674;"  v-if="item.status&&item.status!==200 " >{{item.errorDesc}}</p>
                  <div class="text2" v-if="item.imMsgType === 1 || item.imMsgType === 2">
                    {{ item.content }}
                  </div>
                  <audio controls v-if="item.imMsgType === 3">
                    <source :src="item.content" type="audio/mp4" />
                    您的浏览器不支持播放此音频文件。
                  </audio>
                  <el-image
                    v-else-if="item.imMsgType === 4"
                    :src="item.content"
                    style="width: 200px; height: 200px; border-radius: 0"
                    :preview-src-list="avatarList"
                    :initial-index="0"
                    fit="cover"
                    :preview-teleported="true"
                    :hide-on-click-modal="true"
                    @click="resAvatar(item.content)"
                  />
                  <video
                    v-else-if="item.imMsgType === 5"
                    width="200"
                    height="200"
                    controls
                  >
                    <source :src="item.content" type="video/mp4" />
                    您的浏览器不支持播放此视频文件。
                  </video>
                  <img :src="item.fromAvatar" />
                </div>
              </template>
            </div>
          </div>
          </div>
        </div>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 70]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import pageHook from "@/hooks/pageHook";
import type { FormInstance } from "element-plus";
import { query_chat_list, query_record } from "@/api/userillegal";
import { time } from "@/utils/date";
import { ref, reactive } from "vue";
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
let { totalNum, currentPage, handleSizeChange, handleCurrentChange } = pageHook(
  handleGetPageList
);
const queryFormRef = ref<FormInstance>();
let toData = new Date(new Date().toLocaleDateString()).getTime();
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
let leftList = ref<any>();
let activeId = ref<any>();
let avatarList = ref<any>([]);
let queryForm = reactive<any>({
  fromUserId: null,
  toUserId: null,
  type: 1,
  applicationId:'com.dongxin.app',
  startTime: past7daysStart / 1000,
  date: [new Date(past7daysStart), new Date(todayEnd)],
  endTime: (todayEnd + 86400000) / 1000 + 0.001,
});
let chatList = ref<any>([]);
let pageSize = ref<any>(20);
async function handleGetPageList() {
  let res = await query_chat_list({
    ...queryForm,
    endTime: (todayEnd + 86400000) / 1000 + 0.001,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  chatList.value = data;
  totalNum.value = total;
}
async function handleGetList() {
  let res = await query_chat_list({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let dataList = await query_record({
    ...queryForm,
    pageSize: 2000,
    type: queryForm.type,
  });
  leftList.value = dataList.data.data;
  let { data, total } = res.data;
  dataList.data.data.map((item: any) => {
    if (queryForm.toUserId == item.fromUserId) {
      activeId.value = queryForm.toUserId;
    }
  });
  chatList.value = data;
  totalNum.value = total;
}

const timeChange = (data: any) => {
  queryForm.startTime = data[0] / 1000;
  queryForm.endTime = (data[1] + 86400000) / 1000;
};
const onreset = () => {
  queryForm.fromUserId = null;
  queryForm.toUserId = null;
  activeId.value = null;
  queryForm.startTime = past7daysStart / 1000;
  queryForm.date = [new Date(past7daysStart), new Date(todayEnd)];
  queryForm.endTime = todayEnd / 1000 + 0.001;
  handleGetList();
};
const resAvatar = (e: any) => {
  avatarList.value = [];
  if (avatarList.value.length == 0) {
    avatarList.value.push(e);
  }
};

const handelmenu = async (e: any) => {
  queryForm.toUserId = `${e.fromUserId}`;
  if (activeId.value == e.fromUserId) {
    return;
  }
  activeId.value = e.fromUserId;
  let res = await query_chat_list({
    fromUserId: queryForm.fromUserId,
    toUserId: e.fromUserId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    endTime: queryForm.endTime,
    startTime: queryForm.startTime,
  });
  let { data, total } = res.data;
  chatList.value = data;
  totalNum.value = total;
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  handleGetList();
}
</script>

<style lang="scss" scoped>
.chat-page {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .chat-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }
}

.chat {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  overflow: hidden;

  &-content {
    display: flex;
    flex: 1;
    width: 100%;
    overflow: hidden;
  }
  ::-webkit-scrollbar {
    width: 6px;
  }

  /* 滚动槽 */
  ::-webkit-scrollbar-track {
    border-radius: 10px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    border-radius: inherit;
    background-color: rgba(56, 70, 83, 0.3);
    -webkit-transition: 0.3s background-color;
    transition: 0.3s background-color;
  }

  &-left {
    width: 300px;
    flex: 0 0 auto;
    background: #e9e7e6;
    margin-bottom: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 100%;

    &-item {
      width: 100%;
      height: 60px;
      padding: 10px 0px 0px 10px;
      cursor: pointer;

      img {
        width: 50px;
        height: 50px;
        float: left;
        padding-right: 10px;
      }

      &-context {
        padding: 0 15px;
        font-size: 12px;

        &-name {
          font-size: 15px;
          display: flex;
          justify-content: space-between;
        }

        &-text {
          margin-top: 20px;
          width: 170px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    &-item:hover {
      background: #ededed;
    }

    .active {
      background: #c4c4c4;
    }
  }

  &-main {
    width: 1000px;
    background: #f5f5f5;
    position: relative;
    margin-bottom: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;

    ::-webkit-scrollbar {
      width: 6px;
    }

    /* 滚动槽 */
    ::-webkit-scrollbar-track {
      border-radius: 10px;
    }

    /* 滚动条滑块 */
    ::-webkit-scrollbar-thumb {
      border-radius: inherit;
      background-color: rgba(56, 70, 83, 0.3);
      -webkit-transition: 0.3s background-color;
      transition: 0.3s background-color;
    }

    &-context {
      overflow-y: auto;
      flex: 1;

      &-time {
        max-height: 20px;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        color: #969292;
        // background: #dadada;
        margin-bottom: 5px;
      }

      &-time2 {
        text-align: right;
        max-height: 20px;
        height: 20px;
        color: #969292;
        line-height: 20px;
        font-size: 12px;
        margin-bottom: 10px;
      }

      .message {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }
      }

      .received {
        justify-content: flex-start;
      }

      .sent {
        justify-content: flex-end;
      }

      .text {
        position: relative;
        padding: 10px;
        background-color: #ffffff;
        border-radius: 5px;
        max-width: 700px;
        margin: 0 20px;
      }

      .text:hover {
        background-color: #ebebeb;
      }

      .text2 {
        position: relative;
        padding: 10px;
        background-color: #95ec69;
        border-radius: 5px;
        max-width: 700px;
        margin: 0 20px;
      }

      .text::before {
        content: "";
        position: absolute;
        top: 50%;
        left: -10px;
        transform: translateY(-50%);
        border-width: 5px;
        border-style: solid;
        border-color: transparent #ffffff transparent transparent;
      }

      /* .sent ::after {
        content: "";
        position: absolute;
        top: 50%;
        right: -10px;
        transform: translateY(-50%);
        border-width: 5px;
        border-style: solid;
        border-color: transparent transparent transparent #95ec69;
      } */
    }
  }
}
</style>
