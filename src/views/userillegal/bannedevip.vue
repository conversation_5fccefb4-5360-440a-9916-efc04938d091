<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item label="用户ID" prop="userId">
                <el-input-number :controls="false" v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item label="信息" prop="info">
                <el-input v-model="queryForm.info" type="text"/>
              </el-form-item>
              <el-form-item label="类型" prop="bannedType">
                <el-select v-model="queryForm.bannedType" placeholder="选择类型">
                  <el-option label="全部" :value="opNull" />
                  <el-option label="ip" :value="1" />
                  <el-option label="设备" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间选择" prop="date">
                <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                  format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  :shortcuts="shortcuts" size="default" @change="timeChange" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="query()">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="uselistRef" :data="listData" style="width: 100%; height: 100%" v-loading="loading">
        <template v-for="v in listColmun" :key="v.prop">
          <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
            <template #default="scope">
              <span>{{ scope.row[v.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="v.prop == 'edit'" label="操作" width="120" :fixed="v.fixed">
            <template #default="scope">
              <el-popconfirm title="是否确认?" @confirm="editDel(scope.row)">
                <template #reference>
                  <el-button size="small" type="danger" :icon="Delete" />
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </template>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[20,50,100,200]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from 'element-plus'
import { Delete } from "@element-plus/icons-vue";
import { queryBanned_api, delBanned_api } from "@/api/userillegal"
import { getYMDHMS } from "@/utils/date"
import pageHook from "@/hooks/pageHook";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(queryBanned);
const listColmun = reactive({
  id: {
    prop: "id",
    title: "ID",
    show: true,
    minWidth: "60",
    fixed: "left"
  },
  info: {
    prop: "info",
    title: "信息",
    show: true,
    minWidth: "120",
    fixed: "left"
  },
  bannedType: {
    prop: "bannedType",
    title: "类型",
    show: true,
    minWidth: "60",
    fixed: false
  },
  createTime: {
    prop: "createTime",
    title: "创建时间",
    show: true,
    minWidth: "120",
    fixed: false
  },
  userIdString: {
    prop: "userIdString",
    title: "用户列表",
    show: true,
    minWidth: "120",
    fixed: false
  },
  peopleNum: {
    prop: "peopleNum",
    title: "人数",
    show: true,
    minWidth: "100",
    fixed: false
  },
  edit: {
    prop: "edit", //定义是否显示按钮 操作
    title: "操作",
    show: false,
    minWidth: "120",
    fixed: "right"
  },
});
const queryFormRef = ref<FormInstance>()
let listData = ref([])
let opNull = ref<any>(null);
let date = ref();
let loading = ref(false);
let queryForm = reactive<any>({
  userId: null,
  info: null,
  startTime: null,
  endTime: null,
  bannedType: null
})
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

async function queryBanned() {
  loading.value=true;
  let res = await queryBanned_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let { data, total } = res.data
  data.forEach((v: any) => {
    v.bannedType = v.bannedType == 1 ? "IP" : "设备"
    v.createTime = getYMDHMS("-", ":", v.createTime)
  })
  listData.value = data
  totalNum.value = total
  loading.value=false;
}

queryBanned()

//查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};
async function query() {
  loading.value=true;
  let res = await queryBanned_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let { data, total } = res.data
  data.forEach((v: any) => {
    v.bannedType = v.bannedType == 1 ? "IP" : "设备"
    v.createTime = getYMDHMS("-", ":", v.createTime)
  })
  listData.value = data
  totalNum.value = total;
  loading.value=false;
}

//删除
const editDel = async (row: any) => {
  await delBanned_api({ info: row.info })
  query()
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = null
  formEl.resetFields()
  queryBanned()
}
</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>