<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item label="用户ID" prop="userId">
                <el-input v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="query(queryForm.userId)">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="uselistRef" :data="listData" style="width: 100%; height: 100%">
        <template v-for="v in listColmun" :key="v.prop">
          <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
            <template #default="scope">
              <span>{{ scope.row[v.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="v.prop == 'avatar'" prop="avatar" label="头像" min-width="100">
            <template #default="scope">
              <el-avatar shape="square" :size="70" :src="scope.row.avatar" fit="cover" />
            </template>
          </el-table-column>
          <el-table-column v-if="v.prop == 'edit'" label="操作" width="360">
            <template #default="scope">
              <el-select v-model="scope.row.statusPunish" placeholder="请选择类型" @change="editSelect(scope.row)"
                class="statusPunish" size="small">
                <el-option label="警告" value="警告" />
                <el-option label="禁言" value="禁言" />
                <el-option label="封禁账号" value="封禁账号" />
                <el-option label="封禁IP" value="封禁IP" />
                <el-option label="封禁设备" value="封禁设备" />
                <el-option label="解除封禁" value="解除封禁" />
                <el-option label="解除禁言" value="解除禁言" />
              </el-select>
              <el-button @click="lookList(scope.row)" size="small">查看详情</el-button>
              <el-button @click="handleIgnore(scope.row)" size="small">忽略</el-button>
            </template>
          </el-table-column>
        </template>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10,20,30,50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />

      <el-dialog v-model="detailsShow" title="违规详情" width="100%" :fullscreen="true" @close="disclose">
        <el-table :data="listRecord" style="width: 100%">
          <el-table-column prop="id" label="ID" min-width="120" fixed></el-table-column>
          <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
          <el-table-column prop="operatorDesc" label="描述" min-width="120"></el-table-column>
          <el-table-column prop="illegalType" label="违规类型" min-width="120"></el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="120">
            <template #default="scope">{{ getYMDHMS("-", ":", scope.row.createTime) }}</template>
          </el-table-column>
          <el-table-column prop="operator" label="操作人" min-width="120"></el-table-column>
        </el-table>
        <template #footer>
          <el-pagination v-model:current-page="currentPageDetails" v-model:page-size="pageSizeDetails"
            :page-sizes="[5, 10, 15]" :small="true" layout="total, sizes, prev, pager, next, jumper"
            :total="totalNumDetails" :background="true" @size-change="SizeChange" @current-change="CurrentChange" />
        </template>
      </el-dialog>

      <!-- 警告 -->
      <div class="warn_dialog">
        <el-dialog v-model="warnShow" title="警告" width="35%">
          <el-form :model="warnFrom" class="banned-from" label-width="100px" :rules="rules">
            <el-form-item label="昵称" prop="nickname">
              <span>{{ warnFrom.nickname }}</span>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="warnFrom.editValue" placeholder="选择操作原因">
                <el-option label="低俗色情" value="低俗色情" />
                <el-option label="政治" value="政治" />
                <el-option label="广告" value="广告" />
                <el-option label="虚假欺骗" value="虚假欺骗" />
                <el-option label="侵权" value="侵权" />
                <el-option label="违禁内容" value="违禁内容" />
                <el-option label="辱骂" value="辱骂" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
            <el-form-item label="原因" v-if="warnFrom.editValue == '其他'">
              <el-input v-model="warnFrom.editValueIpt" />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="warnShow = false">取消</el-button>
              <el-button type="primary" @click="warnSubmit">确定</el-button>
            </span>
          </template>
        </el-dialog>
      </div>

      <!-- 禁言 -->
      <div class="metu_dialog">
        <el-dialog v-model="muteShow" title="禁言" width="35%">
          <el-form :model="muteFrom" class="banned-from" label-width="100px" :rules="rules">
            <el-form-item label="昵称" prop="nickname">
              <span>{{ muteFrom.nickname }}</span>
            </el-form-item>
            <el-form-item label="禁言状态" prop="muteStatus">
              <el-select v-model="muteFrom.muteStatus" placeholder="请选择禁言状态" @change="chMuteStatus">
                <el-option label="临时" :value="2" />
                <el-option label="永久" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="禁言时间" prop="MuteTime">
              <el-select v-model="muteFrom.MuteTime" placeholder="禁言状态为永久不用选择" :disabled="disabledMute">
                <el-option label="15分钟" value="15分钟" />
                <el-option label="30分钟" value="30分钟" />
                <el-option label="1小时" value="1小时" />
                <el-option label="1天" value="1天" />
                <el-option label="3天" value="3天" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="muteFrom.editValue" placeholder="选择操作原因">
                <el-option label="低俗色情" value="低俗色情" />
                <el-option label="政治" value="政治" />
                <el-option label="广告" value="广告" />
                <el-option label="虚假欺骗" value="虚假欺骗" />
                <el-option label="侵权" value="侵权" />
                <el-option label="违禁内容" value="违禁内容" />
                <el-option label="辱骂" value="辱骂" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
            <el-form-item label="原因" v-if="muteFrom.editValue == '其他'">
              <el-input v-model="muteFrom.editValueIpt" />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="muteShow = false">取消</el-button>
              <el-button type="primary" @click="muteSubmit">确定</el-button>
            </span>
          </template>
        </el-dialog>
      </div>

      <!-- 临时封禁 -->
      <div class="banned_dialog">
        <el-dialog v-model="banned" title="封禁" width="35%">
          <el-form ref="bannedFromRef" :model="bannedFrom" class="banned-from" label-width="80px" :rules="rules">
            <el-form-item label="昵称" prop="nickname">
              <span>{{ bannedFrom.nickname }}</span>
            </el-form-item>
            <el-form-item label="封禁时间" prop="BannedTime">
              <el-select v-model="bannedFrom.BannedTime" placeholder="请选择时间">
                <el-option label="1天" value="1天" />
                <el-option label="3天" value="3天" />
                <el-option label="7天" value="7天" />
                <el-option label="30天" value="30天" />
                <el-option label="永久" value="永久" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="bannedFrom.editValue" placeholder="选择操作原因">
                <el-option label="低俗色情" value="低俗色情" />
                <el-option label="政治" value="政治" />
                <el-option label="广告" value="广告" />
                <el-option label="虚假欺骗" value="虚假欺骗" />
                <el-option label="侵权" value="侵权" />
                <el-option label="违禁内容" value="违禁内容" />
                <el-option label="辱骂" value="辱骂" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
            <el-form-item label="原因" v-if="bannedFrom.editValue == '其他'">
              <el-input v-model="bannedFrom.editValueIpt" />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="banned = false">取消</el-button>
              <el-button type="primary" @click="bannedSubmit(bannedFromRef)">确定</el-button>
            </span>
          </template>
        </el-dialog>
      </div>

    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs ,onBeforeUnmount} from "vue";
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from "element-plus";
import { ignore_low_woman, illegalRecord_api, query_low_woman } from "@/api/userillegal"
import {  modifyUser_api, muteUser_api, warnUser_api, ipUser_api, deviceUser_api } from "@/api/foregrounduser"
import { getYMDHMS } from "@/utils/date"
import pageHook from "@/hooks/pageHook";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(query);
const listColmun = reactive({
  userId: {
    prop: "userId",
    title: "用户ID",
    show: true,
    minWidth: "120",
    fixed: "left"
  },
  nickname: {
    prop: "nickname",
    title: "昵称",
    show: true,
    minWidth: "120",
    fixed: false
  },
  avatar: {
    prop: "avatar",
    title: "头像",
    show: false,
    minWidth: "120",
    fixed: false
  },
  warnNum: {
    prop: "hitCount",
    title: "命中次数",
    show: true,
    minWidth: "120",
    fixed: false
  },
  edit: {
    prop: "edit", //定义是否显示按钮 操作
    title: "操作",
    show: false,
    minWidth: "120",
    fixed: "right"
  },
});
const queryFormRef = ref<FormInstance>()
let listData = ref([])
let listRecord = ref([])
let detailsShow = ref(false)
let currentPageDetails = ref<number>(1);
let pageSizeDetails = ref<number>(10);
let totalNumDetails = ref<any>(null);
let uId = ref()
let bannedId = ref()
let warnShow = ref(false)
let warnFrom = reactive<any>({
  userId: null,
  nickname: null,
  editValue: null,
  editValueIpt: null
})
let muteShow = ref(false)
let muteFrom = reactive<any>({
  userId: null,
  nickname: null,
  muteStatus: null,
  MuteTime: null,
  editValue: null,
  editValueIpt: null
})
let bannedFrom = reactive<any>({
  nickname: null,
  BannedTime: null,
  editValue: null,
  editValueIpt: null
})
// 当前时间
let toData = ref<any>(null);
let disabledMute = ref(false)
let bannedStatus = ref()
const bannedFromRef = ref<FormInstance>()
let isShow = ref(false)
let banned = ref(false)
const rules = reactive<FormRules>({
  BannedTime: [{ required: true, message: "不能为空", trigger: "change" }],
});
let queryForm = reactive<any>({
  userId: null
})

let setToDate = setInterval(() => {
  toData.value = new Date(new Date()).getTime();
}, 1000)
//查询
async function query(id?: any) {
  let res = await query_low_woman({
    userId: id,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let { data, total } = res.data
  data.forEach((v: any) => {
    if (v.createTime) v.createTime = getYMDHMS("-", ":", v.createTime)
  })
  listData.value = data
  totalNum.value = total;
}
query()

async function getRecord(id?: any) {
  let res = await illegalRecord_api({
    userId: id,
    pageSize: pageSizeDetails.value,
    pageNum: currentPageDetails.value,
  })
  let { data, total } = res.data
  data.forEach((v: any) => {
    if (v.illegalType == 1) v.illegalType = "禁言"
    if (v.illegalType == 2) v.illegalType = "临时禁言"
    if (v.illegalType == 3) v.illegalType = "警告"
    if (v.illegalType == 4) v.illegalType = "临时封禁"
    if (v.illegalType == 5) v.illegalType = "永久封禁"
    if (v.illegalType == 6) v.illegalType = "解除禁言"
    if (v.illegalType == 7) v.illegalType = "解封设备"
    if (v.illegalType == 8) v.illegalType = "忽略"
  })
  listRecord.value = data
  totalNumDetails.value = total;
}

const lookList = (row: any) => {
  uId.value = row.userId
  detailsShow.value = true
  getRecord(uId.value)
}

const disclose = () => {
  currentPageDetails.value = 1
}

// 分页 每条页数更改
const SizeChange = (val: number) => {
  pageSize.value = val;
  getRecord(uId.value);
};
// 当前页码改变
const CurrentChange = (val: number) => {
  currentPage.value = val;
  getRecord(uId.value);
};

//忽略数据
const handleIgnore = (val: any) => {
  const { userId } = val;
  ignore_low_woman({ userId }).then((res) => {
    console.log(res);
  })
}

//编辑
const editSelect = (row: any) => {
  let data = row
  if (row.statusPunish == "警告") { editWarn(data) }
  if (row.statusPunish == "禁言") { editMute(data) }
  if (row.statusPunish == "封禁账号") { editbanned(data) }
  if(row.statusPunish == "封禁IP"){editIP(data)}
  if(row.statusPunish == "封禁设备"){editDevice(data)}
  if(row.statusPunish == "解除封禁"){unseal(data, 0)}
  if(row.statusPunish == "解除禁言"){unMute(data, 1)}
}
//警告
const editWarn = (row: any) => {
  warnFrom.id = row.userId;
  warnFrom.nickname = row.nickname;
  warnFrom.editValueIpt = null
  warnFrom.editValue = null
  warnShow.value = true;
}
const warnSubmit = async () => {
  if (warnFrom.editValue == "其他") { warnFrom.editValue = warnFrom.editValueIpt }
  await warnUser_api({
    operatorDesc: warnFrom.editValue,
    userId: warnFrom.id
  });
  query();
  warnShow.value = false;
};


// //禁言
const editMute = (row: any) => {
  muteFrom.editValueIpt = null
  muteFrom.editValue = null
  muteFrom.muteStatus = null
  muteFrom.MuteTime = null
  muteFrom.nickname = row.nickname
  muteFrom.userId = row.userId
  muteShow.value = true
}

const muteSubmit = async () => {
  let { muteStatus, userId, MuteTime } = toRefs(muteFrom)
  let relieveMuteTime = ref(null)
  if (MuteTime.value == "15分钟") {
    relieveMuteTime.value = toData.value + 0.25 * 60 * 60 * 1000;
  } else if (MuteTime.value == "30分钟") {
    relieveMuteTime.value = toData.value + 0.5 * 60 * 60 * 1000;
  } else if (MuteTime.value == "1小时") {
    relieveMuteTime.value = toData.value + 1 * 60 * 60 * 1000;
  } else if (MuteTime.value == "1天") {
    relieveMuteTime.value = toData.value + 1 * 3600 * 24 * 1000;
  } else if (MuteTime.value == "3天") {
    relieveMuteTime.value = toData.value + 3 * 3600 * 24 * 1000;
  }
  if (muteFrom.muteStatus != null && muteFrom.relieveMuteTime != "") {
    if (muteFrom.editValue == "其他") { muteFrom.editValue = muteFrom.editValueIpt }
    await muteUser_api({ userId: userId.value, muteStatus: muteStatus.value, relieveMuteTime: relieveMuteTime.value, operatorDesc: muteFrom.editValue })
    muteShow.value = false;
    query();
  } else {
    ElMessage({
      message: "请选择状态",
      type: "warning",
    });
  }
}

const bannedSubmit = (bannedFromRef: FormInstance | undefined) => {
  if (!bannedFromRef) return
  let relieveBannedTime = ref(null)
  if (bannedFrom.BannedTime == "永久") {
    bannedStatus.value = 2
  } else {
    bannedStatus.value = 3
  }
  if (bannedFrom.BannedTime == "1天") {
    relieveBannedTime.value = toData.value + 1 * 3600 * 24 * 1000
  } else if (bannedFrom.BannedTime == "3天") {
    relieveBannedTime.value = toData.value + 3 * 3600 * 24 * 1000
  } else if (bannedFrom.BannedTime == "7天") {
    relieveBannedTime.value = toData.value + 7 * 3600 * 24 * 1000
  } else if (bannedFrom.BannedTime == "30天") {
    relieveBannedTime.value = toData.value + 30 * 3600 * 24 * 1000
  } else {
    relieveBannedTime.value = null
  }
  console.log(relieveBannedTime.value, bannedStatus.value);

  bannedFromRef.validate(async (valid: any) => {
    if (bannedFrom.editValue == "其他") { bannedFrom.editValue = bannedFrom.editValueIpt }
    if (valid) {
      await modifyUser_api({ id: bannedId.value, relieveBannedTime: relieveBannedTime.value, status: bannedStatus.value, operatorDesc: bannedFrom.editValue })
      banned.value = false;
      query();
    } else {
      return false
    }
  })
}

//封禁
const editbanned = (row: any) => {
  bannedFrom.relieveBannedTime = null;
  bannedFrom.editValueIpt = null
  bannedFrom.editValue = null
  bannedFrom.nickname = row.nickname
  bannedId.value = row.userId
  banned.value = true
}
// 封禁 IP  设备
const editIP = async (row: any) => {
  await ipUser_api({ userId: row.userId })
  query();
}
const editDevice = async (row: any) => {
  await deviceUser_api({ userId: row.userId })
  query();
}
// 解封
const unseal = async (row: any, status: any) => {
  await modifyUser_api({ id: row.userId, status })
  isShow.value = false;
  query();
}
//禁言状态
const chMuteStatus = (val: any) => {
  if (val == 3) {
    disabledMute.value = true
  } else {
    disabledMute.value = false
  }
}
//解除禁言
const unMute = async (row: any, muteStatus: any) => {
  await await muteUser_api({ userId: row.userId, muteStatus })
  query();
}
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  query()
}
onBeforeUnmount(() => {
  clearInterval(setToDate)
})
</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .statusPunish {
    width: 150px;
    margin-right: 16px;
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>