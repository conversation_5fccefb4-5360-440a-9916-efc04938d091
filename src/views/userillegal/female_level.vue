<template>
  <div class="goldsstatistics">
    <el-card class="box-card">

      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item>
                <el-button :icon="Plus" @click="handeladd">增加</el-button>
              </el-form-item>
              <el-form-item label="女用户ID" prop="userId">
                <el-input v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item label="评分" prop="level">
                <el-select v-model="queryForm.level" hanplaceholder="请选择">
                  <el-option label="S" value="S" />
                  <el-option label="A" value="A" />
                  <el-option label="B1" value="B1" />
                  <el-option label="B2" value="B2" />
                  <el-option label="C" value="C" />
                  <el-option label="D" value="D" />
                  <el-option label="E" value="E" />
                </el-select>
              </el-form-item>
              <el-form-item class="qubtn">
                <el-button type="primary" @click="query">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="goldsTab" style="width: 100%; height: 100%">
        <el-table-column prop="userId" label="用户ID" min-width="120" fixed></el-table-column>
        <el-table-column prop="level" label="等级" min-width="120">
          <template  #default="scope">
            <span v-if="!scope.row.isShow">{{ scope.row.level }}</span>
            <el-select v-else v-model="scope.row.level"  placeholder="请选择">
                  <el-option label="S" value="S" />
                  <el-option label="A" value="A" />
                  <el-option label="B1" value="B1" />
                  <el-option label="B2" value="B2" />
                  <el-option label="C" value="C" />
                  <el-option label="D" value="D" />
                  <el-option label="E" value="E" />
                </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
          <el-button :type="scope.row.isShow ? 'success' : 'primary'" @click="handlesave(scope.row)">{{ scope.row.isShow ? "完成" : "编辑" }}</el-button>
        </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />

      <el-dialog v-model="addShow" title="添加评级" style="max-width: 600px;">
        <el-form ref="addFormRef" :model="addForm" label-width="100px" label-position="right">
          <el-form-item label="用户ID">
          <el-input   v-model="addForm.userId"  :autosize="{ minRows: 20, maxRows: 25 }"
    type="textarea" style="width: 350px; "/>
          </el-form-item>
          <el-form-item label="用户评级">
            <el-select  v-model="addForm.level"  placeholder="请选择" style="width: 350px;">
                  <el-option label="S" value="S" />
                  <el-option label="A" value="A" />
                  <el-option label="B1" value="B1" />
                  <el-option label="B2" value="B2" />
                  <el-option label="C" value="C" />
                  <el-option label="D" value="D" />
                  <el-option label="E" value="E" />
                </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="addShow = false">取消</el-button>
            <el-button type="primary" @click="addLevel">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import type { FormInstance } from 'element-plus'
import pageHook from "@/hooks/pageHook";
import { getfemale_level,save_level } from "@/api/index"
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(queryPlatformGolds);


const queryFormRef = ref<FormInstance>()
let addForm = reactive<any>({
  level:null,
  userId:null,
});
let addShow=ref<boolean>(false);
let queryForm = reactive<any>({
  userId: null,
  level: null,
})
let goldsTab = ref<any>([])


const handlesave= async (row:any)=>{
  row.isShow = !row.isShow;
  if (!row.isShow) {
    await save_level({
      userId: row.userId,
      level: row.level,
    });
  }
}


const handeladd = () => {
addShow.value=true;
}

const addLevel=()=>{
  addShow.value=false;
  addForm.userId =addForm.userId.replace(/\n/g, ",");
  save_level({...addForm}).then(()=>{
    query();
  })
}

async function queryPlatformGolds() {
  let res = await getfemale_level({
    ...queryForm,
    size: pageSize.value,
    page: currentPage.value,
  })
  let { data, total } = res.data
  data.forEach((v: any) => {
    v.isShow = false;
  });
  goldsTab.value = data
  totalNum.value = total
}
queryPlatformGolds()

const query = () => {
  queryPlatformGolds()
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  query()
}
</script>

<style lang="scss" scoped>
.goldsstatistics {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .hasErrMark-on {
    color: #f00;
  }
}
</style>