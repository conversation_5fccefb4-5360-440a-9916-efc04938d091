<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :model="queryForm"  :inline="true" >
          <el-form-item label="时间选择">
              <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              size="default" @change="timeChange" />
            </el-form-item>
            <el-form-item>
          <el-button type="primary" style="margin-left: 20px;" @click="query"  >查询</el-button>
          <el-button @click="onreset">重置</el-button>
          </el-form-item>
        </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table  :data="listData" style="width: 100%; height: 100%">
        <el-table-column label="时间" fixed="left" min-width="120" prop="dateTime"/>
        <el-table-column label="男付费人数" fixed="left" min-width="120" prop="firstPaySumNum">
        <template slot="header" #header="_scope">
            <span>男付费人数</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="首次付费得男付费用户数"
              placement="top-start"
            >
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          </el-table-column>
        <template v-for="(v,index) in listColmun" :key="v.prop">
          <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
            <template #default="scope">
              <span>{{ man_leave_data(scope.row.retentions ,index) }}</span>
            </template>
          </el-table-column>

        </template>
      </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { query_retention } from "@/api/userillegal"
import {man_leave_data} from '@/hooks/numdata';
import{todayEnd,past30daysStart,getYMD} from '@/utils/date'
const listColmun = reactive<any>([
  ]);
let listData = ref([])
let date = ref<[Date, Date]>([
  new Date(past30daysStart),
  new Date(todayEnd),
])
let queryForm = reactive<any>({
  beginDate: past30daysStart,
  endDate: todayEnd,
});
//查询
async function query() {
let  params = {
      beginDate: getYMD('-', queryForm.beginDate),
      endDate: getYMD('-', queryForm.endDate),
    }

  let res = await query_retention({...params});
  let { data } = res.data
  listData.value =data
}
query()

const timeChange = (data: any) => {
  queryForm.beginDate = data[0];
  queryForm.endDate = data[1];
};

const onreset = () => {
  date.value = [
    new Date(past30daysStart),
    new Date(todayEnd),
  ]
  queryForm.beginDate = past30daysStart,
  queryForm.endDate = todayEnd,
  query();
}

function createTablePopper() {
  for (let index = 1; index < 31; index++) {
  let item={
    prop: `leave${index}`,
    title: `次${index}留`,
    show: true,
    minWidth: "120",
  }
  listColmun.push(item)
  }
}



createTablePopper();

</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>