<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item label="马甲包">
                <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect" />
              </el-form-item>
              <el-form-item label="用户ID" prop="userId">
                <el-input v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item label="性别">
                <el-select v-model="queryForm.gender" @change="setGender">
                  <el-option label="男" :value="1"></el-option>
                  <el-option label="女" :value="2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="操作人">
                <el-select v-model="queryForm.isOperator" @change="query">
                  <el-option label="全部" :value="-1"></el-option>
                  <el-option label="有操作人" :value="1"></el-option>
<!--                  <el-option label="无操作人" :value="0"></el-option>-->
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="query()">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="uselistRef" :data="listData" style="width: 100%; height: 100%">
        <template v-for="v in listColmun" :key="v.prop">
          <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
            <template #default="scope">
              <span>{{ scope.row[v.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="v.prop == 'avatar'" prop="avatar" label="头像" min-width="100">
            <template #default="scope">
              <el-avatar shape="square" :size="70" :src="scope.row.avatar" fit="cover" />
            </template>
          </el-table-column>
          <el-table-column v-if="v.prop == 'edit'" label="操作" width="120" :fixed="v.fixed">
            <template #default="scope">
              <el-button @click="lookList(scope.row)" size="small">查看详情</el-button>
            </template>
          </el-table-column>
        </template>
      </el-table>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[3, 5, 10, 20]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <el-dialog v-model="detailsShow" title="违规详情" width="100%" :fullscreen="true" @close="disclose">
        <el-table :data="listRecord" style="width: 100%">
          <el-table-column prop="id" label="ID" min-width="120" fixed></el-table-column>
          <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
          <el-table-column prop="operatorDesc" label="描述" min-width="120"></el-table-column>
          <el-table-column prop="illegalType" label="违规类型" min-width="120"></el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="120">
            <template #default="scope">{{ getYMDHMS("-", ":", scope.row.createTime) }}</template>
          </el-table-column>
          <el-table-column prop="operator" label="操作人" min-width="120"></el-table-column>
        </el-table>
        <template #footer>
          <el-pagination
            v-model:current-page="currentPageDetails"
            v-model:page-size="pageSizeDetails"
            :page-sizes="[5, 10, 15]"
            :small="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalNumDetails"
            :background="true"
            @size-change="SizeChange"
            @current-change="CurrentChange"
          />
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import { illegal_api, illegalRecord_api } from "@/api/userillegal";
import { getYMDHMS } from "@/utils/date";
import pageHook from "@/hooks/pageHook";

let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(query);
const listColmun = reactive({
  id: {
    prop: "id",
    title: "ID",
    show: true,
    minWidth: "120",
    fixed: "left"
  },
  userId: {
    prop: "userId",
    title: "用户ID",
    show: true,
    minWidth: "120",
    fixed: "left"
  },
  avatar: {
    prop: "avatar",
    title: "头像",
    show: false,
    minWidth: "120",
    fixed: false
  },
  nickname: {
    prop: "nickname",
    title: "昵称",
    show: true,
    minWidth: "120",
    fixed: false
  },
  status: {
    prop: "status",
    title: "用户状态",
    show: true,
    minWidth: "120",
    fixed: false
  },
  warnNum: {
    prop: "warnNum",
    title: "警告次数",
    show: true,
    minWidth: "120",
    fixed: false
  },
  muteNum: {
    prop: "muteNum",
    title: "禁言次数",
    show: true,
    minWidth: "120",
    fixed: false
  },
  bannedNum: {
    prop: "bannedNum",
    title: "封禁次数",
    show: true,
    minWidth: "120",
    fixed: false
  },
  createTime: {
    prop: "createTime",
    title: "创建时间",
    show: true,
    minWidth: "120",
    fixed: false
  },
  updateTime: {
    prop: "updateTime",
    title: "更新时间",
    show: true,
    minWidth: "120",
    fixed: false
  },
  edit: {
    prop: "edit", //定义是否显示按钮 操作
    title: "操作",
    show: false,
    minWidth: "120",
    fixed: "right"
  },
});
const queryFormRef = ref<FormInstance>();
let listData = ref([]);
let listRecord = ref([]);
let detailsShow = ref(false);
let currentPageDetails = ref<number>(1);
let pageSizeDetails = ref<number>(10);
let totalNumDetails = ref<any>(null);
let uId = ref();
const queryForm = reactive<any>({
  userId: null,
  applicationId: "com.dongxin.app",
  gender: null,
  isOperator: null,
});

//查询
async function query() {
  let res = await illegal_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    gender: queryForm.gender,
    isOperator: queryForm.isOperator,
  });
  let { data, total } = res.data;
  data.forEach((v: any) => {
    if (v.createTime) v.createTime = getYMDHMS("-", ":", v.createTime);
    v.status === 0 ? (v.status = "正常") : v.status === 1 ? (v.status = "已注销") : v.status === 2 ? (v.status = "被封禁") : v.status === 3 ? (v.status = "临时封禁") : "";
  });
  listData.value = data;
  totalNum.value = total;
}

query();

async function getRecord(id?: any) {
  let res = await illegalRecord_api({
    userId: id,
    applicationId: queryForm.applicationId,
    pageSize: pageSizeDetails.value,
    pageNum: currentPageDetails.value,
  });
  let { data, total } = res.data;
  data.forEach((v: any) => {
    if (v.illegalType == 1) v.illegalType = "禁言";
    if (v.illegalType == 2) v.illegalType = "临时禁言";
    if (v.illegalType == 3) v.illegalType = "警告";
    if (v.illegalType == 4) v.illegalType = "临时封禁";
    if (v.illegalType == 5) v.illegalType = "永久封禁";
    if (v.illegalType == 6) v.illegalType = "解除禁言";
    if (v.illegalType == 7) v.illegalType = "解除封禁";
    if (v.illegalType == 8) v.illegalType = "忽略";
    if (v.illegalType == 9) v.illegalType = "冻结提现";
    if (v.illegalType == 10) v.illegalType = "解除冻结提现";
    if (v.illegalType == 15) v.illegalType = "限制用户音视频";
    if (v.illegalType == 16) v.illegalType = "解锁用户音视频";
    if (v.illegalType == 17) v.illegalType = "更新交友数据等级";
  });
  listRecord.value = data;
  totalNumDetails.value = total;
}

const lookList = (row: any) => {
  uId.value = row.userId;
  detailsShow.value = true;
  getRecord(uId.value);
};

const disclose = () => {
  currentPageDetails.value = 1;
};

// 分页 每条页数更改
const SizeChange = (val: number) => {
  pageSize.value = val;
  getRecord(uId.value);
};
// 当前页码改变
const CurrentChange = (val: number) => {
  currentPage.value = val;
  getRecord(uId.value);
};

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  query();
};

const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  query();
};

const setGender = (gender: number) => {
  queryForm.gender = gender;
  query();
};
</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>