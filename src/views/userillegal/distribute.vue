<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="时间选择">
              <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :shortcuts="shortcuts" size="default" @change="timeChange" />
            </el-form-item>
            <el-form-item  label="女用户等级">
              <el-select v-model="queryForm.level" >
                <el-option label="S" value="S" />
                <el-option label="A" value="A" />
                <el-option label="B" value="B" />
                <el-option label="C" value="C" />
                <el-option label="D" value="D" />
              </el-select>
            </el-form-item>
            <el-form-item  label="女用户ID">
             <el-input v-model="queryForm.userId"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="margin-left: 20px;" @click="query">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="listData" style="width: 100%; height: 100%" :border="true" ref="tableRef">
       <el-table-column label="序号" min-width="120" prop="id" />
        <el-table-column label="女用户ID" min-width="120" prop="femaleId" />
        <el-table-column label="分发开始时间" min-width="200" prop="begin"/>
        <el-table-column label="分发结束时间" min-width="200" prop="end" />
        <el-table-column label="女用户权重" min-width="120" prop="level" column-key="level" />
        <el-table-column label="分发男用户数量" min-width="120" prop="manSum" />
        <el-table-column label="分发男用户ID" min-width="200" prop="maleIds"/>
        <el-table-column label="分发新男用户数量" min-width="120" prop="newManCnt" />
        <el-table-column label="分发新男用户ID" min-width="120" prop="newManIds" >
          <template #default="scope">
           {{ scope.row.newManIds ? scope.row.newManIds : "0" }}
          </template>
        </el-table-column>
        <el-table-column label="分发付费男用户数量" min-width="120" prop="payManCnt" />
        <el-table-column label="分发付费男用户ID" min-width="120" prop="payManIds" >
          <template #default="scope">
           {{ scope.row.payManIds ? scope.row.payManIds : "0" }}
          </template>
        </el-table-column>
        <el-table-column label="分发未付费男用户数量" min-width="120" prop="unPayManCnt" />
        <el-table-column label="分发未付费男用户ID" min-width="120" prop="unPayManIds" >
          <template #default="scope">
           {{ scope.row.unPayManIds ? scope.row.unPayManIds : "0" }}
          </template>
        </el-table-column>
        <el-table-column label="分发后女用户总收益" min-width="120" prop="femaleSumIncom" />
        <el-table-column label="分发新男用户收益" min-width="120" prop="newManIncome" />
        <el-table-column label="分发付费男用户收益" min-width="120" prop="payManIncome" />
        <el-table-column label="分发未付费男用户收益" min-width="120" prop="unPayManIncome" />
        <el-table-column label="分发新男用户收益占比" min-width="120" prop="newManAvg" >
          <template  slot="header" #header="_scope">
          <span> 分发新男用户收益占比
            <el-tooltip class="box-item" effect="dark" content="分发新男用户数量/分发后女用户总收益*100%" placement="top-start">
              <el-icon ><QuestionFilled /></el-icon>
            </el-tooltip> 
          </span>
          </template>
        </el-table-column>
        <el-table-column label="分发付费男用户收益占比" min-width="120" prop="payManAvg" >
        <template  slot="header" #header="_scope">
          <span> 分发付费男用户收益占比
            <el-tooltip class="box-item" effect="dark" content="分发付费男用户收益占比/分发后女用户总收益*100%" placement="top-start">
              <el-icon ><QuestionFilled /></el-icon>
            </el-tooltip> 
          </span>
          </template>
          </el-table-column>
        <el-table-column label="分发未付费男用户收益占比" min-width="140" prop="unPayManAvg" >
          <template  slot="header" #header="_scope">
          <span> 分发未付费男用户收益占比
            <el-tooltip class="box-item" effect="dark" content="分发未付费男用户收益占比/分发后女用户总收益*100%" placement="top-start">
              <el-icon ><QuestionFilled /></el-icon>
            </el-tooltip> 
          </span>
          </template>

        </el-table-column>

      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[20, 40, 60]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { query_distribution_record } from "@/api/index"
import pageHook from "@/hooks/pageHook";
import{getYMDHMS} from '@/utils/date'
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(query);
let listData = ref<any>([])
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
// //最近30天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
// const tableHeight = ref(); // 已移除，使用CSS布局

let queryForm = reactive<any>({
  startTime: past7daysStart,
  endTime: todayEnd,
  level: null,
  userId:null,
});
let tableRef = ref<any>();

let date = ref<[Date, Date]>([
  new Date(past7daysStart),
  new Date(todayEnd),
])
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

// 移除动态高度计算，使用CSS布局
// onMounted(() => {
//   tableHeight.value = window.innerHeight - tableRef.value.$el.offsetTop - 150;
//   window.onresize = () => {
//     tableHeight.value = window.innerHeight - tableRef.value.$el.offsetTop - 150;
//   };
// })


//查询
async function query() {
  const { data: { data, total } } = await query_distribution_record({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  data.forEach((item: { begin: string; end: string; })=>{
    item.begin = getYMDHMS("-", ":", item.begin)
    item.end = getYMDHMS("-", ":", item.end)
  })
  listData.value = data;
  totalNum.value = total;
  let list = {
    id: '总计',
    manSum: 0,
    femaleId: '总计',
    newManCnt: 0,
    payManCnt: 0,
    unPayManCnt: 0,
    femaleSumIncom: 0,
    newManIncome: 0,
    payManIncome: 0,
    unPayManIncome: 0,
    newManAvg: 0,
    payManAvg: 0,
    unPayManAvg: 0,
    level: '总计'
  };
  for (let index = 0; index < data.length; index++) {
    list.manSum += data[index].manSum;
    list.newManCnt += data[index].newManCnt;
    list.payManCnt += data[index].payManCnt
    list.unPayManCnt += data[index].unPayManCnt
    list.newManIncome += data[index].newManIncome
    list.payManIncome += data[index].payManIncome
    list.unPayManIncome += data[index].unPayManIncome
  }
  if (list.femaleSumIncom) {
    list.newManAvg = list.newManIncome / list.femaleSumIncom;
    list.payManAvg = list.payManCnt / list.femaleSumIncom;
    list.unPayManAvg = list.unPayManCnt / list.femaleSumIncom;
  }
  listData.value.push(list);
}
query();

const onreset = () => {
  queryForm.startTime=past7daysStart,
  queryForm.endTime=todayEnd,
  queryForm.userId=null,
  queryForm.level=null,
  query();
}


</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>