<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item label="用户ID" prop="userId">
                <el-input :controls="false" v-model="queryForm.userId"/>
              </el-form-item>
              <el-form-item label="用户昵称" prop="nickName">
                <el-input :controls="false" v-model="queryForm.nickName"/>
              </el-form-item>
              <el-form-item label="目标用户ID" prop="toUserId">
                <el-input v-model="queryForm.toUserId" @change="handleChange" type="text"/>
              </el-form-item>
              <el-form-item prop="backpackType" label="类型" fixed>
                <el-select v-model="queryForm.backpackType" placeholder="请选择" style="width: 200px;">
                  <el-option label="礼物" :value="1"/>
                  <el-option label="道具" :value="2"/>
                </el-select>
              </el-form-item>
              <el-form-item prop="businessType" label="道具类型" fixed>
                <el-select v-model="queryForm.businessType" placeholder="请选择" style="width: 200px;">
                  <el-option label="礼物" :value="0"/>
                  <el-option label="视频体验卡" :value="1"/>
                  <el-option label="搭讪卡" :value="2"/>
                  <el-option label="消息卡" :value="3"/>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="query()">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="uselistRef" :data="listData" style="width: 100%; height: 100%">
        <el-table-column v-if="true" label="ID" prop="id" min-width="60" fixed="left"></el-table-column>
        <el-table-column v-if="true" label="用户ID" prop="userId" min-width="120" fixed="left"></el-table-column>
        <!--        <el-table-column v-if="true" label="用户昵称" prop="nickName" min-width="120" fixed="left"></el-table-column>-->
        <el-table-column prop="nickName" label="昵称" min-width="150">
          <template #default="scope">
            <div style="display: flex; align-items: center;">
              <span>{{ scope.row.nickname }}</span>
              <sup v-if="scope.row.channel === 'vivo_vivo_vivo'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">vivo</sup>
              <sup v-else-if="scope.row.channel === 'huawei_huawei_huawei'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">华为</sup>
              <sup v-else-if="scope.row.channel === 'official_official_official'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">官方</sup>
              <sup v-else-if="scope.row.channel === 'oppo_oppo_oppo'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">oppo</sup>
              <sup v-else-if="scope.row.channel === 'new_new_new'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New</sup>
              <sup v-else-if="scope.row.channel === 'new1_new1_new1'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New1</sup>
              <sup v-else-if="scope.row.channel === 'new2_new2_new2'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New2</sup>
              <sup v-else-if="scope.row.channel === 'dage_dage_dage'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">大哥</sup>
              <sup v-else-if="scope.row.channel === 'honor_honor_honor'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">荣耀</sup>
              <sup v-else-if="scope.row.channel === 'ying_ying_ying'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">YY宝</sup>
              <sup v-else-if="scope.row.channel === 'juliang_juliang_juliang'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">抖音</sup>
              <sup v-else-if="scope.row.channel === 'xiaomi_xiaomi_xiaomi'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">小米</sup>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="true" label="目标用户ID" prop="toUserId" min-width="60"></el-table-column>
        <el-table-column v-if="true" label="类型" prop="backpackType" min-width="120"></el-table-column>
        <el-table-column v-if="true" label="道具类型" prop="businessType" min-width="120"></el-table-column>
        <el-table-column v-if="true" label="使用前数量" prop="beforeNums" min-width="100"></el-table-column>
        <el-table-column v-if="true" label="使用数量" prop="nums" min-width="100"></el-table-column>
        <el-table-column v-if="true" label="剩余数量" prop="afterNums" min-width="100"></el-table-column>
        <el-table-column v-if="true" label="使用时间" prop="useTime" min-width="100"></el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import type {FormInstance} from 'element-plus'
import {backpack_used_record} from "@/api/userillegal"
import {getYMDHMS} from "@/utils/date"
import pageHook from "@/hooks/pageHook";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} = pageHook(query);
// const listColmun = reactive({
//   id: {
//     prop: "id",
//     title: "ID",
//     show: true,
//     minWidth: "60",
//     fixed: "left"
//   },
//   userId: {
//     prop: "userId",
//     title: "用户ID",
//     show: true,
//     minWidth: "120",
//     fixed: "left"
//   },
//   nickName: {
//     prop: "nickName",
//     title: "用户昵称",
//     show: true,
//     minWidth: "120",
//     fixed: "left"
//   },
//   toUserId: {
//     prop: "toUserId",
//     title: "目标用户ID",
//     show: true,
//     minWidth: "60",
//     fixed: false
//   },
//   backpackType: {
//     prop: "backpackType",
//     title: "类型",
//     show: true,
//     minWidth: "120",
//     fixed: false
//   },
//   businessType: {
//     prop: "businessType",
//     title: "道具类型",
//     show: true,
//     minWidth: "120",
//     fixed: false
//   },
//   beforeNums: {
//     prop: "beforeNums",
//     title: "使用前数量",
//     show: true,
//     minWidth: "100",
//     fixed: false
//   },
//   nums: {
//     prop: "nums",
//     title: "使用数量",
//     show: true,
//     minWidth: "100",
//     fixed: false
//   },
//   afterNums: {
//     prop: "afterNums",
//     title: "剩余数量",
//     show: true,
//     minWidth: "100",
//     fixed: false
//   },
//   useTime: {
//     prop: "useTime",
//     title: "使用时间",
//     show: true,
//     minWidth: "100",
//     fixed: false
//   },
//
// });
const queryFormRef = ref<FormInstance>()
let listData = ref([])
let date = ref();
let queryForm = reactive<any>({
  userId: null,
  toUserId: null,
  businessType: null,
  backpackType: null,
})


const handleChange = (e: any) => {
  if (e === '') queryForm.totalNum = null;
}


async function query() {
  let res = await backpack_used_record({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let {data, total} = res.data
  data.forEach((item: any) => {
    item.backpackType = item.backpackType === 2 ? '道具' : '礼物';
    item.businessType = item.businessType === 1 ? '视频体验卡' : item.businessType === 2 ? '搭讪卡' : item.businessType === 3 ? '消息卡' : '礼物';
    item.useTime = getYMDHMS("-", ":", item.useTime);
  })
  listData.value = data
  totalNum.value = total
}


const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = null
  formEl.resetFields()
  query();
}
query();
</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>