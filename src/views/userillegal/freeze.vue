<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item label="用户ID" prop="userId">
                <el-input :controls="false" v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item label="操作人" prop="operator">
                <el-input v-model="queryForm.operator" @change="handleChange"  type="text"/>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="query()">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="uselistRef" :data="listData" style="width: 100%; height: 100%">
        <template v-for="v in listColmun" :key="v.prop">
          <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
            <template #default="scope">
              <span>{{ scope.row[v.prop] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from 'element-plus'
import { freeze_withdrawal_record_api } from "@/api/userillegal"
import pageHook from "@/hooks/pageHook";
import { getYMDHMS } from "@/utils/date";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(queryBackpack);
const listColmun = reactive({
  userId: {
    prop: "userId",
    title: "用户ID",
    show: true,
    minWidth: "120",
    fixed: "left"
  },
  operator: {
    prop: "operator",
    title: "操作人",
    show: true,
    minWidth: "60",
    fixed: false
  },
  operatorDate: {
    prop: "operatorDate",
    title: "操作时间",
    show: true,
    minWidth: "120",
    fixed: false
  },
  freezeWithdrawal: {
    prop: "freezeWithdrawal",
    title: "操作状态",
    show: true,
    minWidth: "120",
    fixed: false
  }
});
const queryFormRef = ref<FormInstance>()
let listData = ref([])
let date = ref();
let queryForm = reactive<any>({
  userId: null,
  operator:null,
})


async function queryBackpack() {
  let res = await freeze_withdrawal_record_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let { data, total } = res.data
  data.forEach((item:any)=>{
    item.operatorDate=getYMDHMS('-',':',item.operatorDate);
    item.freezeWithdrawal=item.freezeWithdrawal?'冻结提现':'解除冻结';
  })
  listData.value = data
  totalNum.value = total
}
queryBackpack()

const handleChange=(e:any)=>{
  if(e==='')queryForm.totalNum=null;
}


async function query() {
  let res = await freeze_withdrawal_record_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let { data, total } = res.data
  data.forEach((item:any)=>{
    item.operatorDate=getYMDHMS('-',':',item.operatorDate);
    item.freezeWithdrawal=item.freezeWithdrawal?'冻结提现':'解除冻结';
  })
  listData.value = data
  totalNum.value = total
}


const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = null
  formEl.resetFields()
  queryBackpack()
}
</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>