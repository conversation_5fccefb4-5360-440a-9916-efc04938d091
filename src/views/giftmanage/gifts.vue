<template>
  <div class="gifts">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm">
            <el-form-item>
              <el-button @click="addGifts()" :icon="Plus">添加</el-button>
            </el-form-item>
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="搜索" style="margin-left: -120px">
              <el-input v-model="searchTerm" placeholder="请输入礼物名" @input="queryGifts"></el-input>
            </el-form-item>
            <el-form-item label="礼物展示状态" prop="status">
              <el-select class="header-select" v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="上架" value="on"/>
                <el-option label="下架" value="off"/>
              </el-select>
            </el-form-item>
            <el-form-item label="礼物类型" prop="giftType">
              <el-select class="header-select" v-model="queryForm.giftType" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="礼物面板" :value="1"/>
                <el-option label="搭讪礼物" :value="2"/>
                <el-option label="缘分礼物" :value="3"/>
                <el-option label="背包亲密度礼物" :value="4"/>
                <el-option label="视频免费助力礼物" :value="5"/>
                <el-option label="亲密度特权礼物" :value="6"/>
              </el-select>
            </el-form-item>
            <el-form-item label="礼物可见" prop="giftClassType">
              <el-select class="header-select" v-model="queryForm.giftClassType" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="男可见" :value="1"/>
                <el-option label="女可见" :value="2"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="giftsTab" style="width: 100%; height: 100%">
          <el-table-column prop="id" label="ID" min-width="60" fixed></el-table-column>
          <el-table-column prop="title" label="礼物名" min-width="120" fixed></el-table-column>
          <el-table-column prop="giftTypeValue" label="礼物类型" min-width="120">
            <template #default="scope">
              {{ scope.row.giftTypeValue }}
            </template>
          </el-table-column>
          <el-table-column prop="price" label="礼物金币数" min-width="120"></el-table-column>
          <el-table-column prop="intimacyVal" label="亲密度值" min-width="120">
          </el-table-column>
          <el-table-column prop="intimacyLevel" label="亲密度等级" min-width="120">
          </el-table-column>
          <el-table-column prop="picUrl" label="礼物图片" min-width="120">
            <template #default="scope">
              <el-avatar shape="square" :size="80" fit="cover" :src="scope.row.picUrl"/>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="svga" label="礼物动画" min-width="120">
                      <template #default="scope">
                          <video width="80" height="80" :src="scope.row.svga" controls autoplay muted loop></video>
                          <el-avatar shape="square" :size="80" fit="cover" :src="scope.row.svga" />
                      </template>
                  </el-table-column> -->
          <el-table-column prop="status" label="礼物展示状态" min-width="120">
            <template #default="scope">
              {{ scope.row.status == "on" ? "上架" : "下架" }}
            </template>
          </el-table-column>
          <el-table-column prop="sort" label="排序" min-width="120"></el-table-column>
          <el-table-column prop="remark" label="备注" min-width="120"></el-table-column>
          <el-table-column prop="giftClassType" label="礼物可见" min-width="120">
            <template #default="scope">
              {{ scope.row.giftClassType === 1 ? "男可见" : scope.row.giftClassType === 2 ? "女可见" : "-" }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="150">
            <template #default="scope">
              <el-button @click="editGifts(scope.row)" size="small" type="primary" :icon="Edit"/>
              <el-button @click="handleDelete(scope.row)" size="small" type="danger" :icon="Delete"/>
              <el-button v-if="scope.row.giftType === 4" @click="openGiveGiftDialog(scope.row)" size="small"
                         type="success">赠送礼物
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
      <el-dialog v-model="giveGiftDialogVisible" title="赠送礼物">
        <el-form :model="giveGiftForm">
          <el-form-item label="用户ID" prop="userId">
            <el-input v-model="giveGiftForm.userId"/>
          </el-form-item>
          <el-form-item label="数量" prop="num">
            <el-input-number v-model="giveGiftForm.num" :min="1"/>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="giveGiftDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="giveGift">确认</el-button>
        </template>
      </el-dialog>
      <el-dialog v-model="isShow" title="编辑礼物">
        <el-form :model="editForm">
          <el-form-item label="ID" prop="id">
            <el-input v-model="editForm.id" disabled/>
          </el-form-item>
          <el-form-item label="礼物名" prop="title">
            <el-input v-model="editForm.title"/>
          </el-form-item>
          <el-form-item label="礼物金币数" prop="price">
            <el-input v-model="editForm.price"/>
          </el-form-item>
          <el-form-item label="礼物动画" prop="svga">
            <div class="avatar-uploader" @click="uploadFile">
              <input type="file" @change="upLod" ref="upLoad" class="upImg">
              <span v-if="editForm.svga">{{ editForm.svga }}</span>
              <el-icon v-else class="avatar-uploader-icon">
                <Plus/>
              </el-icon>
            </div>
          </el-form-item>
          <el-form-item label="礼物图片" prop="picUrl">
            <div class="avatar-uploader" @click="upload">
              <input type="file" @change="getFile" ref="upImg" class="upImg">
              <img v-if="editForm.picUrl" :src="editForm.picUrl" class="avatar"/>
              <el-icon v-else class="avatar-uploader-icon">
                <Plus/>
              </el-icon>
            </div>
          </el-form-item>
          <el-form-item label="展示状态" prop="status">
            <el-select class="header-select" v-model="editForm.status" placeholder="请选择">
              <el-option label="上架" value="on"/>
              <el-option label="下架" value="off"/>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="礼物类型" prop="giftType">
            <el-select class="header-select" v-model="editForm.giftType" placeholder="请选择">
              <el-option label="礼物面板" :value="1" />
              <el-option label="搭讪礼物" :value="2" />
              <el-option label="缘分礼物" :value="3" />
            </el-select>
          </el-form-item> -->
          <el-form-item label="亲密度值" prop="intimacyVal" v-if="editForm.giftType===4||editForm.giftType===5">
            <el-input v-model="editForm.intimacyVal"/>
          </el-form-item>
          <el-form-item label="亲密度等级" prop="intimacyLevel" v-if="editForm.giftType===6">
            <el-input-number v-model="addForm.intimacyLevel" :controls="false">
            </el-input-number>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="editForm.sort"/>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="editForm.remark" autocomplete="off"/>
          </el-form-item>
          <el-form-item label="礼物可见" prop="giftClassType">
            <el-select class="header-select" v-model="editForm.giftClassType" placeholder="请选择">
              <el-option label="全部" :value="0"/>
              <el-option label="男可见" :value="1"/>
              <el-option label="女可见" :value="2"/>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isShow = false">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog v-model="addShow" title="添加礼物">
        <el-form :model="addForm" ref="addFormRef" :rules="rules">
          <el-form-item label="礼物名" prop="title">
            <el-input v-model="addForm.title"/>
          </el-form-item>
          <el-form-item label="马甲包">
            <majaSelect :applicationId="addForm.applicationId" @changeSelect="changeSelect1"/>
          </el-form-item>
          <el-form-item label="礼物金币数" prop="price">
            <el-input v-model="addForm.price"/>
          </el-form-item>
          <el-form-item label="礼物动画" prop="svga">
            <div class="avatar-uploader" @click="uploadFile">
              <input type="file" @change="upLod" ref="upLoad" class="upImg">
              <span v-if="addForm.svga">{{ addForm.svga }}</span>
              <el-icon v-else class="avatar-uploader-icon">
                <Plus/>
              </el-icon>
            </div>
          </el-form-item>
          <el-form-item label="礼图片" prop="picUrl">
            <div class="avatar-uploader" @click="upload">
              <input type="file" @change="getFile" ref="upImg" class="upImg">
              <img v-if="addForm.picUrl" :src="addForm.picUrl" class="avatar"/>
              <el-icon v-else class="avatar-uploader-icon">
                <Plus/>
              </el-icon>
            </div>
          </el-form-item>
          <el-form-item label="礼物类型" prop="giftType">
            <el-select class="header-select" v-model="addForm.giftType" placeholder="请选择">
              <el-option label="礼物面板" :value="1"/>
              <el-option label="搭讪礼物" :value="2"/>
              <el-option label="缘分礼物" :value="3"/>
              <el-option label="背包亲密度礼物" :value="4"/>
              <el-option label="视频免费助力礼物" :value="5"/>
              <el-option label="亲密度特权礼物" :value="6"/>
            </el-select>
          </el-form-item>
          <el-form-item label="展示状态" prop="status">
            <el-select class="header-select" v-model="addForm.status" placeholder="请选择">
              <el-option label="上架" value="on"/>
              <el-option label="下架" value="off"/>
            </el-select>
          </el-form-item>
          <el-form-item label="亲密度值" prop="intimacyVal" v-if="addForm.giftType===4||editForm.giftType===5">
            <el-input v-model="addForm.intimacyVal"/>
          </el-form-item>
          <el-form-item label="亲密度等级" prop="intimacyLevel" v-if="addForm.giftType===6">
            <el-input-number v-model="addForm.intimacyLevel" :controls="false">
            </el-input-number>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="addForm.sort"/>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="addForm.remark" autocomplete="off"/>
          </el-form-item>
          <el-form-item label="礼物可见" prop="giftClassType">
            <el-select class="header-select" v-model="addForm.giftClassType" placeholder="请选择">
              <el-option label="全部" :value="0"/>
              <el-option label="男可见" :value="1"/>
              <el-option label="女可见" :value="2"/>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="addShow = false">取消</el-button>
            <el-button type="primary" @click="addSubMit(addFormRef)">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import pageHook from "@/hooks/pageHook";
import {Delete, Edit, Plus} from '@element-plus/icons-vue'
import COS from 'cos-js-sdk-v5'
import type {FormInstance, FormRules} from 'element-plus'
import axios from 'axios'
import {queryGifts_api, modifyGifts_api, addGifts_api, deleteGift_api, giveGift_api} from "@/api/gifts"

let searchTerm = ref('');

let giveGiftDialogVisible = ref(false)
let giveGiftForm = reactive<any>({
  userId: '',  // 用户每次打开对话框时，这里的userId都会被重置为空
  num: 1,
  giftId: null
})

const openGiveGiftDialog = (row: any) => {
  giveGiftForm.giftId = row.id
  giveGiftForm.userId = ''  // 每次打开对话框时，都将userId重置为空
  giveGiftDialogVisible.value = true
}

const giveGift = async () => {
  let res = await giveGift_api(giveGiftForm)
  if (res.data.code === 200 && res.data.success) {
    alert(res.data.message)
    giveGiftDialogVisible.value = false
  } else {
    alert('赠送礼物失败')
  }
}
let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(querySubmit);
const queryFormRef = ref<FormInstance>()
const addFormRef = ref<FormInstance>()
let giftsTab = ref([])
let isShow = ref(false)
let addShow = ref(false)
let editForm = reactive<any>({
  id: null,
  title: null,
  price: null,
  picUrl: null,
  svga: null,
  status: null,
  sort: null,
  remark: null,
  intimacyLevel: null,
  giftType: null,
  intimacyVal: null,
  giftClassType: 0 as number
})
let addForm = reactive<any>({
  title: '',
  price: '',
  picUrl: '',
  svga: '',
  status: null,
  sort: '',
  remark: '',
  giftType: null,
  intimacyVal: '',
  intimacyLevel: '',
  applicationId: 'com.dingkumixiang.app',
  giftClassType: 0 as number
})
const upLoad = ref<any>(null)
let upImg = ref<any>(null)
let Cos = reactive<any>({
  tmpSecretId: "",
  tmpSecretKey: "",
  sessionToken: "",
  createTime: null,
  expireTime: null
})
const rules = reactive<FormRules>({
  title: [{required: true, message: "不能为空", trigger: "blur"}],
  price: [{required: true, message: "不能为空", trigger: "blur"}],
  svga: [{required: true, message: "不能为空", trigger: "change"}],
  picUrl: [{required: true, message: "不能为空", trigger: "change"}],
  status: [{required: true, message: "不能为空", trigger: "change"}],
  giftType: [{required: true, message: "不能为空", trigger: "change"}],
  sort: [{required: true, message: "不能为空", trigger: "blur"}],
  intimacyVal: [{required: true, message: "不能为空", trigger: "change"}],
  intimacyLevel: [{required: true, message: "不能为空", trigger: "blur"}],
  giftClassType: [{required: true, message: "不能为空", trigger: "change"}]
});

const getCos = () => {
  axios.post('https://api.ge0.cc/uaa/access_credentials').then((res: any) => {
    const {data} = res.data
    Cos.tmpSecretId = data.tmpSecretId
    Cos.tmpSecretKey = data.tmpSecretKey
    Cos.sessionToken = data.sessionToken
    Cos.createTime = data.createTime
    Cos.expireTime = data.expireTime
  })
}
// StartTime: Cos.createTime, // 时间戳，单位秒，如：1580000000
// ExpiredTime: Cos.expireTime, // 时间戳，单位秒，如：1580000000
getCos()
const cos = new COS({
  getAuthorization: (_options, callback) => {
    const obj: any = {
      TmpSecretId: Cos.tmpSecretId,
      TmpSecretKey: Cos.tmpSecretKey,
      XCosSecurityToken: Cos.sessionToken,
      StartTime: Cos.createTime, // 开始时间戳，单位秒
      ExpiredTime: Cos.expireTime // 过期时间戳，单位秒
    }
    callback(obj)
  }
})


let opNull = ref<any>(null)
let queryForm = reactive<any>({
  id: null,
  status: null,
  giftType: null,
  giftClassType: null,
  applicationId: 'com.dongxin.app'
})

async function queryGifts() {
  let res = await queryGifts_api({
    // pageSize: pageSize.value,
    // pageNum: currentPage.value
    pageSize: 1000, // 设置一个足够大的值,确保能获取到所有礼物数
    pageNum: currentPage.value
  })
  let {data, total} = res.data
  data.forEach((v: any) => {
    if (v.giftType == 1) {
      v.giftTypeValue = "礼物面板"
    } else if (v.giftType == 2) {
      v.giftTypeValue = "搭讪礼物"
    } else if (v.giftType == 3) {
      v.giftTypeValue = "缘分礼物"
    } else if (v.giftType == 4) {
      v.giftTypeValue = "背包亲密度礼物"
    } else if (v.giftType == 5) {
      v.giftTypeValue = "视频免费助力礼物"
    } else if (v.giftType == 6) {
      v.giftTypeValue = "亲密度特权礼物"
    } else if (v.giftType == null) {
      v.giftTypeValue = "全部"
    }
  })
  giftsTab.value = data.filter((gift: any) => gift.title.includes(searchTerm.value))
  totalNum.value = total
}

queryGifts()
// 操作

const editGifts = (row: any) => {
  let {id, title, price, picUrl, svga, status, sort, remark, giftType, intimacyVal, intimacyLevel, giftClassType} = row
  editForm.id = id
  editForm.title = title
  editForm.price = price
  editForm.picUrl = picUrl
  editForm.svga = svga
  editForm.status = status
  editForm.sort = sort
  editForm.remark = remark
  editForm.giftType = giftType
  editForm.intimacyVal = intimacyVal
  editForm.intimacyLevel = intimacyLevel
  editForm.giftClassType = giftClassType
  isShow.value = true
}

const handleDelete = async (row: any) => {
  if (row.status === 'on') {
    alert('礼物上架中,不能删除');
  } else {
    await deleteGift_api({id: row.id});
    queryGifts();
  }
};

const editSubMit = async () => {
  await modifyGifts_api({...editForm})
  isShow.value = false
  querySubmit()
}

const addGifts = () => {
  addForm.title = ''
  addForm.intimacyVal = ''
  addForm.price = ''
  addForm.picUrl = ''
  addForm.svga = ''
  addForm.status = null
  addForm.sort = ''
  addForm.remark = ''
  addForm.giftType = null
  addForm.giftClassType = null
  addShow.value = true
}

const addSubMit = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  let {
    title,
    price,
    picUrl,
    svga,
    status,
    sort,
    remark,
    giftType,
    giftClassType,
    applicationId,
    intimacyVal,
    intimacyLevel
  } = addForm

  formEl.validate(async (valid, _fields) => {
    if (valid) {
      const giftData = {
        title,
        price,
        picUrl,
        svga,
        status,
        sort,
        remark,
        giftType,
        applicationId,
        intimacyVal: giftType === 4 || giftType === 5 ? intimacyVal : undefined,
        intimacyLevel: giftType === 6 ? intimacyLevel : undefined,
        giftClassType
      } as any

      await addGifts_api(giftData)
      addShow.value = false
      querySubmit()
    } else {
      return
    }
  })
}

// 上传图片

const getFile = (e: any) => {
  const file = e.target.files[0];
  handleFileInUploading(file)
}

const upload = () => {
  upImg.value?.click()
}

// cos上传
const handleFileInUploading = (file: any) => {
  cos.putObject({
    Bucket: 'prod-1309639790', /* 填写自己的 bucket，必须字段 */
    Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
    Key: 'gifts/' + file.name,              /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */
    StorageClass: 'STANDARD', //上传模式，标准
    Body: file, // 上传文件对象
    // SliceSize: 1024 * 1024 * 5,     /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */
    onProgress: function (progressData) {
      console.log(JSON.stringify(progressData));
    }
  }, function (err, data) {
    if (err) {
      console.log('上传失败', err);
    } else {
      editForm.picUrl = `https://${data.Location}`
      addForm.picUrl = `https://${data.Location}`
      // console.log(`https://${data.Location}`);
      console.log(addForm.picUrl);
      // console.log('上传成功', data);
    }
  });
}

// 上传文

const uploadFile = () => {
  upLoad.value?.click()
}

const upLod = (e: any) => {
  const File = e.target.files[0];
  cos.putObject({
    Bucket: 'prod-1309639790', /* 填写自己的 bucket，必须字段 */
    Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
    Key: 'gifts/' + File.name,              /* 存储在桶��的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */
    // StorageClass: 'STANDARD', //上传模式，标准
    Body: File, // 上传文件对象
    // SliceSize: 1024 * 1024 * 5,     /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */
    onProgress: function (progressData) {
      console.log(JSON.stringify(progressData));
    }
  }, function (err, data) {
    if (err) {
      console.log('上传失败', err);
    } else {
      editForm.svga = `https://${data.Location}`
      addForm.svga = `https://${data.Location}`
      // console.log('上传成功', data)
    }
  });
}


// 查询
async function querySubmit() {
  let res = await queryGifts_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let {data, total} = res.data
  data.forEach((v: any) => {
    if (v.giftType == 1) {
      v.giftTypeValue = "礼物面板"
    } else if (v.giftType == 2) {
      v.giftTypeValue = "搭讪礼物"
    } else if (v.giftType == 3) {
      v.giftTypeValue = "缘分礼物"
    } else if (v.giftType == 4) {
      v.giftTypeValue = "背包亲密度礼物"
    } else if (v.giftType == 5) {
      v.giftTypeValue = "视频免费助力礼物"
    } else if (v.giftType == 6) {
      v.giftTypeValue = "亲密度特权礼物"
    } else if (v.giftType == null) {
      v.giftTypeValue = "全部"
    }
  })
  giftsTab.value = data
  totalNum.value = total
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  queryGifts()
}
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  querySubmit();
}
const changeSelect1 = (e: string) => {
  addForm.applicationId = e;
}
</script>

<style lang="scss" scoped>
.gifts {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .card-header {
      display: flex;
      align-items: center;

      .el-form {
        // height: 32px;
        margin-left: 16px;
      }
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    width: 35%;
    min-width: 350px;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }

      .el-form-item__label {
        width: 100px;
      }
    }
  }

  .ml-3 {
    margin-left: 10px;
  }

  .avatar-uploader {
    width: 100px;
    height: 100px;
    box-sizing: border-box;
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }

    .upImg {
      display: none;
    }


    img {
      width: 100px;
      height: 100px;
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }
  }


  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    text-align: center;
  }
}
</style>