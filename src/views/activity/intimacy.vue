<template>
  <div class="intimacy">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button :icon="Plus" @click="addintimacylist">增加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="intimacyListTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="序号" fixed></el-table-column>
        <el-table-column prop="title" label="任务名称" fixed>
        </el-table-column>
        <el-table-column prop="type" label="任务奖励" fixed>
          <template #default="scope">
          {{ scope.row.type===0?"搭讪卡":'' }}
          </template>
        </el-table-column>
        <el-table-column prop="refreshType" label="刷新时间" fixed>
          <template #default="scope">
          {{ scope.row.refreshType?"每周一3:00刷新":'每日3:00刷新' }}
          </template>
        </el-table-column>
        <el-table-column prop="iconUrl" label="搭讪卡icon" min-width="150" fixed>
          <template #default="scope">
            <el-image v-if="scope.row.iconUrl" style="width: 100px; height: 100px" :src="scope.row.iconUrl" :preview-src-list="idcaBa"
              :initial-index="0" fit="cover" :preview-teleported="true" :hide-on-click-modal="true"
              @click="resIdcaBa(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="msgCardIconUrl" label="消息卡icon" min-width="150" fixed>
          <template #default="scope">
            <el-image  v-if="scope.row.msgCardIconUrl" style="width: 100px; height: 100px" :src="scope.row.msgCardIconUrl" :preview-src-list="idcaBa"
              :initial-index="0" fit="cover" :preview-teleported="true" :hide-on-click-modal="true"
              @click="resIdcaBa(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="enable" label="状态" fixed>
          <template #default="{row}">
            {{  row.enable?'上架':'下架'}}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editintimacy(scope.row)" size="small" type="primary" :icon="Edit">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="isShow" :activityName="addForm.id ? '修改活动物品' : '添加活动物品'" destroy-on-close @close="close"
        width="35%">
        <el-form ref="addFormRef" :model="addForm" label-width="100px" :rules="rules">
          <el-form-item prop="title" label="活动标题" fixed>
            <el-input v-model="addForm.title" style="width: 200px;"> </el-input>
          </el-form-item>
          <el-form-item prop="activityId" label="活动ID" fixed>
            <el-select v-model="addForm.activityId" placeholder="请选择" style="width: 200px;">
              <el-option :label="item.title" :value="item.id" v-for="item in act" :key="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="type" label="任务奖励" fixed>
            <el-select :disabled="addForm.id&&addForm.type!==null" v-model="addForm.type" placeholder="请选择" style="width: 200px;">
              <el-option label="搭讪卡" :value="0" />
              <el-option label="消息卡" :value="1" />
              <el-option label="搭讪卡+消息卡" :value="2" />
            </el-select>
         
          </el-form-item>
          <el-form-item v-if="addForm.type!==null&&addForm.type!==1" label="搭讪卡数量" prop="chatupCardCount">
          <el-input-number  :disabled="addForm.id&&addForm.type!==null" :controls="false" v-model="addForm.chatupCardCount" style="width: 200px;" placeholder="自定义数量" />
          </el-form-item>
          <el-form-item  v-if="addForm.type!==null&&addForm.type!==0" label="消息卡数量" prop="messageCardCount">
          <el-input-number :disabled="addForm.id&&addForm.type!==null"  :controls="false" v-model="addForm.messageCardCount" style="width: 200px;" placeholder="自定义数量" />
          </el-form-item>
          <el-form-item  v-if="addForm.type!==null&&addForm.type!==1" label="搭讪卡icon" prop="iconUrl">
            <CosUpload :imageUrl="addForm.iconUrl" @handleImagUrl="changeImgurl" />
          </el-form-item>
          <el-form-item  v-if="addForm.type!==null&&addForm.type!==0" label="消息卡icon" prop="msgCardIconUrl">
            <CosUpload :imageUrl="addForm.msgCardIconUrl" @handleImagUrl="changeImgurl1" />
          </el-form-item>
          <el-form-item prop="sendCount" label="达成条件" fixed>
           <el-input-number :controls="false"  v-model="addForm.sendCount " placeholder="累计赠送X个礼物"></el-input-number>
          </el-form-item>
          <el-form-item prop="refreshType" label="刷新时间" fixed>
            <el-select v-model="addForm.refreshType" placeholder="请选择" style="width: 200px;">
              <el-option label="每日3:00刷新" :value="0" />
              <el-option label="每周一3:00刷新" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item prop="enable" label="是否上架" fixed>
            <el-radio-group v-model="addForm.enable" class="ml-4">
              <el-radio :label="1">上架</el-radio>
              <el-radio :label="0">下架</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="orders" label="排序" fixed>
            <el-input-number v-model="addForm.orders" style="width: 200px;" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="addintimacy(addFormRef)">确认</el-button>
          </span>
        </template>
      </el-dialog>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Edit, Plus } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
import CosUpload from '@/components/CosUpload.vue'
import { query_intimacy_list, add_intimacy_list, modify_intimacy_list, query_act_list } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(getintimacylist);
let intimacyListTab = ref([]);
let addFormRef = ref<any>(null);
let act = ref<any>(null);
let isShow = ref(false)
let addForm = reactive<any>({
  activityId: null,
  title: null,
  type: null,
  chatupCardCount: null,
  messageCardCount:null,
  refreshType: null,
  sendCount: null,
  iconUrl: null,
  enable: 1,
  orders: null,
  msgCardIconUrl:null,
}
)
let idcaBa = ref<any>([])
const rules = reactive<any>({
  title: [{ required: true, message: "请输入活动标题", trigger: "blur" }],
  activityId: [{ required: true, message: "请选择活动ID", trigger: "change" }],
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  chatupCardCount: [{ required: true, message: "请输入数量", trigger: "blur" }],
  messageCardCount: [{ required: true, message: "请输入数量", trigger: "blur" }],
  refreshType: [{ required: true, message: "请选择刷新时间", trigger: "change" }],
  sendCount: [{ required: true, message: "请输入达成条件", trigger: "blur" }],
  iconUrl: [{ required: true, message: "请上传图片", trigger: "change" }],
  orders: [{ required: true, message: "请输入排序", trigger: "change" }],
  msgCardIconUrl:[{ required: true, message: "请输入排序", trigger: "change" }],
});


const getAct = async () => {
  let res = await query_act_list();
  act.value = res.data.data;
}
getAct();
async function getintimacylist() {
  let res = await query_intimacy_list({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { content } = res.data.data;
  intimacyListTab.value = content;
}

getintimacylist();

const close = () => {
  isShow.value = false;
  addForm = reactive<any>({
    activityId: null,
    title: null,
    type: null,
    chatupCardCount: null,
    messageCardCount:null,
    refreshType: null,
    sendCount: null,
    iconUrl: null,
    enable: 1,
    msgCardIconUrl:null,
    orders: null,
  })
}
const resIdcaBa = (row: any) => {
  idcaBa.value = []
  if (idcaBa.value.length == 0) {
    idcaBa.value.push(row.iconUrl)
  }
}
// 修改
const editintimacy = async (row: any) => {
  let data = row;
  data.enable = data.enable ? 1 : 0;
  addForm = reactive(JSON.parse(JSON.stringify(data)));
  isShow.value = true;
};


const changeImgurl = (e: string) => {
  addForm.iconUrl = e;
  addFormRef._rawValue.validateField('iconUrl');
}

const changeImgurl1 = (e: string) => {
  addForm.msgCardIconUrl = e;
  addFormRef._rawValue.validateField('msgCardIconUrl');
}

// 增加
const addintimacylist = () => {
  isShow.value = true
}

const addintimacy = async (_addFormRef: any) => {
  addFormRef._rawValue.validate(async (_valid: any) => {
    if (_valid) {
      if (!addForm.id) {
        await add_intimacy_list({ ...addForm })
        getintimacylist();
        isShow.value = false
      } else {
        await modify_intimacy_list({ ...addForm })
        getintimacylist();
        isShow.value = false
      }
    }
  })
}





</script>

<style lang="scss" scoped>
.intimacy {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }
  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
