<template>
  <div class="giftBoard">
    <div class="table-container">
      <el-table :data="giftListTab" style="width: 100%; height: 100%">
        <el-table-column prop="exchangeDate" label="日期" fixed></el-table-column>
        <el-table-column prop="giftCount" label="礼物兑换次数" fixed>
        </el-table-column>
        <el-table-column prop="pointCount" label="积分兑换次数" fixed>
        </el-table-column>
        <el-table-column prop="charmValCount" label="魅力值兑换次数" fixed>
        </el-table-column>

      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {gift_receive_list} from '@/api/index'

let giftListTab = ref<any>(null);

const query= async()=>{
 let res = await gift_receive_list();
 giftListTab.value=res.data.data;
  
}
query();
</script>

<style lang="scss" scoped>
.giftBoard {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .table-container {
    flex: 1;
    overflow: auto;
    min-height: 0;
  }
}
</style>

<style scoped>

</style>