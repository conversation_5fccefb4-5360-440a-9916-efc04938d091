<template>
  <div class="intimacyBoard">
    <div class="table-container">
      <el-table :data="intimacyListTab" style="width: 100%; height: 100%">
       <el-table-column prop="receiveDate" label="日期" fixed></el-table-column>
       <el-table-column prop="fiveCount" label="累计送5个亲密度礼物/人" fixed>
       </el-table-column>
       <el-table-column prop="fifteenCount" label="累计送15个亲密度礼物" fixed>
       </el-table-column>
       <el-table-column prop="hundredCount" label="累计送100个亲密度礼物" fixed>
       </el-table-column>
     </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {intimacy_receive_list} from '@/api/index'

let intimacyListTab = ref([]);

const query= async()=>{
let res = await intimacy_receive_list();
intimacyListTab.value=res.data.data;
 
}
query();
</script>

<style lang="scss" scoped>
.intimacyBoard {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .table-container {
    flex: 1;
    overflow: auto;
    min-height: 0;
  }
}
</style>

<style scoped>

</style>