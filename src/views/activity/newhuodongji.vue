<template>
  <div class="face">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="物品类型" prop="gender">
              <el-select v-model="queryForm.gender" placeholder="请选择物品类型">
                <el-option label="全部" value=""></el-option>
                <el-option label="实物" value="1"></el-option>
                <el-option label="积分" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="faceRef" :data="faceData" style="width: 100%; height: 100%">
          <el-table-column label="用户ID" width="120" fixed>
            <template #default="scope">{{ scope.row.userId }}</template>
          </el-table-column>
          <el-table-column label="获得奖励" min-width="120">
            <template #default="scope">{{ scope.row.goods }}</template>
          </el-table-column>
          <el-table-column label="姓名" min-width="200">
            <template #default="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="性别" min-width="100">
            <template #default="scope">
              <span v-if="scope.row.gender === 1">男</span>
              <span v-if="scope.row.gender === 2">女</span>
            </template>
          </el-table-column>
          <el-table-column label="电话" min-width="200">
            <template #default="scope">{{ scope.row.mobile }}</template>
          </el-table-column>
          <el-table-column label="详细地址" min-width="200">
            <template #default="scope">{{ scope.row.address }}</template>
          </el-table-column>
          <el-table-column label="快递" min-width="120">
            <template #default="scope">{{ scope.row.company }}</template>
          </el-table-column>
          <el-table-column label="运单号" min-width="120">
            <template #default="scope">{{ scope.row.waybill }}</template>
          </el-table-column>
          <el-table-column label="领取时间" min-width="200">
            <template #default="scope">{{ scope.row.createTime }}</template>
          </el-table-column>
          <el-table-column label="状态" min-width="120">
            <template #default="scope">
              <span v-if="scope.row.status === 1">⚪️可领取</span>
              <span v-if="scope.row.gender === 2 && scope.row.status === 2">🔵已领取</span>
              <span v-if="scope.row.gender === 1 && scope.row.status === 2">⚪待发货</span>
              <span v-if="scope.row.status === 3">🟢已发货</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="120">
            <template #default="scope">
              <el-button type="primary" size="small" @click="openDialog(scope.row)">
                发快递
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>

    <!-- 发快递弹窗 -->
    <el-dialog v-model="dialogVisible" title="发快递" width="30%">
      <el-form :model="expressForm">
        <el-form-item label="快递公司" :label-width="formLabelWidth">
          <el-select v-model="expressForm.company" placeholder="请选择快递公司">
            <el-option label="邮政（EMS)" value="邮政（EMS)"></el-option>
            <el-option label="顺丰" value="顺丰"></el-option>
            <el-option label="申通" value="申通"></el-option>
            <el-option label="圆通" value="圆通"></el-option>
            <el-option label="韵达" value="韵达"></el-option>
            <el-option label="中通" value="中通"></el-option>
            <el-option label="京东" value="京东"></el-option>
            <el-option label="极兔" value="极兔"></el-option>
            <el-option label="德邦" value="德邦"></el-option>
            <el-option label="百世快运" value="百世快运"></el-option>
            <el-option label="优速物流" value="优速物流"></el-option>
            <el-option label="其他" value="其他"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="快递单号" :label-width="formLabelWidth">
          <el-input v-model="expressForm.waybill" autocomplete="off"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitExpress">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import pageHook from "@/hooks/pageHook";
import {query_kuaidi_list, query_fakuaidi_list} from "@/api/authentication";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(querySubmit);
let faceData = ref([]); // 表格数据
const queryForm = reactive<any>({
  userId: null,
  gender: ''
});

// 发快递相关
const dialogVisible = ref(false)
const expressForm = reactive({
  id: '',
  company: '',
  waybill: ''
})
const formLabelWidth = '140px'

// 请求列表数据
async function getfaceList() {
  let res = await query_kuaidi_list({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    userId: queryForm.userId,
    gender: queryForm.gender
  });

  faceData.value = res.data?.data || [];
  totalNum = res.data?.total || 0;
}

getfaceList();

// 查询
async function querySubmit() {
  let {userId, gender} = queryForm;
  let res = await query_kuaidi_list({
    userId,
    gender,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });

  faceData.value = res.data?.data || [];
  totalNum = res.data?.total || 0;
};

const onreset = () => {
  queryForm.userId = null;
  queryForm.gender = '';
  getfaceList();
};

// 发快递
interface RowData {
  id: string;
  gender: number;
}

const openDialog = (row: RowData) => {
  expressForm.id = row.id;
  expressForm.company = '';
  expressForm.waybill = '';
  dialogVisible.value = true;
};

const submitExpress = async () => {
  await query_fakuaidi_list(expressForm)
  dialogVisible.value = false
  getfaceList() // 刷新列表
}
</script>

<style scoped lang="scss">
.face {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .card-header {
      display: flex;
      align-items: center;
      width: 100%;

      .el-form {
        width: 100%;
      }
    }
  }
}
</style>