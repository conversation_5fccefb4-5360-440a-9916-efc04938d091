<template>
  <div class="activity">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <majaSelect :applicationId="applicationId" @changeSelect="changeSelect"/>
          <el-button :icon="Plus" @click="addReqlylist">增加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="reqlyListTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="序号" fixed></el-table-column>
        <el-table-column prop="title" label="活动标题" fixed>
        </el-table-column>
        <el-table-column prop="beginTime" label="活动开始时间" fixed></el-table-column>
        <el-table-column prop="endTime" label="结束时间" fixed></el-table-column>
        <el-table-column prop="orders" label="排序" width="80" fixed></el-table-column>
        <el-table-column prop="coverImage" label="活动封面" width="300" fixed>
          <template #default="scope">
            <el-image  style="height: 100px;" :src="scope.row.coverImage" :preview-src-list="idcaBa"
              :initial-index="0" fit="cover" :preview-teleported="true" :hide-on-click-modal="true"
              @click="resIdcaBa(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="image" label="详情页封面" width="300" fixed>
          <template #default="scope">
            <el-image  style="height: 100px;" :src="scope.row.image" :preview-src-list="idcaBa"
              :initial-index="0" fit="cover" :preview-teleported="true" :hide-on-click-modal="true"
              @click="resIdcaBa(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="jumpUrl" label="活动URL" width="80" fixed></el-table-column>
        <el-table-column prop="enable" label="活动状态" fixed>
          <template #default="scope">
            {{ scope.row.enable ? "上架" : "下架" }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editreqly(scope.row)" size="small" type="primary" :icon="Edit">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="isShow" :title="addForm.id ? '修改活动' : '添加活动'" destroy-on-close @close="close" width="35%">
        <el-form ref="addFormRef" :model="addForm" label-width="100px" :rules="rules">
          <el-form-item label="马甲包">
              <majaSelect :applicationId="addForm.applicationId" @changeSelect="changeSelect1" :disabled="addForm.id"/>
            </el-form-item>
          <el-form-item prop="title" label="活动主题" fixed>
            <el-input v-model="addForm.title" style="width: 200px;" />
          </el-form-item>
          <el-form-item label="是否永久" prop="forever">
            <el-radio-group v-model="addForm.forever" class="ml-4">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="beginTime" label="开始时间" fixed v-if="!addForm.forever">
            <el-date-picker v-model="addForm.beginTime" type="datetime" placeholder="请选择" format="YYYY/MM/DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss" />
          </el-form-item>
          <el-form-item prop="endTime" label="结束时间" fixed  v-if="!addForm.forever">
            <el-date-picker v-model="addForm.endTime" type="datetime" placeholder="请选择" format="YYYY/MM/DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss " />
          </el-form-item>
          <el-form-item label="活动封面" prop="coverImage">
            <CosUpload :imageUrl="addForm.coverImage" @handleImagUrl="changeImgurl" />
          </el-form-item>
          <el-form-item label="详情页封面" prop="image">
            <CosUpload :imageUrl="addForm.image" @handleImagUrl="changeImage" />
          </el-form-item>
          <el-form-item prop="jumpUrl" label="活动URL" fixed>
            <el-input v-model="addForm.jumpUrl" style="width: 200px;" />
          </el-form-item>
          <el-form-item label="排版" prop="type">
            <el-radio-group v-model="addForm.type" class="ml-4">
              <el-radio :label="0">全屏</el-radio>
              <el-radio :label="1">半屏</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="enable" label="是否上架" fixed>
            <el-radio-group v-model="addForm.enable" class="ml-4">
              <el-radio :label="1">上架</el-radio>
              <el-radio :label="0">下架</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="orders" label="排序" fixed>
            <el-input-number v-model="addForm.orders" style="width: 200px;" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="addreqly(addFormRef)">确认</el-button>
          </span>
        </template>
      </el-dialog>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Edit, Plus } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
import CosUpload from '@/components/CosUpload.vue'
import {convertDateFormat} from '@/utils/date'
import { query_activity_list, add_activity_list, modify_activity_list } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(getreqlylist);
let reqlyListTab = ref([]);
let applicationId=ref('com.dongxin.app')
let addFormRef = ref<any>(null);
let isShow = ref(false)
let idcaBa = ref<any>([])
let addForm = reactive<any>({
  title: null,
  orders: null,
  coverImage: null,
  beginTime: null,
  endTime: null,
  enable: 1,
  image:null,
  jumpUrl:null,
  type: 1,
  forever:0,
  applicationId:'com.dongxin.app'
}
)

const rules = reactive<any>({
  title: [{ required: true, message: "活动标题不能为空", trigger: "blur" }],
  endTime: [{ required: true, message: "结算不能为空", trigger: "change" }],
  beginTime: [{ required: true, message: "开始不能为空", trigger: "change" }],
  coverImage: [{ required: true, message: "请上传图片", trigger: "change" }],
  image: [{ required: true, message: "请上传图片", trigger: "change" }],
  orders: [{ required: true, message: "请输入排序", trigger: "blur" }],
  jumpUrl:[{ required: true, message: "请输入活动URL", trigger: "change" }]
});
const resIdcaBa = (row: any) => {
  idcaBa.value = []
  if (idcaBa.value.length == 0) {
    idcaBa.value.push(row.coverImage)
  }
}



async function getreqlylist() {
  let res = await query_activity_list({
    applicationId:applicationId.value,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data } = res.data;
  data.content.forEach((item:any) => {
    item.endTime=item.endTime?convertDateFormat(item.endTime):'永久'
    item.beginTime=item.beginTime?convertDateFormat(item.beginTime):'永久'
  });
  reqlyListTab.value = data.content;
  totalNum.value = data.totalElements;
}

getreqlylist();

const close = () => {
  isShow.value = false;
  addForm = reactive<any>({
    title: null,
    orders: null,
    coverImage: null,
    beginTime: null,
    endTime: null,
    enable: 1,
    jumpUrl:null,
    type:1,
    forever:0,
    image:null,
  })
}


const changeImgurl = (e: string) => {
  addForm.coverImage = e;
  addFormRef._rawValue.validateField('coverImage');
}
const changeImage = (e: string) => {
  addForm.image = e;
  addFormRef._rawValue.validateField('image');
}

// 修改
const editreqly = async (row: any) => {
  let data=row;
  data.enable=data.enable?1:0;
  data.forever=data.forever?1:0;
  addForm =reactive(JSON.parse( JSON.stringify(data)));
  isShow.value = true;
};



// 增加
const addReqlylist = () => {
  isShow.value = true
}

const addreqly = async (_addFormRef: any) => {
  addFormRef._rawValue.validate(async (_valid: any) => {
    if(_valid){
      if (!addForm.id) {
      await add_activity_list({ ...addForm })
      getreqlylist();
      isShow.value = false
    } else {
      let data = addForm;
        data.endTime=data.endTime== '永久'?'': data.endTime;
        data.beginTime=data.beginTime=='永久'?'': data.beginTime;
      await modify_activity_list({ ...addForm })
      getreqlylist();
      isShow.value = false
    }
    }
  })
}
const changeSelect=(e: string)=>{
  applicationId.value=e;
  getreqlylist();
}
const changeSelect1=(e:string)=>{
  addForm.applicationId=e;

}



</script>

<style lang="scss" scoped>
.activity {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }
  :deep(.el-dialog) {
    width: 35%;
    .el-form {
      width: 100%;
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
