<template>
  <div class="exchangeProp">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button :icon="Plus" @click="addReqlylist">增加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="reqlyListTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="序号" fixed></el-table-column>
        <el-table-column prop="activityName" label="活动名称" fixed>
        </el-table-column>
        <el-table-column prop="limitationType" label="限制类型" fixed>
          <template #default="{ row }">
            {{ row.limitationType === 1 ? '每日限兑' : row.limitationType === 2 ? '每周限兑' : row.limitationType ===3 ? '每日限兑' : '无限制' }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="兑换类型" fixed>
          <template #default="{ row }">
            {{ row.type === 1 ? '魅力值' : row.type === 2 ? '礼物' : '积分' }}
          </template>
        </el-table-column>
        <el-table-column prop="point" label="兑换物品" fixed></el-table-column>
        <el-table-column prop="iconUrl" label="物品icon" min-width="150" fixed>
          <template #default="scope">
            <el-image style="width: 100px; height: 100px" :src="scope.row.iconUrl" :preview-src-list="idcaBa"
              :initial-index="0" fit="cover" :preview-teleported="true" :hide-on-click-modal="true"
              @click="resIdcaBa(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="orders" label="限制次数" fixed></el-table-column>
        <el-table-column prop="enable" label="状态" fixed>
          <template #default="scope">
            {{ scope.row.enable ? "上架" : "下架" }}
          </template>
        </el-table-column>
        <el-table-column prop="orders" label="排序" fixed></el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editreqly(scope.row)" size="small" type="primary" :icon="Edit">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="isShow" :activityName="addForm.id ? '修改活动物品' : '添加活动物品'" destroy-on-close @close="close"
        width="35%">
        <el-form ref="addFormRef" :model="addForm" label-width="100px" :rules="rules">
          <el-form-item prop="activityId" label="活动ID" fixed>
            <el-select v-model="addForm.activityId" placeholder="请选择" style="width: 200px;">
              <el-option :label="item.title" :value="item.id" v-for="item in act" :key="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="type" label="类型" fixed>
            <el-select :disabled="addForm.id" v-model="addForm.type" placeholder="请选择" style="width: 200px;">
              <el-option label="积分" :value="0" />
              <el-option label="魅力值" :value="1" />
              <el-option label="礼物" :value="2" />
            </el-select>
          </el-form-item>

          <el-form-item :prop="addForm.type === 0 ? 'point' : addForm.type === 1 ? 'charmVal' : 'giftId'"
            :label="addForm.type === 0 ? '积分' : addForm.type === 1 ? '魅力值' : '礼物ID'" fixed>
            <el-input v-model="addForm.point" style="width: 200px;" v-if="addForm.type === 0" />
            <el-input v-model="addForm.charmVal" style="width: 200px;" v-else-if="addForm.type === 1" />
            <el-input-number v-model="addForm.giftId" style="width: 200px;" v-else-if="addForm.type === 2"  :controls="false"/>
          </el-form-item>
          <el-form-item prop="limitationType" label="限制类型" fixed>
            <el-select v-model="addForm.limitationType" placeholder="请选择" style="width: 200px;">
              <el-option label="无限制" :value="0" />
              <el-option label="每日限兑" :value="1" />
              <el-option label="每周限兑" :value="2" />
              <el-option label="每月限兑" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item prop="limitationCount" label="限制次数" fixed>
            <el-input-number v-model="addForm.limitationCount" style="width: 80px;" :controls="false" />
          </el-form-item>
          <el-form-item label="物品icon" prop="iconUrl">
            <CosUpload :imageUrl="addForm.iconUrl" @handleImagUrl="changeImgurl" />
          </el-form-item>

          <el-form-item prop="orders" label="排序" fixed>
            <el-input-number v-model="addForm.orders" style="width: 200px;" />
          </el-form-item>
          <el-form-item prop="enable" label="是否上架" fixed>
            <el-radio-group v-model="addForm.enable" class="ml-4">
              <el-radio :label="1">上架</el-radio>
              <el-radio :label="0">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="addreqly(addFormRef)">确认</el-button>
          </span>
        </template>
      </el-dialog>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Edit, Plus } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
import CosUpload from '@/components/CosUpload.vue'
import { prop_query_list, add_prop_list, modify_prop_list, query_act_list } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(getreqlylist);
let reqlyListTab = ref([]);
let addFormRef = ref<any>(null);
let act = ref<any>(null);
let isShow = ref(false)
let idcaBa = ref<any>([])
let addForm = reactive<any>({
  orders: null,
  iconUrl: null,
  enable: 1,
  type: 0,
  limitationType: 0,
  limitationCount: 0,
}
)

const rules = reactive<any>({
  activityId: [{ required: true, message: "请选择活动ID", trigger: "change" }],
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  point: [{ required: true, message: "请输入积分", trigger: "change" }],
  charmVal: [{ required: true, message: "请输入魅力值", trigger: "change" }],
  giftId: [{ required: true, message: "请输入礼物", trigger: "change" }],
  iconUrl: [{ required: true, message: "请上传图片", trigger: "change" }],
  orders: [{ required: true, message: "请输入排序", trigger: "change" }],
});
const resIdcaBa = (row: any) => {
  idcaBa.value = []
  if (idcaBa.value.length == 0) {
    idcaBa.value.push(row.iconUrl)
  }
}

const getAct = async () => {
  let res = await query_act_list();
  act.value = res.data.data;
}
getAct();
async function getreqlylist() {
  let res = await prop_query_list({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  reqlyListTab.value = data;
  totalNum.value = total;
}

getreqlylist();

const close = () => {
  isShow.value = false;
  addForm = reactive<any>({
    orders: null,
    iconUrl: null,
    enable: 1,
    type: 0,
    limitationType: 0,
  })
}


const changeImgurl = (e: string) => {
  addForm.iconUrl = e;
  addFormRef._rawValue.validateField();
}

// 修改
const editreqly = async (row: any) => {
  let data = row;
  data.enable = data.enable ? 1 : 0;
  addForm = reactive(JSON.parse(JSON.stringify(data)));

  isShow.value = true;
};



// 增加
const addReqlylist = () => {
  isShow.value = true
}

const addreqly = async (_addFormRef: any) => {
  addFormRef._rawValue.validate(async (_valid: any) => {
    if (_valid) {
      if (!addForm.id) {
        await add_prop_list({ ...addForm })
        getreqlylist();
        isShow.value = false
      } else {
        await modify_prop_list({ ...addForm })
        getreqlylist();
        isShow.value = false
      }
    }
  })
}





</script>

<style lang="scss" scoped>
.exchangeProp {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }
  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
