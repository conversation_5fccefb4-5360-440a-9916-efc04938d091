<template>
  <div class="dashboard" v-loading="loading" element-loading-text="加载中">
    <div class="dashboard-top">
      <majaSelect :applicationId="applicationId" :skipFirstSelect="false" 
                  @changeSelect="handleChange"/>
      
      <el-button :icon="Refresh" type="primary" @click="handleRefresh()">刷新</el-button>
      <span style="margin-left: 20px"> 刷新时间：{{ showTime }}</span>
    </div>

    <div class="dashboard-box">
      <div class="dashboard-box-column">
        <span class="title"> 今日 </span>
        <span class="colon">:</span>
        <div class="dashboard-box-column-right">
          <div
            class="column-item"
            style="margin: 4px 20px"
            v-for="(item, index) in todayData"
            :key="index"
          >
            <span>{{ item.text }} : {{ today[item.data] }}</span>
            <p style="font-size: 12px; margin-top: 3px">{{ item.text2 }}{{today[item.data2] }}&nbsp;&nbsp;{{ item.text3 }}{{today[item.data3] }}</p>
          </div>
        </div>
      </div>

      <div class="dashboard-box-column" style="padding-top: 30px;">
        <span class="title"> 昨天 </span>
        <span class="colon">:</span>
        <div class="dashboard-box-column-right">
          <div class="column-item" v-for="(item, index) in yesterdayData" :key="index">
            <span>{{ item.text }} : {{ yesterday[item.data] }}</span>
            <p style="font-size: 12px; margin-top: 3px" v-if="item.text2">{{ item.text2 }}{{yesterday[item.data2] }}&nbsp;&nbsp;{{ item.text3 }}{{yesterday[item.data3] }}</p>
          </div>
        </div>
      </div>

      <div class="dashboard-box-column" >
        <span class="title"> 总数据 </span>
        <span class="colon">:</span>
        <div class="dashboard-box-column-right">
          <div class="column-item" v-for="(item, index) in sumData" :key="index">
            <span>{{ item.text }} : {{ total[item.data] }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="dashboard-person">
      <p style="margin-bottom: 10px">实时在线人数：{{ online.onlineCnt }}</p>
      <div>
        <span style="font-size: 12px; margin-right: 20px"
          >男占比：{{ online.proportionOfMale }}%</span
        >
        <span style="font-size: 12px">女占比：{{ online.proportionOfFemale }}%</span>
      </div>
      <div style="margin-top: 10px">
        <span style="font-size: 14px">注册人数 {{ registerData.register }} 回复 {{ registerData.reply }}</span>
        <p style="font-size: 12px; margin-top: 3px">回复率：{{ ((registerData.reply / registerData.register) * 100).toFixed(2) }}%</p>
      </div>
    </div>
    <div class="dashboard-bottom">
      <el-descriptions :column="4" style="width: 1200px">
        <el-descriptions-item
          v-for="item in onlineData4"
          :key="item.data"
          :label="item.text"
          ><span @click="handleClick(item.dataList)" style="cursor: pointer">{{
            online[item.data]
          }}</span></el-descriptions-item
        >
      </el-descriptions>
    </div>
    <br />
    <div class="dashboard-bottom dashboard-online">
      <div   
      class="dashboard-online-item"
        v-for="item in onlineData5"
        :key="item.data"
        >
      <span style="cursor: pointer;" @click="handleClick(item.dataList)" >   {{item.text}} {{online[item.data] }}</span>
      <span style="cursor: pointer;" @click="handleClick(item.dataList2)">  {{item.text2}} {{online[item.data2] }}</span>
      </div>
    </div>
    <el-dialog
      v-model="isShow"
      title="在线人ID"
      destroy-on-close
      @close="isShow = 0"
      width="15%"
      custom-class="menu-dialog-height"
    >
      <el-table :data="tabData" max-height="500px">
        <el-table-column prop="userId" label="ID">
          <template #default="scope">
            <span @click="handleTo(scope.row)" style="cursor: pointer">
              {{ scope.row }}</span
            ></template
          >
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { queryhome, queryRegister } from "@/api/index";
import { ref } from "vue";
import { useRouter } from "vue-router";
import { time } from "@/utils/date";
import { Refresh } from "@element-plus/icons-vue";
import userIdStore from "@/store/userIdStore";
import majaSelect from "@/components/majaSelect.vue";
let store = userIdStore();
let online = ref<any>({});
let today = ref<any>({});
let router = useRouter();
let loading = ref<boolean>(false);
let showTime = ref<any>("");
let yesterday = ref<any>({});
let total = ref<any>({});
let applicationId = ref<string>("com.dongxin.app");
let tabData = ref<any>([]);
let isShow = ref<any>(false);
let todayData = ref<any>([
  { text: "激活数", data: "activeCnt" },
  { text: "注册数", data: "registerCnt",text2: "男:",text3: "女:",data2:"registerCntMale",data3:"registerCntFemale",},
  { text: "注册率", data: "registerRate" },
  { text: "新增付费人数", data: "newPayCnt", text2: "注册当天" },
  { text: "新增付费金额", data: "newPayAmount", text2: "注册当天" },
  { text: "新增付费金额(前5笔)", data: "newPayAmountFirst5" },
  { text: "新增付费率", data: "newPayRate", text2: "注册当天" },
  { text: "新arpu", data: "newArpu" },
  { text: "新arppu", data: "newArppu" },
  { text: "老用户付费人数", data: "oldPayCnt" },
  { text: "老用户付费金额", data: "oldPayAmount" },
  { text: "总付费金额", data: "payAmount" },
]);
let yesterdayData = ref<any>([
  { text: "激活数", data: "activeCnt" },
  { text: "注册数", data: "registerCnt" ,text2: "男:",text3: "女:",data2:"registerCntMale",data3:"registerCntFemale",},
  { text: "注册率", data: "registerRate" },
  { text: "新增付费人数", data: "newPayCnt" },
  { text: "新增付费金额", data: "newPayAmount" },
  { text: "新增付费金额(前5笔)", data: "newPayAmountFirst5" },
  { text: "新增付费率", data: "newPayRate" },
  { text: "新arpu", data: "newArpu" },
  { text: "新arppu", data: "newArppu" },
  { text: "老用户付费人数", data: "oldPayCnt" },
  { text: "老用户付费金额", data: "oldPayAmount" },
  { text: "总付费金额", data: "payAmount" },
]);
let sumData = ref<any>([
  { text: "总用户", data: "registerCnt" },
  { text: "总男用户", data: "registerMaleCnt" },
  { text: "总女用户", data: "registerFemaleCnt" },
  { text: "总流水", data: "payAmount" },
  { text: "注册转换率", data: "registerConversion" },
  { text: "平均月活跃", data: "avgMonthActive" },
  { text: "月留存", data: "monthRetention" },
]);
let onlineData4 = ref<any>([
  { text: "男用户实时在线人数:", data: "maleOnline", dataList: "maleOnlineList" },
  { text: "付费男实时在线人数:", data: "payMaleOnline", dataList: "payMaleOnlineList" },
  { text: "新用户男实时在线人数:", data: "newMaleOnline", dataList: "newMaleOnlineList" },
  {
    text: "未付费男实时在线人数:",
    data: "unPayMaleOnline",
    dataList: "unPayMaleOnlineList",
  },
  { text: "男用户实时空闲人数:", data: "maleIdle", dataList: "maleIdleList" },
  { text: "付费男实时空闲人数:", data: "payMaleIdle", dataList: "payMaleIdleList" },
  { text: "新用户男实时空闲人数:", data: "newMaleIdle", dataList: "newMaleIdleList" },
  { text: "未付费男实时空闲人数:", data: "unPayMaleIdle", dataList: "unPayMaleIdleList" },
]);
let onlineData5 = ref<any>([
  { text: "女用户实时在线人数:", data: "femaleOnline", dataList: "femaleOnlineList" },
  { text: "S级女实时在线人数:",text2: "优女用户实时在线人数:", data: "sFemaleOnline",data2:"oneFemaleOnline", dataList: "sFemaleOnlineList", dataList2:"oneFemaleOnlineList"},
  { text: "A级女实时在线人数:",text2: "良女用户实时在线人数:", data: "aFemaleOnline", data2:"twoFemaleOnline",dataList: "aFemaleOnlineList", dataList2:"twoFemaleOnlineList"},
  { text: "B1级女实时在线人数:", text2: "中女用户实时在线人数:",data: "b1FemaleOnline",data2:"threeFemaleOnline", dataList: "b1FemaleOnlineList", dataList2:"threeFemaleOnlineList"},
  { text: "B2级女实时在线人数:",text2: "差女用户实时在线人数:", data: "b2FemaleOnline",data2:"fourFemaleOnline", dataList: "b2FemaleOnlineList",dataList2:"fourFemaleOnlineList" },
  { text: "C级女实时在线人数:",text2: "极差女用户实时在线人数:", data: "cFemaleOnline",data2:"fiveFemaleOnline", dataList: "cFemaleOnlineList",dataList2: "fiveFemaleOnlineList" },
  { text: "女用户实时空闲人数:", data: "femaleIdle", dataList: "femaleIdleList" },
  { text: "S级女实时空闲人数:",text2: "优女用户实时空闲人数:", data: "sFemaleIdle",data2:"oneFemaleIdle", dataList: "sFemaleIdleList",dataList2:"oneFemaleIdleList" },
  { text: "A级女实时空闲人数:", text2: "良女用户实时空闲人数:",data: "aFemaleIdle",data2:"twoFemaleIdle", dataList: "aFemaleIdleList",dataList2:"twoFemaleIdleList" },
  { text: "B1级女实时空闲人数:", text2: "中女用户实时空闲人数:",data: "b1FemaleIdle",data2:"threeFemaleIdle", dataList: "b1FemaleIdleList",dataList2:"threeFemaleIdleList" },
  { text: "B2级女实时空闲人数:",text2: "差女用户实时空闲人数:", data: "b2FemaleIdle",data2:"fourFemaleIdle", dataList: "b2FemaleIdleList",dataList2:"fourFemaleIdleList" },
  { text: "C级女实时空闲人数:", text2: "极差女用户实时空闲人数:",data: "cFemaleIdle",data2:"fiveFemaleIdle",dataList: "cFemaleIdleList",dataList2: "fiveFemaleIdleList" },
]);
let registerData = ref<any>({
  reply: 0,
  register: 0
});

const queryTop = () => {
  queryhome({ mode: 1, applicationId: applicationId.value }).then(
    ({ data: { data } }: any) => {
      loading.value = false;
      data.today.newArpu = data.today.newArpu + "元";
      data.today.newArppu = data.today.newArppu + "元";
      data.today.newPayAmount = data.today.newPayAmount + "元";
      data.today.registerRate = (data.today.registerRate * 100).toFixed(2) + "%";
      data.today.newPayRate = (data.today.newPayRate * 100).toFixed(2) + "%";
      data.yesterday.newArpu = data.yesterday.newArpu + "元";
      data.yesterday.newPayAmount = data.yesterday.newPayAmount + "元";
      data.yesterday.newArppu = data.yesterday.newArppu + "元";
      data.yesterday.registerRate = (data.yesterday.registerRate * 100).toFixed(2) + "%";
      data.yesterday.newPayRate = (data.yesterday.newPayRate * 100).toFixed(2) + "%";
      data.total.registerConversion =
        (data.total.registerConversion * 100).toFixed(2) + "%";
      data.total.payAmount = data.total.payAmount + "元";
      data.total.monthRetention = (data.total.monthRetention * 100).toFixed(2) + "%";
      today.value = data.today;
      yesterday.value = data.yesterday;
      total.value = data.total;
    },
    (_e) => {
      loading.value = false;
    }
  );
};
const queryBottom = async () => {
  queryhome({ mode: 2, applicationId: applicationId.value }).then(
    ({ data: { data } }: any) => {
      let d = new Date().getTime();
      showTime.value = time(d / 1000);
      data.online.proportionOfMale = (data.online.proportionOfMale * 100).toFixed(2);
      data.online.proportionOfFemale = (data.online.proportionOfFemale * 100).toFixed(2);
      online.value = data.online;
    }
  );
};

const queryRegisterData = () => {
  queryRegister(applicationId.value).then(
    ({ data: { data } }: any) => {
      registerData.value = data;
    }
  );
};

const handleTo = (e: number) => {
  store.userId = e;
  router.push({
    path: "/foreground_user/foregrounduser",
  });
};
const handleChange = (e: string) => {
  applicationId.value = e;
  queryTop();
  queryBottom();
  queryRegisterData();
};

const handleRefresh = () => {
  queryTop();
  queryBottom();
  queryRegisterData();
};

queryTop();
queryBottom();
queryRegisterData();

const handleClick = (e: any) => {
  isShow.value = true;
  tabData.value = JSON.parse(JSON.stringify(online.value[e]));
};
</script>

<style lang="scss" scoped>
.dashboard {
  padding-left: 30px;
  background: rgb(243, 244, 247);
  padding-top: 20px;
  padding-bottom: 20px;
  &-top{
    display: flex;
    align-items: center;
  }

  &-bottom {
    box-shadow: 0 2px 4px 0 rgba(54, 58, 80, 0.32);
    width: 1230px;
    background: #fff;
    padding-left: 40px;
    padding-top: 20px;
  }
  &-online{
    display: grid;
    grid-template-columns: 200px  200px  200px  200px  200px  200px ;
    grid-template-rows: 60px  60px;
  
    &-item{
      display: flex;
      justify-content: center;
      /* align-items: center; */
      font-size: 14px;
      line-height: 28px;
      flex-direction: column;
      text-align: left;
      color: rgb(0,0,0,0.9);
    }
  }

  &-box {
    width: 80%;
    height: 400px;
    /* border: 1px solid #333; */
    background: #fff;
    box-shadow: 0 2px 4px 0 rgba(54, 58, 80, 0.32);
    margin-top: 20px;
    padding: 20px;

    &-column {
      display: flex;
      margin: 40px auto;
      height: 80px;
      align-items: center;

      .title {
        display: flex;
        font-weight: 600;
        width: 90px;
        line-height: 30px;
        font-size: 25px;
      }

      .colon {
        font-size: 30px;
        margin-left: 20px;
      }

      &-right {
        display: flex;
        flex-wrap: wrap;

        .column-item {
          margin: 10px 20px;
          color: #333;
          min-width: 80px;
        }
      }
    }
  }

  &-person {
    margin: 50px auto;
    width: 300px;
    margin-left: 30%;
    height: 100px;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-shadow: 0 2px 4px 0 rgba(54, 58, 80, 0.32);
  }
}
.menu-dialog-height {
  height: 704px;
}
</style>
