<template>
  <div class="realname">
    <!--    //实名认证-->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <!-- <el-form-item label="ID" prop="id">
              <el-input v-model="queryForm.id" />
            </el-form-item> -->
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" :skipFirstSelect="true"
                          @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="实名" prop="realName">
              <el-input v-model="queryForm.realName"/>
            </el-form-item>
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="queryForm.idCard"/>
            </el-form-item>
            <el-form-item label="认证状态" prop="state">
              <el-select class="header-select" v-model="queryForm.state" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="未提交" :value="0"/>
                <el-option label="认证审核中" :value="1"/>
                <el-option label="认证成功" :value="2"/>
                <el-option label="认证失败" :value="3"/>
                <el-option label="不允许再认证" :value="4"/>
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryRealname">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="RealnameTab" style="width: 100%; height: 100%">
          <el-table-column prop="id" label="ID" min-width="60" fixed></el-table-column>
          <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
          <el-table-column prop="realName" label="实名" min-width="120"></el-table-column>
          <el-table-column prop="idCard" label="身份证号" min-width="140"></el-table-column>
          <el-table-column prop="state" label="认证状态" min-width="120">
            <template #default="scope">{{ scope.row.state }}</template>
          </el-table-column>
          <el-table-column prop="idCardFront" label="身份证正面" min-width="100">
            <template #default="scope">
              <el-image style="width: 70px; height: 70px" :src="scope.row.idCardFront" :zoom-rate="1.2"
                        :preview-src-list="idcaFr" :initial-index="0" fit="cover" :preview-teleported="true"
                        :hide-on-click-modal="true" @click="resIdcaFr(scope.row)"/>
            </template>
          </el-table-column>
          <el-table-column prop="idCardBackend" label="身份证背面" min-width="100">
            <template #default="scope">
              <el-image style="width: 70px; height: 70px" :src="scope.row.idCardBackend" :zoom-rate="1.2"
                        :preview-src-list="idcaBa" :initial-index="0" fit="cover" :preview-teleported="true"
                        :hide-on-click-modal="true" @click="resIdcaBa(scope.row)"/>
            </template>
          </el-table-column>
          <el-table-column prop="result" label="对比结果" min-width="120"></el-table-column>
          <el-table-column prop="description" label="描述" min-width="120"></el-table-column>
          <el-table-column fixed="right" label="操作" width="140">
            <template #default="scope">
              <el-button v-if="scope.row.state==='认证审核中'" @click="handleCancelb({ row: scope.row, state: 2 })"
                         size="small" type="primary" style="color:#ffffff;">
                通过
              </el-button>
              <el-button v-if="scope.row.state==='认证审核中'" @click="handleCancelb({ row: scope.row, state: 3 })"
                         size="small" type="primary" style="color:#ffffff;">
                拒绝
              </el-button>
              <el-button v-if="scope.row.state==='认证成功'" @click="handleCancel({ row: scope.row, state: 3 })"
                         size="small" type="primary">
                取消认证
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import type {FormInstance} from 'element-plus'
import pageHook from "@/hooks/pageHook";
import {queryRealName_api, cancel_api} from "@/api/authentication"

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(queryRealname);
const queryFormRef = ref<FormInstance>()
let opNull = ref<any>(null)
let RealnameTab = ref([]);
let queryForm = reactive<any>({
  id: null,
  userId: null,
  realName: null,
  idCard: null,
  state: 1,
  applicationId: ''
})
let idcaFr = ref<any>([])
let idcaBa = ref<any>([])

async function queryname() {
  let res = await queryRealName_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    state: queryForm.state,
    applicationId: queryForm.applicationId,
  })
  let {data, total} = res.data

  data.forEach((v: any) => {
    if (v.state == 0) {
      v.state = "未提交";
    } else if (v.state == 1) {
      v.state = "认证审核中";
    } else if (v.state == 2) {
      v.state = "认证成功";
    } else if (v.state == 3) {
      v.state = "认证失败";
    } else if (v.state == 4) {
      v.state = "不允许再认证";
    }
  });
  RealnameTab.value = data
  totalNum.value = total
}

queryname()

// 查询
async function queryRealname() {
  let res = await queryRealName_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let {data, total} = res.data
  data.forEach((v: any) => {
    if (v.state == 0) {
      v.state = "未提交";
    } else if (v.state == 1) {
      v.state = "认证审核中";
    } else if (v.state == 2) {
      v.state = "认证成功";
    } else if (v.state == 3) {
      v.state = "认证失败";
    } else if (v.state == 4) {
      v.state = "不允许再认证";
    }
  });
  RealnameTab.value = data
  totalNum.value = total
}

const resIdcaFr = (row: any) => {
  idcaFr.value = []
  if (idcaFr.value.length == 0) {
    idcaFr.value.push(row.idCardFront)
  }
}
const resIdcaBa = (row: any) => {
  idcaBa.value = []
  if (idcaBa.value.length == 0) {
    idcaBa.value.push(row.idCardBackend)
  }
}

const handleCancel = ({row, state}: { row: any; state: number }) => {
  cancel_api({id: row.id, state}).then(() => {
    queryRealname();
  });
}

const handleCancelb = ({row, state}: { row: any; state: number }) => {
  cancel_api({id: row.id, state}).then(() => {
    queryRealname();
  });
}
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  queryRealname();
}
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  queryForm.state = 1
  formEl.resetFields()
  queryRealname()
}
</script>

<style lang="scss" scoped>
.realname {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .card-header {
      .el-form-item {
        width: 22%;
      }

      .el-select {
        width: 100%;
      }
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }
}
</style>