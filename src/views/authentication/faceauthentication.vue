<template>
  <div class="face">
    <el-card class="box-card">
<!--      //真人认证-->
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" :skipFirstSelect="true" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-select v-model="queryForm.gender" placeholder="请选择">
                <el-option label="男" :value="1"/>
                <el-option label="女" :value="2"/>
              </el-select>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="queryForm.state" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="未提交" :value="0"/>
                <el-option label="认证审核中" :value="1"/>
                <el-option label="认证成功" :value="2"/>
                <el-option label="认证失败" :value="3"/>
                <el-option label="不允许再认证" :value="5"/>
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker v-model="queryForm.date" :clearable="false" type="daterange" unlink-panels
                              value-format="x"
                              format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :shortcuts="shortcuts" size="default" @change="timeChange"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="faceRef" :data="faceData" style="width: 100%; height: 100%">
          <el-table-column label="用户ID" width="120" fixed>
            <template #default="scope">{{ scope.row.userId }}</template>
          </el-table-column>
          <el-table-column label="性别" min-width="60">
            <template #default="scope">{{ scope.row.gender == 1 ? "男" : scope.row.gender == 2 ? "女" : "" }}</template>
          </el-table-column>
          <el-table-column prop="bindInviteCode" label="绑定邀请码" min-width="120"></el-table-column>
          <el-table-column label="头像" min-width="100">
            <template #default="scope">
              <div class="img-box">
                <el-image style="width: 70px; height: 70px" :src="scope.row.avatar" :zoom-rate="1.2"
                          :preview-src-list="faceList" :initial-index="0" fite="cover" :preview-teleported="true"
                          :hide-on-click-modal="true" @click="fdimg(scope.row.avatar)"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="人脸" min-width="100">
            <template #default="scope">
              <div class="img-box">
                <el-image style="width: 70px; height: 70px" :src="scope.row.realFace" :zoom-rate="1.2"
                          :preview-src-list="faceList" :initial-index="0" fit="cover" :preview-teleported="true"
                          :hide-on-click-modal="true" @click="fdimg(scope.row.realFace)"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="对比分数" min-width="80">
            <template #default="scope">{{ scope.row.score }}</template>
          </el-table-column>
          <el-table-column label="认证状态" min-width="112">
            <template #default="scope">
              {{ scope.row.state }}
            </template>
          </el-table-column>
          <el-table-column label="描述" min-width="300">
            <template #default="scope">{{ scope.row.description }}</template>
          </el-table-column>
          <el-table-column label="认证时间" min-width="200">
            <template #default="scope">
              <span v-show="scope.row.submitTime">{{ getYMDHMS('-', ':', scope.row.submitTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最后操作人" min-width="120">
            <template #default="scope">{{ scope.row.lastOperator }}</template>
          </el-table-column>
          <el-table-column label="操作" width="400" fixed="right">
            <template #default="scope">
              <div v-if="scope.row.state !== 5">
                <el-button @click="faceEdit(scope.row, 2)" size="small" type="success"
                           v-if="scope.row.state != '认证成功'">通过
                </el-button>
                <el-button @click="faceEdit(scope.row, 3)" size="small" type="primary">取消认证</el-button>
                <el-button @click="faceEdit(scope.row, 4)" size="small" >取消更换头像

                </el-button>
<!--                <el-button @click="faceEdit(scope.row, 4)" size="small"-->
<!--                           v-if="scope.row.avatarInReview != null">取消更换头像-->

<!--                </el-button>-->
                <el-button @click="faceEdit(scope.row, 5)" size="small" type="primary">不允许再认证</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>

      <el-dialog v-model="isShow" title="理由" destroy-on-close @close="close" width="35%">
        <el-form ref="addFormRef" label-width="100px">
          <el-form-item prop="title" label="理由">
            <el-select v-model="reason">
              <el-option value="广告" label="广告"></el-option>
              <el-option value="头像与真人照片不符" label="头像与真人照片不符"></el-option>
              <el-option value="AI合成图片" label="AI合成图片"></el-option>
              <el-option value="其它" label="其它"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="handleAudit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import pageHook from "@/hooks/pageHook";
// import { Edit } from "@element-plus/icons-vue";
import {getYMDHMS} from "@/utils/date"
import {queryReal_api, modifyDay_api} from "@/api/authentication";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(querySubmit);
let faceData = ref([]); // 表格数据
let faceList = ref<any>([]);
let opNull = ref<any>(null);
let reason = ref('广告')
let isShow = ref<boolean>(false);
const queryForm = reactive<any>({
  userId: null,
  date: [],
  state: 1,
  startTime: null,
  endTime: null,
  applicationId: ''
});
let userId = ref<number>();
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 请求列表数据
async function getfaceList() {
  let res = await queryReal_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId: queryForm.applicationId,
    state: queryForm.state
  });

  let {data, total} = res.data;
  data.forEach((v: any) => {
    if (v.state == 0) {
      v.state = "未提交";
    } else if (v.state == 1) {
      v.state = "认证审核中";
    } else if (v.state == 2) {
      v.state = "认证成功";
    } else if (v.state == 5) {
      v.state = "不允许再认证";
      return;
    } else {
      v.state = "认证失败";
    }
  });
  faceData.value = data;
  totalNum = total;
}

getfaceList();
// 编辑
const faceEdit = async (row: any, state: any) => {
  if (state === 3) {
    isShow.value = true;
    userId.value = row.id;
    return;
  }
  await modifyDay_api({id: row.id, state});
  querySubmit();
};

const fdimg = (img: any) => {
  faceList.value = [img];
};

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function querySubmit() {
  let {userId, gender, state, startTime, endTime, applicationId} = queryForm;
  let res = await queryReal_api({
    userId,
    gender,
    state,
    startTime,
    endTime,
    applicationId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let {data, total} = res.data;
  data.forEach((v: any) => {
    if (v.state == 0) {
      v.state = "未提交";
    } else if (v.state == 1) {
      v.state = "认证审核中";
    } else if (v.state == 2) {
      v.state = "认证成功";
    } else if (v.state == 5) {
      v.state = "不允许再认证";
      return;
    } else {
      v.state = "认证失败";
    }
  });
  faceData.value = data;
  totalNum = total;
};
const onreset = () => {
  queryForm.userId = null;
  queryForm.date = [];
  queryForm.state = 1;
  getfaceList();
};

const handleAudit = async () => {
  await modifyDay_api({id: userId.value, state: 3, operatorDesc: reason.value})
  querySubmit()
  close();

}

const close = () => {
  isShow.value = false;
}
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  querySubmit();
}
</script>

<style scoped lang="scss">
.face {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .card-header {
      display: flex;
      align-items: center;
      width: 100%;

      .el-form {
        width: 100%;
      }
    }

    .none {
      display: none;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .image-slot {
      font-size: 30px;
    }

    .image-slot .el-icon {
      font-size: 30px;
    }

    .el-image {
      width: 100%;
    }

    :deep(.el-dialog) {
      width: 35%;
    }

    :deep(.dia-ipt) {
      width: 215px;
    }
  }
}
</style>
