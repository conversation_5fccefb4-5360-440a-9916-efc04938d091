import { reactive } from "vue";

// 自己定义表格头部的数据
export const femaleDataColumn = reactive({
  userId: {
    prop: "userId",
    title: "用户ID",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  nickname: {
    prop: "nickname",
    title: "昵称",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  unlockContactPackageCount: {
    prop: "unlockContactPackageCount",
    title: "一键解锁亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  oneLevelCount: {
    prop: "oneLevelCount",
    title: "1级亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  twoLevelCount: {
    prop: "twoLevelCount",
    title: "2级亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  threeLevelCount: {
    prop: "threeLevelCount",
    title: "3级亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  fourLevelCount: {
    prop: "fourLevelCount",
    title: "4级亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  fiveLevelCount: {
    prop: "fiveLevelCount",
    title: "5级亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  sixLevelCount: {
    prop: "sixLevelCount",
    title: "6级亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  sevenLevelCount: {
    prop: "sevenLevelCount",
    title: "7级亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  eightLevelCount: {
    prop: "eightLevelCount",
    title: "8级亲密度礼包被购买数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
})