<template>
  <div class="channel">
    <el-card class="box-card">
      <div class="table-container">
        <el-table ref="uselistRef" :data="dataList" style="width: 100%; height: 100%" v-loading="listLoading" highlight-current-rowstripe>
          <template v-for="item in femaleDataColumn" :key="item.prop">
            <el-table-column :prop="item.prop" :label="item.title" :min-width="item.minWidth">
              <template #default="{ row}">
                {{row[item.prop]?row[item.prop]:0}}
              </template>
            </el-table-column>
         </template>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, reactive, onBeforeMount } from "vue";
import { femaleDataColumn } from "./columnData/femaleDataColumn";
import pageHook from "@/hooks/pageHook";
import {query_woman_list_api} from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(queryList);
let dataList = ref([])
let timer: any = null


 async function queryList(){
 let {data:{data,total}} = await query_woman_list_api({ 
  pageSize: pageSize.value,
    pageNum: currentPage.value,});
 dataList.value=data;
 totalNum.value=total
}
queryList();
// 表格相关
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});




let { listLoading } = toRefs(state);

onBeforeMount(() => {
  clearInterval(timer)
})

</script>

<style lang="scss" scoped>
.channel {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    .mb-header {
      display: flex;

      :deep(.el-range-editor) {
        margin-left: 20px;
        flex: 1;
      }

      .el-input {
        margin-left: 20px;
        flex: 1;
      }

      .el-button {
        margin-left: 20px;
      }
    }
  }
}
</style>