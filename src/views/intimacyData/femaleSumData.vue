<template>
  <div class="channel">
    <el-card class="box-card">
      <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="query-form-inline">
        <el-form-item label="时间选择">
          <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
            format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="shortcuts"
            size="default" @change="timeChange" />
        </el-form-item>
        <el-form-item>


          <el-button type="primary" @click="queryList">查询</el-button>
          <el-button @click="onreset(queryFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="table-container">
        <el-table ref="uselistRef" :data="dataList" style="width: 100%; height: 100%" v-loading="listLoading"
          highlight-current-rowstripe>
          <template v-for="item in femaleSumDataColumn" :key="item.prop">
            <el-table-column :prop="item.prop" :label="item.title" :min-width="item.minWidth">
              <template #default="{ row }">
                {{ row[item.prop] ? row[item.prop] : 0 }}
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>

    </el-card>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, reactive, onBeforeMount } from "vue";
import { femaleSumDataColumn } from "./columnData/femaleSumDataColumn";
import type { FormInstance } from 'element-plus'
import { getYMD } from '@/utils/date'
import { query_sum_woman_list_api } from "@/api/index";

let dataList = ref([]);
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayEnd = toData + 24 * 60 * 60 * 1000; //结束时间
let todayStart = toData - 30 * 3600 * 24 * 1000
const queryFormRef = ref<FormInstance>()
let timer: any = null
let queryForm = reactive({
  beginDate: getYMD('-', todayStart),
  endDate: getYMD('-', todayEnd),
})
let date = ref<any>([
  new Date(todayStart),
  new Date(todayEnd),
])
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
async function queryList() {
  let { data: { data } } = await query_sum_woman_list_api({
    ...queryForm
  });
  dataList.value = data;

}
queryList();
// 表格相关
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});

const timeChange = (data: any) => {
  queryForm.beginDate = getYMD('-', data[0]);
  queryForm.endDate = getYMD('-', data[1]);
};

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = [
    new Date(todayStart),
    new Date(todayEnd),
  ]
  queryForm.beginDate = getYMD('-', todayStart),
    queryForm.endDate = getYMD('-', todayEnd)
  formEl.resetFields()
  queryList()
}

let { listLoading } = toRefs(state);

onBeforeMount(() => {
  clearInterval(timer)
})

</script>

<style lang="scss" scoped>
.channel {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }

  .card-header {
    .mb-header {
      display: flex;

      :deep(.el-range-editor) {
        margin-left: 20px;
        flex: 1;
      }

      .el-input {
        margin-left: 20px;
        flex: 1;
      }

      .el-button {
        margin-left: 20px;
      }
    }
  }
}
</style>