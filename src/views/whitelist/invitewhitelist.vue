<template>
  <div class="invitewhitelist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item>
              <el-button :icon="Plus" @click="addWhitelist">增加</el-button>
            </el-form-item>
            <!-- <el-form-item label="ID" prop="id">
              <el-input v-model="queryForm.id" />
            </el-form-item> -->
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryWhitelist">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="whitelistTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" min-width="80" fixed></el-table-column>
        <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
        <el-table-column prop="nickname" label="昵称" min-width="120"></el-table-column>
        <el-table-column prop="avatar" label="头像" min-width="113">
          <template #default="scope">
            <el-image style="width: 80px; height: 80px" :src="scope.row.avatar" :zoom-rate="1.2"
              :preview-src-list="avatarList" :initial-index="0" fit="cover" :preview-teleported="true"
              :hide-on-click-modal="true" @click="resAvatar(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="parentUserId" label="上级用户ID" min-width="120"></el-table-column>
        <el-table-column prop="inviteCount" label="邀请数量" min-width="120"></el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-popconfirm title="你确定删除吗?" @confirm="delWhitelist(scope.row)">
              <template #reference>
                <el-button type="danger" :icon="Delete" size="small"></el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
      <el-dialog v-model="addShow" title="添加超级邀请人">
        <el-form ref="addFormRef" :model="addForm">
          <el-form-item label="用户ID" label-width="60">
            <el-input v-model="addForm.userId" autocomplete="off" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="addShow = false">取消</el-button>
            <el-button type="primary" @click="addSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import {  Plus, Delete } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
import { queryWhitelist_api, addWhitelist_api, delWhitelist_api } from "@/api/whitelist";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(queryWhitelist);
const queryFormRef = ref<FormInstance>();
let whitelistTab = ref([]);
let queryForm = reactive<any>({
  id: null,
  userId: null,
  applicationId:'com.dongxin.app'
});
let addShow = ref(false)
let addForm = reactive<any>({
  userId: null,
})
let avatarList = ref<any>([])

async function getWhitelist() {
  let res = await queryWhitelist_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId
  });
  // console.log(res);
  let { data, total } = res.data;
  data.forEach((v: any) => (v.isShow = false));
  whitelistTab.value = data;
  totalNum.value = total;
}
getWhitelist();

const resAvatar = (row: any) => {
  avatarList.value = []
  if (avatarList.value.length == 0) {
    avatarList.value.push(row.avatar)
  }
}



// 增加
const addWhitelist = () => {
  addShow.value = true
}
const addSubMit = async () => {
  // console.log({ ...addForm });
  await addWhitelist_api({ ...addForm })
  queryWhitelist()
  addShow.value = false
}

//删除
const delWhitelist = async (row: any) => {
  await delWhitelist_api({ id: row.id })
  queryWhitelist()
}

// 查询
async function queryWhitelist() {
  let res = await queryWhitelist_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  whitelistTab.value = data;
  totalNum.value = total;
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  queryWhitelist();
};

</script>

<style lang="scss" scoped>
.invitewhitelist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
