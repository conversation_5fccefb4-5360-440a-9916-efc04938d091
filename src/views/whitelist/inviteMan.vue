<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="ID" prop="id">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button class="ml" type="primary" @click="handleExportExcel">
                    <el-icon style="vertical-align: middle">
                      <Download />
                    </el-icon>
                    <span style="vertical-align: middle">导出Excel</span>
                  </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="listData" style="width: 100%; height: 100%">
        <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
      <el-table-column  prop="userName" label="昵称" min-width="userName"></el-table-column>
      <el-table-column  prop="avatar" label="头像" min-width="100">
            <template #default="scope">
              <el-avatar shape="square" :size="70" :src="scope.row.avatar" fit="cover" />
            </template>
          </el-table-column>
      <el-table-column prop="status" label="用户状态" min-width="100"> 
         <template #default="scope">
            {{ scope.row.status===1?"已注销":scope.row.status===2?"被封禁":scope.row.status===3?"临时封禁":"正常" }}
            </template> </el-table-column>
      <el-table-column prop="bannedNum" label="封禁次数" min-width="100">  </el-table-column>
      <el-table-column prop="totalIncome" label="总收益" min-width="100">  </el-table-column>
      <el-table-column prop="registerTime" label="注册时间" min-width="100"></el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive,unref } from "vue";
import { queryInvite_whitelist_api } from "@/api/wallet"
import{FormInstance} from 'element-plus'
import{getYMDHMS} from '@/utils/date'
import pageHook from "@/hooks/pageHook";
import { ElMessage } from "element-plus";
import { aoaToSheetXlsx } from "@/utils/excel";
import { Download } from "@element-plus/icons-vue";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(query);
let listData = ref([])
let listLoading =ref(false);
const queryFormRef = ref<FormInstance>()
let queryForm = reactive({
  userId:null,
  applicationId:'com.dongxin.app'
});
//查询
async function query() {
  let res = await queryInvite_whitelist_api({...queryForm,  pageSize: pageSize.value,
    pageNum: currentPage.value});
  let { data, total } = res.data
  data.forEach((item:any)=>{
    item.registerTime===getYMDHMS('-',':',item.registerTime)
  })
  totalNum.value = total
  listData.value = data
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  queryForm.userId=null;
  listData.value=[];
};
// 导出Excel表格
const fileName = ref("邀请人明细");
const handleExportExcel = async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }
  listLoading.value = true;
  //标题数组
  let titleArr =['用户ID','昵称','头像','用户状态','封禁次数','总收益','注册时间']
  let table = unref(listData); //取消数据响应
  table.forEach((item:any)=>{
    item.status=item.status===1?"已注销":item.status===2?"被封禁":item.status===3?"临时封禁":"正常" 
  })
  let header = titleArr;
  let data = table.map((item: any, _index: any) => {
    let {userId,userName,avatar,status,bannedNum,totalIncome,registerTime} = item;
    return [userId,userName,avatar,status,bannedNum,totalIncome,registerTime];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });

  setInterval(() => {
    listLoading.value = false;
  }, 1000)
};

const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}

</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>