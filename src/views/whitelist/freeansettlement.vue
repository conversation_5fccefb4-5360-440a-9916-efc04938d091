<template>
  <div class="invitesettlement">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
<!--            <el-form-item label="ID" prop="id">-->
<!--              <el-input v-model="queryForm.id"/>-->
<!--            </el-form-item>-->
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker
                  v-model="date"
                  :clearable="false"
                  type="daterange"
                  unlink-panels
                  value-format="x"
                  format="YYYY-MM-DD"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="default"
                  @change="timeChange"
              />
            </el-form-item>
            <el-form-item label="结算状态" prop="status">
              <el-select v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="未结算" :value="0"/>
                <el-option label="已结算" :value="1"/>
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="querySettlement">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button class="ml" type="primary" @click="handleExportExcel">
                <el-icon style="vertical-align: middle">
                  <Download/>
                </el-icon>
                <span style="vertical-align: middle">导出Excel</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="settlementTab" style="width: 100%; height: 100%">
          <el-table-column prop="id" label="ID" min-width="80" fixed></el-table-column>
          <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
          <el-table-column prop="points" label="邀请好友所得积分" min-width="150"></el-table-column>
          <el-table-column prop="inviteIncome" label="推广人周收益" min-width="150"></el-table-column>
          <el-table-column prop="usedPayGolds" label="邀请男用户消费的付费金币数" min-width="120"></el-table-column>
          <el-table-column prop="rechargeIncome" label="消费金币所得收益" min-width="150"></el-table-column>
          <el-table-column prop="dateBegin" label="结算开始时间" min-width="120"></el-table-column>
          <el-table-column prop="dateEnd" label="结算结束时间" min-width="120"></el-table-column>
          <el-table-column prop="status" label="结算状态" min-width="120">
            <template #default="scope">
              {{ scope.row.status == 0 ? "未结算" : "已结算" }}
            </template>
          </el-table-column>
          <el-table-column prop="lastOperator" label="最后操作人" min-width="120"></el-table-column>
          <el-table-column fixed="right" label="操作" width="300">
            <template #default="scope">
              <el-button @click="editSettlement(scope.row, 0)" size="small" type="primary" v-if="scope.row.status != 1">
                未结算
              </el-button>
              <el-button @click="editSettlement(scope.row, 1)" size="small" type="success" v-if="scope.row.status != 1">
                已结算
              </el-button>
              <el-button @click="seeDetails(scope.row)" size="small">详情</el-button>
<!--            <el-button @click="handleExportExcel" size="small" type="primary">导出</el-button>-->
<!--            <el-button @click="exportDetailsData" size="small" type="primary">导出详情</el-button>-->
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>

      <el-dialog v-model="detailsShow" title="自然人结算详情" width="100%" :fullscreen="true" @close="disclose">
        <el-button type="primary" @click="exportDetailsData">
          <el-icon style="vertical-align: middle">
            <Download/>
          </el-icon>
          <span style="vertical-align: middle">导出详细数据</span>
        </el-button>
        <el-table :data="detailsTab" style="width: 100%">
          <el-table-column prop="id" label="ID" min-width="80" fixed></el-table-column>
          <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
          <el-table-column prop="toUserId" label="被邀请用户ID" min-width="120"></el-table-column>
          <el-table-column prop="toUserStatus" label="被邀请用户状态" min-width="120">
          </el-table-column>
          <el-table-column prop="points" label="邀请好友所得积分" min-width="150"></el-table-column>
          <el-table-column prop="inviteIncome" label="推广人获得收益" min-width="150"></el-table-column>
          <el-table-column prop="rechargeIncome" label="消费金币所得收益" min-width="150"></el-table-column>
          <el-table-column prop="usedPayGolds" label="邀请男用户消费的付费金币数" min-width="120"></el-table-column>
          <el-table-column prop="dateBegin" label="结算开始时间" min-width="120"></el-table-column>
          <el-table-column prop="dateEnd" label="结算结束时间" min-width="120"></el-table-column>

        </el-table>
        <template #footer>
          <el-pagination v-model:current-page="currentPageDetails" v-model:page-size="pageSizeDetails"
                         :page-sizes="[5, 10, 15]" :small="true" layout="total, sizes, prev, pager, next, jumper"
                         :total="totalNumDetails" :background="true" @size-change="SizeChange"
                         @current-change="CurrentChange"/>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, unref} from "vue";
import type {FormInstance} from "element-plus";
import pageHook from "@/hooks/pageHook";
import {aoaToSheetXlsx} from "@/utils/excel";
import {ElMessage} from "element-plus";
import {queryNatureSettlement_api, modifyNatureSettlement_api, settlementNatureDetails_api} from "@/api/whitelist";
import {todayEnd, past30daysStart, getYMD} from "@/utils/date";
import { modifyUserWallet_api } from "@/api/wallet";
import { queryGoldsPoints_api } from "@/api/wallet";


interface DetailsData {
  id: number;
  userId: number;
  toUserId: number;
  toUserStatus: string;
  points: number;
  inviteIncome: number;
  rechargeIncome: number;
  usedPayGolds: number;
  dateBegin: string;
  dateEnd: string;
}

let allDetailsData = ref<DetailsData[]>([]);


async function fetchAllDetailsData() {
  let allData: DetailsData[] = [];
  let pageNum = 1;
  const pageSize = 10000; // 设置一个足够大的值以获取所有数据
  while (true) {
    const res = await settlementNatureDetails_api({
      userId: uId.value,
      dateEnd: dateEnd.value,
      dateBegin: dateBegin.value,
      pageSize,
      pageNum,
    });
    const { data, total } = res.data;
    allData = [...allData, ...data];
    if (allData.length >= total) {
      break;
    }
    pageNum++;
  }
  allDetailsData.value = allData;
}

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} = pageHook(querySettlement);
const queryFormRef = ref<FormInstance>();
let settlementTab = ref([]);
let date = ref<[Date, Date]>([new Date(past30daysStart), new Date(todayEnd)]);
let queryForm = reactive<any>({
  id: null,
  userId: null,
  status: 0,
  dateBegin: null,
  dateEnd: null,
  applicationId: 'com.dongxin.app'
});
let opNull = ref<any>(null)
let detailsShow = ref(false)
let detailsTab = ref([]);
let currentPageDetails = ref<number>(1);
let pageSizeDetails = ref<number>(10);
let totalNumDetails = ref<any>(null);
let dateBegin = ref()
let dateEnd = ref()
let uId = ref()

async function getSettlement() {
  let res = await queryNatureSettlement_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let {data, total} = res.data;
  settlementTab.value = data;
  totalNum.value = total;
}

getSettlement();

const exportDetailsData = async () => {
  await fetchAllDetailsData();
  let header = ['ID', '用户ID', '被邀请用户ID', '被邀请用户状态', '邀请好友所得积分', '推广人获得收益', '消费金币所得收益', '邀请男用户消费的付费金币数', '结算开始时间', '结算结束时间'];
  let datas = allDetailsData.value.map((item: any) => {
    let {
      id,
      userId,
      toUserId,
      toUserStatus,
      points,
      inviteIncome,
      rechargeIncome,
      usedPayGolds,
      dateBegin,
      dateEnd
    } = item;
    return [id, userId, toUserId, toUserStatus, points, inviteIncome, rechargeIncome, usedPayGolds, dateBegin, dateEnd];
  });
  aoaToSheetXlsx({
    data: datas,
    header,
    filename: `自然人结算详情.xlsx`,
  });
}
// 编辑
const editSettlement = async (row: any, status: any) => {
  await modifyNatureSettlement_api({id: row.id, status})
  
  // 如果是设置为已结算状态，则同时增加积分
  if (status === 1) {
    // 先查询用户的钱包记录ID
    let walletRes = await queryGoldsPoints_api({
      userId: row.userId,
      applicationId: queryForm.applicationId,
      pageSize: 1,
      pageNum: 1
    });
    
    if (walletRes.data.data && walletRes.data.data.length > 0) {
      const walletId = walletRes.data.data[0].id;
      await modifyUserWallet_api({
        id: walletId, // 使用钱包记录ID
        points: row.inviteIncome,
        hasAdd: true,
        pointsBusinessType: "invite_settlement"
      })
    }
  }
  
  querySettlement()
}

//查看详情
const seeDetails = async (row: any) => {
  detailsShow.value = true
  dateBegin.value = row.dateBegin;
  dateEnd.value = row.dateEnd;
  uId.value = row.userId
  getDetails({userId: uId.value, dateEnd: dateEnd.value, dateBegin: dateBegin.value})
}

async function getDetails(e: any) {
  let res = await settlementNatureDetails_api({
    ...e,
    pageSize: pageSizeDetails.value,
    pageNum: currentPageDetails.value
  })
  let {data, total} = res.data;
  data.forEach((item: any) => {
    switch (item.toUserStatus) {
      case 0:
        item.toUserStatus = '正常'
        break;
      case 1:
        item.toUserStatus = '已注销'
        break;
      case 2:
        item.toUserStatus = '被封禁'
        break;
      case 3:
        item.toUserStatus = '临时封禁'
        break;
      default:
        break;
    }
  })
  detailsTab.value = data;
  totalNumDetails.value = total;
}

const disclose = () => {
  currentPageDetails.value = 1
}

// 分页 每条页数更改
const SizeChange = (val: number) => {
  pageSize.value = val;
  getDetails({userId: uId.value, dateEnd: dateEnd.value, dateBegin: dateBegin.value});
};
// 当前页码改变
const CurrentChange = (val: number) => {
  currentPage.value = val;
  getDetails({userId: uId.value, dateEnd: dateEnd.value, dateBegin: dateBegin.value});
};

// 查询
async function querySettlement() {
  let res = await queryNatureSettlement_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let {data, total} = res.data;
  settlementTab.value = data;
  totalNum.value = total;
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  getSettlement();
};
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  getSettlement();
}

const timeChange = (data: any) => {
  queryForm.dateBegin = getYMD('-', data[0]);
  queryForm.dateEnd = getYMD('-', data[1]);
};

// 导出Excel表格
const fileName = ref("自然人结算详情");
const handleExportExcel = async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }

  let res = await queryNatureSettlement_api({
    ...queryForm,
    pageSize: 10000,
    pageNum: currentPage.value,
  });
  let {data} = res.data;
  let header = ['ID', '用户ID', '邀请好友所得积分', '推广人周收益', '消费金币所得收益', '邀请男用户消费的付费金币数', '结算开始时间', '结算结束时间', '结算状态', '最后操作人', '是否存在重复数据'];
  let datas = data.map((item: any, _index: any) => {
    item.status === 1 ? item.status = "已结算" : item.status = "未结算";
    let {
      id,
      userId,
      points,
      inviteIncome,
      rechargeIncome,
      usedPayGolds,
      dateBegin,
      dateEnd,
      status,
      lastOperator,
      hasAbnormal
    } = item;
    return [id, userId, points, inviteIncome, rechargeIncome, usedPayGolds, dateBegin, dateEnd, status, lastOperator, hasAbnormal];
  });
  aoaToSheetXlsx({
    data: datas,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });
}
</script>

<style lang="scss" scoped>
.invitesettlement {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>
  