<template>
  <div class="login">
    <div class="login-main">
      <div class="header-login">
        <!-- <img src="@/assets/imgs/log.png" alt=""> -->
        <span>后台管理系统</span>
      </div>
      <div class="main-register">
        <p class="title">欢迎登录</p>
        <el-form ref="loginFormRef" :model="loginForm" status-icon  @keyup.enter.native="loginFn(loginFormRef)"  :rules="rules" class="demo-ruleForm">
          <el-form-item prop="account">
            <el-input v-model="loginForm.account" type="text" placeholder="账号" autocomplete="off">
              <template #prefix>
                <svgIcon size="vd" name="zhanghao" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" type="password" placeholder="密码" autocomplete="off">
              <template #prefix>
                <svgIcon size="vd" name="mima" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button color="#8242D0" @click="loginFn(loginFormRef)">登录</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
// import { validateAccount  } from "@/utils/validator"; //规则 validatePassword
import userStore from "@/store/userStore";
let { login_store } = userStore() //解构登录方法
const loginFormRef = ref<FormInstance>();

const loginForm = reactive<userFom>({
  account: "",
  password: "",
});
// 验证规则
const rules = reactive<FormRules>({
  // account: [{ validator: validateAccount, trigger: "blur" }],
  // password: [{ validator: validatePassword, trigger: "blur" }],
});
// 点击登录
const loginFn = (loginFormRef: FormInstance | undefined) => {
  if (!loginFormRef) return
  loginFormRef.validate((valid: any) => {
    if (valid) {
      login_store(loginForm)
    } else {
      return false
    }
  })
}

</script>
  
<style scoped lang="scss">
.login {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url("@/assets/imgs/bg.png") no-repeat center;
  background-size: 100%;
  .login-main {
    width: 20vw;
    height: 350px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;

    .header-login {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 30px;

      span {
        width: 225px;
        font-size: 28px;
        color: #fff;
        margin-left: 16px;
        background-image: -webkit-linear-gradient(270deg, rgba(119,33,184,0.66) 0%, #B979EF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .main-register {
      min-width: 360px;
      flex: 1;
      height: 100%;
      background-color: rgba($color: #fff, $alpha: .5);
      padding: 20px;
      box-sizing: border-box;
      border-radius: 10px;

      .title {
        font-weight: 700;
        font-size: 26px;
        margin-bottom: 20px;
        background-image: -webkit-linear-gradient(45deg, rgba(119,33,184,0.66) 0%, #B979EF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .el-input {
        height: 40px;
        letter-spacing: 3px;
        --el-input-focus-border-color: #8242D0;
      }

      .el-button {
        width: 100%;
        margin-top: 40px;
        height: 40px;
      }
    }
  }
}
</style>
  