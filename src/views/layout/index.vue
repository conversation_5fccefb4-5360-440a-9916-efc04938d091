<template>
  <div class="layout">
    <left-nav />
    <div class="content">
      <div class="content-nav">
        <header-nav />
      </div>
      <div class="content-main">
        <right-main />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import LeftNav from "@/views/layout/leftNav.vue";
import HeaderNav from "@/views/layout/headerNav.vue";
import RightMain from "@/views/layout/rightMain.vue";
</script>

<style scoped lang="scss">
.layout {
  width: 100%;
  height: 100%;
  display: flex;

  .content {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    .content-nav {
      height: 60px;
    }

    .content-main {
      flex: 1;
      overflow: hidden;
      overflow-y: auto;
    }
  }

}</style>
