<template>
    <div class="left-nav" :style="{ width: w + 'px' }">
        <el-scrollbar>
            <el-menu active-text-color="#8cc5fd" background-color="#363e50" class="el-menu-vertical-demo"
                :default-active="$router.path" text-color="#fff" unique-opened router :collapse-transition="false"
                :collapse="isCollapse">
                <template v-for="(v) in menus_store" :key="v">
                    <el-sub-menu :index="v.path">
                        <template #title>
                            <el-icon>
                                <!-- 如果是Element Plus图标 -->
                                <component v-if="v.meta.iconType === 'element'" :is="v.meta.icon" />
                                <!-- 如果是自定义SVG图标 -->
                                <svgIcon v-else size="sm" :name="v.meta.icon || 'guanli'" />
                            </el-icon>
                            <span>{{ v.meta.title }}</span>
                        </template>
                        <el-menu-item v-for="(item, i) in v.children" :key="i" :index="item.meta.path">
                            {{ item.meta.title }}
                        </el-menu-item>
                    </el-sub-menu>
                </template>
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<script setup lang="ts">
import { toRefs, computed } from 'vue';
import { useRoute } from "vue-router"
import leftnavStore from '@/store/leftnavStore';
// 引入仓库权限路由
import routerStore from "@/store/routerStore"
let menus_store: any = computed(() => routerStore().meunsArrs)
let { w, isCollapse } = toRefs(leftnavStore())
let { wFn } = leftnavStore()
let { route } = routerStore()
let $router = useRoute()

wFn()
route()
</script>

<style lang="scss" scoped>
.left-nav {
    background: #363e50;
    box-shadow: 0 0 10px #ccc;

    // overflow-x: hidden;
    .el-menu {
        border: none;
    }
}
</style>