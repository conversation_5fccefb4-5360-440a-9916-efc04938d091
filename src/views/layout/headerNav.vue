<template>
  <div class="header-nav">
    <div class="bar-left">
      <el-icon @click="onIsCollapse()" :class="isCollapse ? 'zhuan' : 'buzhuan'" size="26">
        <Menu/>
      </el-icon>
      <!-- 面包屑 -->
      <el-breadcrumb :separator-icon="ArrowRight">
        <el-breadcrumb-item :to="{ path: '/dashboard' }">后台首页</el-breadcrumb-item>
        <el-breadcrumb-item v-for="v in brendArr" :key="v.path" :to="{ path: v.path }">{{
            v.title
          }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="bar-right">
      <router-link class="topbnt" to="/operation/offlinePushall">
        <el-button style="background-color: #930000" type="primary">📢全站通知</el-button>
      </router-link>
      <router-link class="topbnt" to="/friendsData/friendsDetailRate">
        <el-button style="background-color: #e3b5ff" type="primary">交友评级</el-button>
      </router-link>
      <router-link class="topbnt" to="/foreground_user/foregrounduser">
        <el-button style="background-color: #06a200" type="primary">用户信息</el-button>
      </router-link>
      <router-link class="topbnt" to="/white_list/binding">
        <el-button style="background-color: #000000" type="primary">申请绑定</el-button>
      </router-link>
      <router-link class="topbnt" to="/recharge/withdrawcash">
        <el-button style="background-color: #c93838" type="primary">提现管理</el-button>
      </router-link>
      <router-link class="topbnt" to="/white_list/freeansettlement">
        <el-button style="background-color: #c93838" type="primary">自然人结算</el-button>
      </router-link>
      <router-link class="topbnt" to="/user_authentication/realname">
        <el-button style="background-color: #264b8d" type="primary">实名认证</el-button>
      </router-link>
      <router-link class="topbnt" to="/user_authentication/face">
        <el-button style="background-color: #9d52ea" type="primary">真人认证</el-button>
      </router-link>
      <router-link class="topbnt" to="/user_authentication/videocert">
        <el-button style="background-color: #367a00" type="primary">视频认证</el-button>
      </router-link>
      <router-link class="topbnt" to="/gift_manage/gifts">
        <el-button style="background-color: #d26ee0" type="primary">礼物管理</el-button>
      </router-link>
      <span class="baracc" @click="handleTohome" v-if="$route.path!='/dashboard/home'">回到首页</span>
      <el-dropdown @command="sellFn">
        <el-button type="primary">
          {{ person }}
          <el-icon class="el-icon--right">
            <arrow-down/>
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="useper">个人中心</el-dropdown-item>
            <el-dropdown-item command="unbanRecharge">解除充值限制</el-dropdown-item>
            <el-dropdown-item command="searchUsers">多号查询</el-dropdown-item>
            <el-dropdown-item command="banRecharge">禁止充值</el-dropdown-item>
            <el-dropdown-item command="modifyCharm">修改魅力值</el-dropdown-item>
<!--            <el-dropdown-item command="wechatPrice" style="color: #409EFF;">微信解锁价格</el-dropdown-item>-->
            <el-dropdown-item command="adBlock" style="color: red;">广告拉人反制</el-dropdown-item>
            <el-dropdown-item command="predestinationSwitch" style="color: #409EFF;">缘分推送开关</el-dropdown-item>
            <el-dropdown-item command="examineConfig" style="color: #E6A23C;">审核包认证管理</el-dropdown-item>
            <el-dropdown-item command="videoStingConfig" style="color: #9C27B0;">视频认证示例文案</el-dropdown-item>
            <el-dropdown-item command="videoAuthSwitch" style="color: #FF5722;">视频认证开关</el-dropdown-item>
            <el-dropdown-item command="loveLocksSwitch" style="color: #E91E63;">同心锁开关</el-dropdown-item>
            <el-dropdown-item command="womanOpenSwitch" style="color: #FF69B4;">女用户建联开关</el-dropdown-item>
            <el-dropdown-item command="manOpenSwitch" style="color: #4169E1;">男用户建联开关</el-dropdown-item>
            <el-dropdown-item command="androidIdManager" style="color: #795548;">不限制注册安卓ID</el-dropdown-item>
            <el-dropdown-item command="manLabelConfig" style="color: #2196F3;">男用户新人标签</el-dropdown-item>
            <el-dropdown-item command="womanLabelConfig" style="color: #FF9800;">女用户新人标签</el-dropdown-item>
            <el-dropdown-item command="rechargeTextConfig" style="color: #00BCD4;">充值页提示语</el-dropdown-item>
            <el-dropdown-item command="msgCardConfig" style="color: #FF6B35;">消息使用张数</el-dropdown-item>
            <el-dropdown-item command="msgCardNumConfig" style="color: #FF4500;">注册赠送消息卡</el-dropdown-item>
            <el-dropdown-item command="limitCountConfig" style="color: #FF1493;">每个人使用消息卡张数</el-dropdown-item>
            <el-dropdown-item command="clearRtc" style="color: #ff8c00;">清理卡视频</el-dropdown-item>
            <el-dropdown-item command="outlogin">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>


  <!-- 解除充值限制弹窗 -->
  <el-dialog v-model="unbanRechargeVisible" title="输入用户ID 解除充值限制">
    <el-input v-model="unbanUserId" placeholder="请输入user_id" maxlength="8"
              oninput="value=value.replace(/\D/g,'')"></el-input>
    <el-button @click="unbanRecharge" type="primary" style="margin-top: 10px">解除限制</el-button>
  </el-dialog>

  <!-- 多号查询弹窗 -->
  <el-dialog v-model="userSearchVisible" title="多号查询">
    <el-input v-model="searchUserId" placeholder="请输入user_id"></el-input>
    <el-button @click="searchUsers" type="primary" style="margin-top: 10px">查询</el-button>
    <span style="margin-top: 10px">充值总金额：{{ totalRechargeAmount }}</span>
    <el-table :data="userList" style="width: 100%; margin-top: 20px">
      <el-table-column prop="user_id" label="用户ID"></el-table-column>
      <el-table-column prop="nickname" label="昵称"></el-table-column>
      <el-table-column prop="total_recharge_amount" label="充值金额"></el-table-column>
      <el-table-column prop="android_id" label="设备ID"></el-table-column>
    </el-table>
  </el-dialog>

  <!-- 禁止充值弹窗 -->
  <el-dialog v-model="banRechargeVisible" title="禁止充值(设备ID在你刚才点击的下来菜单里面输入用户ID查询)">
    <el-input v-model="banDeviceId" placeholder="请输入设备ID"></el-input>
    <el-button @click="addBanRecharge" type="primary" style="margin-top: 10px">添加</el-button>

    <div style="margin-top: 20px">
      <div v-for="id in payList" :key="id" class="ban-item">
        <span class="ban-id">{{ id }}</span>
        <el-button @click="delBanRecharge(id)" type="danger" size="small" class="ban-delete-btn">X</el-button>
      </div>
    </div>
  </el-dialog>

  <!-- 修改魅力值弹窗 -->
  <el-dialog v-model="charmValueVisible" title="修改魅力值">
    <el-input v-model="charmUserId" placeholder="请输入用户ID"></el-input>
    <el-button @click="fetchCharmValue" type="primary" style="margin-top: 10px">读取</el-button>
    <el-input v-model="charmValue" placeholder="魅力值" style="margin-top: 10px"></el-input>
    <el-button @click="updateCharmValue" type="primary" style="margin-top: 10px">修改</el-button>
  </el-dialog>

  <!-- 广告反制弹窗 -->
  <el-dialog v-model="adBlockVisible" title="广告拉人反制">
    <el-input v-model="blockIp" placeholder="请输入捣乱者的IP地址"></el-input>
    <el-button @click="addBlockIp" type="primary" style="margin-top: 10px">增加反制</el-button>

    <div style="margin-top: 20px">
      <div v-for="ip in blockIpList" :key="ip" class="ban-item">
        <span class="ban-id">{{ ip }}</span>
        <el-button @click="removeBlockIp(ip)" type="danger" size="small" class="ban-delete-btn">X</el-button>
      </div>
    </div>
  </el-dialog>

  <!-- 清理卡视频状态弹窗 -->
  <el-dialog v-model="clearRtcVisible" title="清理卡视频状态">
    <el-input v-model="rtcUserId" placeholder="请输入卡视频的用户ID"></el-input>
    <el-button @click="clearRtcStatus" type="primary" style="margin-top: 10px">清理卡视频状态</el-button>
  </el-dialog>

  <!-- 微信解锁价格弹窗 -->
  <el-dialog v-model="wechatPriceVisible" title="微信解锁价格 / 金币">
    <el-input v-model="wechatPrice" placeholder="请输入微信解锁价格"></el-input>
    <el-button @click="updateWechatPrice" type="primary" style="margin-top: 10px">更新价格</el-button>
  </el-dialog>

  <!-- 缘分推送开关弹窗 -->
  <el-dialog v-model="predestinationSwitchVisible" title="缘分推送开关">
    <div style="margin-bottom: 20px;">
      <span>当前状态：</span>
      <span :style="{ color: predestinationStatus === '开启' ? '#67C23A' : '#F56C6C' }">
        {{ predestinationStatus }}
      </span>
    </div>
    <el-button @click="togglePredestinationSwitch(true)" type="success" style="margin-right: 10px">开启缘分推送</el-button>
    <el-button @click="togglePredestinationSwitch(false)" type="danger">关闭缘分推送</el-button>
  </el-dialog>

  <!-- 审核包认证管理弹窗 -->
  <el-dialog v-model="examineConfigVisible" title="审核包认证管理">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>当前版本：</span>
        <span style="color: #409EFF; font-weight: bold;">{{ currentVersion }}</span>
      </div>
      <div style="margin-bottom: 15px;">
        <span>渠道数量：</span>
        <span style="color: #67C23A; font-weight: bold;">{{ channelCount }} 个渠道</span>
      </div>
      <div style="margin-bottom: 15px;">
        <span>当前配置：</span>
        <div style="background-color: #f5f7fa; padding: 10px; border-radius: 4px; margin-top: 5px; word-break: break-all;">
          {{ currentExamineVerify || '暂无配置' }}
        </div>
      </div>
    </div>

    <div style="margin-bottom: 20px; border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>更新渠道认证配置：</span>
      </div>
      <el-input
          v-model="newExamineVerify"
          type="textarea"
          :rows="3"
          placeholder="请输入渠道认证配置，格式：official_official_official,official_official_official,official_official_official"
          style="margin-bottom: 10px;">
      </el-input>
      <el-button @click="updateExamineVerify" type="success" style="margin-top: 10px">更新渠道配置</el-button>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>更新版本：</span>
      </div>
      <el-input v-model="newVersion" placeholder="请输入新版本号，如：1.0.1" style="margin-bottom: 10px;"></el-input>
      <el-button @click="updateVersion" type="primary">更新版本</el-button>
    </div>
  </el-dialog>

  <!-- 视频认证示例文案管理弹窗 -->
  <el-dialog v-model="videoStingConfigVisible" title="视频认证示例文案管理">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>当前文案：</span>
        <div style="background-color: #f5f7fa; padding: 10px; border-radius: 4px; margin-top: 5px; word-break: break-all;">
          {{ currentVideoSting || '暂无文案' }}
        </div>
      </div>
      <div style="margin-bottom: 15px;">
        <span>文案长度：</span>
        <span style="color: #67C23A; font-weight: bold;">{{ textLength }} 个字符</span>
      </div>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>更新示例文案：</span>
      </div>
      <el-input
          v-model="newVideoSting"
          type="textarea"
          :rows="4"
          placeholder="请输入视频认证示例文案，如：请录制一段视频进行身份认证，确保面部清晰可见"
          style="margin-bottom: 10px;">
      </el-input>
      <el-button @click="updateVideoSting" type="primary">更新文案</el-button>
    </div>
  </el-dialog>

  <!-- 视频认证开关弹窗 -->
  <el-dialog v-model="videoAuthSwitchVisible" title="视频认证开关">
    <div style="margin-bottom: 20px;">
      <span>当前状态：</span>
      <span :style="{ color: videoAuthStatus === '需要视频认证' ? '#67C23A' : '#F56C6C' }">
        {{ videoAuthStatus }}
      </span>
    </div>
    <el-button @click="toggleVideoAuthSwitch(true)" type="success" style="margin-right: 10px">开启视频认证</el-button>
    <el-button @click="toggleVideoAuthSwitch(false)" type="danger">关闭视频认证</el-button>
  </el-dialog>

  <!-- 同心锁开关弹窗 -->
  <el-dialog v-model="loveLocksVisible" title="同心锁开关">
    <div style="margin-bottom: 20px;">
      <span>当前状态：</span>
      <span :style="{ color: loveLocksStatus === '开启' ? '#67C23A' : '#F56C6C' }">
        {{ loveLocksStatus }}
      </span>
    </div>
    <el-button @click="toggleLoveLocksSwitch(true)" type="success" style="margin-right: 10px">开启同心锁</el-button>
    <el-button @click="toggleLoveLocksSwitch(false)" type="danger">关闭同心锁</el-button>
  </el-dialog>

  <!-- 女用户建联开关弹窗 -->
  <el-dialog v-model="womanOpenVisible" title="女用户建联开关">
    <div style="margin-bottom: 20px;">
      <span>当前状态：</span>
      <span :style="{ color: womanOpenStatus === '能建联视频' ? '#67C23A' : '#F56C6C' }">
        {{ womanOpenStatus }}
      </span>
    </div>
    <el-button @click="toggleWomanOpenSwitch(true)" type="success" style="margin-right: 10px">开启女用户建联</el-button>
    <el-button @click="toggleWomanOpenSwitch(false)" type="danger">关闭女用户建联</el-button>
  </el-dialog>

  <!-- 男用户建联开关弹窗 -->
  <el-dialog v-model="manOpenVisible" title="男用户建联开关">
    <div style="margin-bottom: 20px;">
      <span>当前状态：</span>
      <span :style="{ color: manOpenStatus === '能建联视频' ? '#67C23A' : '#F56C6C' }">
        {{ manOpenStatus }}
      </span>
    </div>
    <el-button @click="toggleManOpenSwitch(true)" type="success" style="margin-right: 10px">开启男用户建联</el-button>
    <el-button @click="toggleManOpenSwitch(false)" type="danger">关闭男用户建联</el-button>
  </el-dialog>

  <!-- 不限制注册安卓ID管理弹窗 -->
  <el-dialog v-model="androidIdManagerVisible" title="不限制注册安卓ID管理">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>当前数量：</span>
        <span style="color: #67C23A; font-weight: bold;">{{ androidIdCount }} 个AndroidId</span>
      </div>
      <div style="margin-bottom: 15px;">
        <span>AndroidId列表：</span>
        <div style="background-color: #f5f7fa; padding: 10px; border-radius: 4px; margin-top: 5px; max-height: 200px; overflow-y: auto;">
          <div v-if="androidIdArray.length === 0" style="color: #909399;">暂无AndroidId</div>
          <div v-else>
            <div v-for="(id, index) in androidIdArray" :key="index" class="android-item">
              <span class="android-id">{{ id }}</span>
              <el-button @click="removeAndroidId(id)" type="danger" size="small" class="android-delete-btn">X</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>添加新的AndroidId：</span>
      </div>
      <el-input
        v-model="newAndroidId"
        placeholder="请输入AndroidId"
        style="margin-bottom: 10px;">
      </el-input>
      <el-button @click="addAndroidId" type="primary">添加AndroidId</el-button>
    </div>
  </el-dialog>

  <!-- 男用户新人标签配置弹窗 -->
  <el-dialog v-model="manLabelConfigVisible" title="男用户新人标签配置">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>标签状态：</span>
        <span :style="{ color: manLabelStatus === '开启' ? '#67C23A' : '#F56C6C' }">
          {{ manLabelStatus }}
        </span>
      </div>
      <div style="margin-bottom: 15px;">
        <span>当前天数：</span>
        <span style="color: #409EFF; font-weight: bold;">{{ manTime }} 天</span>
      </div>
    </div>

    <div style="margin-bottom: 20px;">
      <el-button @click="toggleManLabel(true)" type="success" style="margin-right: 10px">开启男用户新人标签</el-button>
      <el-button @click="toggleManLabel(false)" type="danger" style="margin-right: 10px">关闭男用户新人标签</el-button>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>设置注册天数：</span>
      </div>
      <el-input v-model="newManTime" type="number" placeholder="请输入天数（大于0的整数）" style="margin-bottom: 10px;"></el-input>
      <el-button @click="updateManTime" type="primary">更新天数</el-button>
    </div>
  </el-dialog>

  <!-- 女用户新人标签配置弹窗 -->
  <el-dialog v-model="womanLabelConfigVisible" title="女用户新人标签配置">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>标签状态：</span>
        <span :style="{ color: womanLabelStatus === '开启' ? '#67C23A' : '#F56C6C' }">
          {{ womanLabelStatus }}
        </span>
      </div>
      <div style="margin-bottom: 15px;">
        <span>当前天数：</span>
        <span style="color: #409EFF; font-weight: bold;">{{ womanTime }} 天</span>
      </div>
    </div>

    <div style="margin-bottom: 20px;">
      <el-button @click="toggleWomanLabel(true)" type="success" style="margin-right: 10px">开启女用户新人标签</el-button>
      <el-button @click="toggleWomanLabel(false)" type="danger" style="margin-right: 10px">关闭女用户新人标签</el-button>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>设置注册天数：</span>
      </div>
      <el-input v-model="newWomanTime" type="number" placeholder="请输入天数（大于0的整数）" style="margin-bottom: 10px;"></el-input>
      <el-button @click="updateWomanTime" type="primary">更新天数</el-button>
    </div>
  </el-dialog>

  <!-- 充值页提示语配置弹窗 -->
  <el-dialog v-model="rechargeTextConfigVisible" title="充值页提示语配置">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>当前提示语：</span>
        <div style="background-color: #f5f7fa; padding: 10px; border-radius: 4px; margin-top: 5px; word-break: break-all;">
          {{ currentRechargeText || '暂无提示语' }}
        </div>
      </div>
      <div style="margin-bottom: 15px;">
        <span>文本长度：</span>
        <span style="color: #67C23A; font-weight: bold;">{{ rechargeTextLength }} 个字符</span>
      </div>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>更新提示语：</span>
      </div>
      <el-input
          v-model="newRechargeText"
          type="textarea"
          :rows="4"
          placeholder="请输入充值页提示语，如：充值即可享受更多服务"
          style="margin-bottom: 10px;">
      </el-input>
      <el-button @click="updateRechargeText" type="primary">更新提示语</el-button>
    </div>
  </el-dialog>

  <!-- 消息卡赠送数量配置弹窗 -->
  <el-dialog v-model="msgCardConfigVisible" title="消息卡赠送数量配置">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>当前消息使用张数</span>
        <span style="color: #409EFF; font-weight: bold; font-size: 18px;">{{ currentMsgCardLimit }} 张</span>
      </div>
      <div style="margin-bottom: 15px;">
        <span>配置说明：</span>
        <div style="background-color: #f5f7fa; padding: 10px; border-radius: 4px; margin-top: 5px; color: #606266;">
          设置消息使用张数限制，必须为非负整数
        </div>
      </div>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>数量：</span>
      </div>
      <el-input
          v-model="newMsgCardLimit"
          type="number"
          placeholder="请输入消息使用张数（非负整数）"
          style="margin-bottom: 10px;"
          :min="0">
      </el-input>
      <el-button @click="updateMsgCardLimit" type="primary">更新配置</el-button>
    </div>
  </el-dialog>

  <!-- 注册赠送消息卡配置弹窗 -->
  <el-dialog v-model="msgCardNumConfigVisible" title="注册赠送消息卡配置">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>当前注册赠送消息卡数量：</span>
        <span style="color: #409EFF; font-weight: bold; font-size: 18px;">{{ currentMsgCardNum }} 张</span>
      </div>
      <div style="margin-bottom: 15px;">
        <span>配置说明：</span>
        <div style="background-color: #f5f7fa; padding: 10px; border-radius: 4px; margin-top: 5px; color: #606266;">
          设置用户注册时赠送的消息卡数量，必须为非负整数
        </div>
      </div>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>赠送数量：</span>
      </div>
      <el-input
          v-model="newMsgCardNum"
          type="number"
          placeholder="请输入注册赠送消息卡数量（非负整数）"
          style="margin-bottom: 10px;"
          :min="0">
      </el-input>
      <el-button @click="updateMsgCardNumConfig" type="primary">更新配置</el-button>
    </div>
  </el-dialog>

  <!-- 每个人使用消息卡张数限制配置弹窗 -->
  <el-dialog v-model="limitCountConfigVisible" title="每个人使用消息卡张数限制配置">
    <div style="margin-bottom: 20px;">
      <div style="margin-bottom: 15px;">
        <span>当前每个人使用消息卡张数限制：</span>
        <span style="color: #409EFF; font-weight: bold; font-size: 18px;">{{ currentLimitCount }} 张</span>
      </div>
      <div style="margin-bottom: 15px;">
        <span>配置说明：</span>
        <div style="background-color: #f5f7fa; padding: 10px; border-radius: 4px; margin-top: 5px; color: #606266;">
          设置每个用户可以使用的消息卡张数限制，必须为非负整数，默认值为10张
        </div>
      </div>
    </div>

    <div style="border-top: 1px solid #eee; padding-top: 20px;">
      <div style="margin-bottom: 10px;">
        <span>限制张数：</span>
      </div>
      <el-input
          v-model="newLimitCount"
          type="number"
          placeholder="请输入每个人使用消息卡张数限制（非负整数）"
          style="margin-bottom: 10px;"
          :min="0">
      </el-input>
      <el-button @click="updateLimitCountConfig" type="primary">更新配置</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import {ArrowRight, ArrowDown, Menu} from "@element-plus/icons-vue";
import {ElMessage} from 'element-plus'
import {computed, reactive, toRefs, ref} from "vue";
import {useRoute, useRouter} from "vue-router";
import leftnavStore from '@/store/leftnavStore';
import userStore from "@/store/userStore";
import {jiechupay} from "@/api/foregrounduser";
import {secretKeyApiClient} from "@/utils/secretKeyApiClient";


let {isCollapse} = toRefs(leftnavStore())
let {onIsCollapse} = leftnavStore()
let {clear_store} = userStore();
let {person} = toRefs(userStore())

// 面包屑
let $route = useRoute();

interface brend {
  title: string | unknown;
  path: string;
}

let brendArr = computed(() => {
  let brendNav = reactive<Array<brend>>([]);
  $route.matched.forEach((v) => {
    if (v.meta.path != "") {
      brendNav.push({
        title: v.meta.title,
        path: v.path,
      });
    }
  });
  return brendNav;
});

// 下拉框
let $router = useRouter();
let sellFn = (val: string) => {
  if (val == "useper") {
    $router.push("/info");
  } else if (val == "searchUsers") {
    userSearchVisible.value = true;
  } else if (val == "banRecharge") {
    banRechargeVisible.value = true;
    getPayList();
  } else if (val == "outlogin") {
    $router.push("/login");
    clear_store();
    setTimeout(() => {
      location.reload();
    }, 300);
  } else if (val == "unbanRecharge") {
    unbanRechargeVisible.value = true;
  } else if (val == "modifyCharm") {
    charmValueVisible.value = true;
  } else if (val == "wechatPrice") {
    wechatPriceVisible.value = true;
    getWechatPrice();
  } else if (val == "adBlock") {
    adBlockVisible.value = true;
    getBlockIpList();
  } else if (val == "predestinationSwitch") {
    predestinationSwitchVisible.value = true;
    getPredestinationStatus();
  } else if (val == "examineConfig") {
    examineConfigVisible.value = true;
    getExamineConfig();
  } else if (val == "videoStingConfig") {
    videoStingConfigVisible.value = true;
    getVideoStingConfig();
  } else if (val == "videoAuthSwitch") {
    videoAuthSwitchVisible.value = true;
    getVideoAuthStatus();
  } else if (val == "loveLocksSwitch") {
    loveLocksVisible.value = true;
    getLoveLocksStatus();
  } else if (val == "womanOpenSwitch") {
    womanOpenVisible.value = true;
    getWomanOpenStatus();
  } else if (val == "manOpenSwitch") {
    manOpenVisible.value = true;
    getManOpenStatus();
  } else if (val == "androidIdManager") {
    androidIdManagerVisible.value = true;
    getAndroidIdList();
  } else if (val == "manLabelConfig") {
    manLabelConfigVisible.value = true;
    getManLabelConfig();
  } else if (val == "womanLabelConfig") {
    womanLabelConfigVisible.value = true;
    getWomanLabelConfig();
  } else if (val == "rechargeTextConfig") {
    rechargeTextConfigVisible.value = true;
    getRechargeTextConfig();
  } else if (val == "msgCardConfig") {
    msgCardConfigVisible.value = true;
    getMsgCardConfig();
  } else if (val == "msgCardNumConfig") {
    msgCardNumConfigVisible.value = true;
    getMsgCardNumConfig();
  } else if (val == "limitCountConfig") {
    limitCountConfigVisible.value = true;
    getLimitCountConfig();
  } else if (val == "clearRtc") {
    clearRtcVisible.value = true;
  }
};

const handleTohome = () => {
  $router.push("/datadetail/home");
}
interface User {
  user_id: string;
  nickname: string;
  total_recharge_amount: string;
  android_id: string;
}
// 多号查询
// 多号查询
let userSearchVisible = ref(false);
let searchUserId = ref('');
let userList = ref<User[]>([]);
let totalRechargeAmount = computed(() => {
  return userList.value.reduce((sum, user) => sum + parseFloat(user.total_recharge_amount || '0'), 0);
});

const searchUsers = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/userinfo', { user_id: searchUserId.value });
    userList.value = res.data;
  } catch (error) {
    console.error(error);
  }
}


// 解除充值限制
let unbanRechargeVisible = ref(false);
let unbanUserId = ref('');

const unbanRecharge = async () => {
  try {
    const res = await jiechupay({userId: unbanUserId.value});
    ElMessage.success(res.data.message);
  } catch (error) {
    console.error(error);
  }
}
// 禁止充值
let banRechargeVisible = ref(false);
let banDeviceId = ref('');
let payList = ref([]);

const getPayList = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/paylist');
    payList.value = res.data.split(',');
  } catch (error) {
    console.error(error);
  }
}

const addBanRecharge = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/uppay', { jqm: banDeviceId.value });
    ElMessage.success(res.data.message);
    getPayList();
    banDeviceId.value = ''; // 清空 banDeviceId
  } catch (error) {
    console.error(error);
  }
}

const delBanRecharge = async (id: string) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/delpay', { jqm: id });
    ElMessage.success(res.data.message);
    getPayList();
  } catch (error) {
    console.error(error);
  }
}

let charmValueVisible = ref(false);
let charmUserId = ref('');
let charmValue = ref('');

const fetchCharmValue = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/meiliduqu', { uid: charmUserId.value });
    if (res.data.code === 200) {
      charmValue.value = res.data.data.charmVal; // 更新为从 data 中提取 charmVal
    } else {
      ElMessage.error(res.data.message); // 处理非200的返回
    }
  } catch (error) {
    console.error(error);
    ElMessage.error("获取魅力值失败");
  }
};

const updateCharmValue = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/meiliup', { uid: charmUserId.value, charmVal: charmValue.value });
    ElMessage.success(res.data.message);
  } catch (error) {
    console.error(error);
  }
};

// 广告反制
let adBlockVisible = ref(false);
let blockIp = ref('');
let blockIpList = ref<string[]>([]);

// 获取反制IP列表
const getBlockIpList = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/adfanzhi');
    if (res.data.code === 200 && res.data.data) {
      blockIpList.value = res.data.data.split(',');
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取反制IP列表失败');
  }
}

// 添加反制IP
const addBlockIp = async () => {
  if (!blockIp.value) {
    ElMessage.warning('请输入IP地址');
    return;
  }

  try {
    const newList = [...blockIpList.value, blockIp.value].join(',');
    const res = await secretKeyApiClient.get('/app/yuai/adfanzhi', { ipList: newList });
    if (res.data.code === 200) {
      ElMessage.success('添加成功');
      blockIp.value = '';
      getBlockIpList();
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('添加失败');
  }
}

// 移除反制IP
const removeBlockIp = async (ip: string) => {
  try {
    const newList = blockIpList.value.filter(item => item !== ip).join(',');
    const res = await secretKeyApiClient.get('/app/yuai/adfanzhi', { ipList: newList });
    if (res.data.code === 200) {
      ElMessage.success('移除成功');
      getBlockIpList();
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('移除失败');
  }
}

// 清理卡视频状态
let clearRtcVisible = ref(false);
let rtcUserId = ref('');

const clearRtcStatus = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/clearRtcStatus', { uid: rtcUserId.value });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      rtcUserId.value = ''; // 清空输入框
      clearRtcVisible.value = false; // 关闭弹窗
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('清理失败');
  }
}

// 微信解锁价格
let wechatPriceVisible = ref(false);
let wechatPrice = ref('');

// 缘分推送开关
let predestinationSwitchVisible = ref(false);
let predestinationStatus = ref('');

// 审核包认证管理
let examineConfigVisible = ref(false);
let currentExamineVerify = ref('');
let channelCount = ref(0);
let currentVersion = ref('');
let newExamineVerify = ref('');
let newVersion = ref('');

// 视频认证示例文案管理
let videoStingConfigVisible = ref(false);
let currentVideoSting = ref('');
let textLength = ref(0);
let newVideoSting = ref('');

// 视频认证开关
let videoAuthSwitchVisible = ref(false);
let videoAuthStatus = ref('');

// 同心锁开关
let loveLocksVisible = ref(false);
let loveLocksStatus = ref('');

// 女用户建联开关
let womanOpenVisible = ref(false);
let womanOpenStatus = ref('');

// 男用户建联开关
let manOpenVisible = ref(false);
let manOpenStatus = ref('');

// 不限制注册安卓ID管理
let androidIdManagerVisible = ref(false);
let androidIdArray = ref<string[]>([]);
let androidIdCount = ref(0);
let newAndroidId = ref('');

// 男用户新人标签配置
let manLabelConfigVisible = ref(false);
let manLabelStatus = ref('');
let manTime = ref('');
let newManTime = ref('');

// 女用户新人标签配置
let womanLabelConfigVisible = ref(false);
let womanLabelStatus = ref('');
let womanTime = ref('');
let newWomanTime = ref('');

// 充值页提示语配置
let rechargeTextConfigVisible = ref(false);
let currentRechargeText = ref('');
let rechargeTextLength = ref(0);
let newRechargeText = ref('');

// 消息卡赠送数量配置
let msgCardConfigVisible = ref(false);
let currentMsgCardLimit = ref('0');
let newMsgCardLimit = ref('');

// 注册赠送消息卡配置
let msgCardNumConfigVisible = ref(false);
let currentMsgCardNum = ref('0');
let newMsgCardNum = ref('');

// 每个人使用消息卡张数限制配置
let limitCountConfigVisible = ref(false);
let currentLimitCount = ref('10');
let newLimitCount = ref('');

const getWechatPrice = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/wechatPrice');
    if (res.data.code === 200) {
      wechatPrice.value = res.data.data;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取微信价格失败');
  }
};

const updateWechatPrice = async () => {
  if (!wechatPrice.value) {
    ElMessage.warning('请输入价格');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/wechatPrice', { price: wechatPrice.value });
    if (res.data.code === 200) {
      ElMessage.success('价格更新成功');
      wechatPrice.value = '';
      getWechatPrice();
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新价格失败');
  }
};

// 缘分推送开关相关方法
const getPredestinationStatus = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/predestinationSwitch');
    if (res.data.code === 200) {
      predestinationStatus.value = res.data.data.status;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取缘分推送状态失败');
  }
};

const togglePredestinationSwitch = async (isOpen: boolean) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/predestinationSwitch', { isOpen });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getPredestinationStatus(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('操作失败');
  }
};

// 审核包认证管理相关方法
const getExamineConfig = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/examineConfig');
    if (res.data.code === 200) {
      currentExamineVerify.value = res.data.data.examineVerify;
      channelCount.value = res.data.data.channelCount;
      currentVersion.value = res.data.data.version;
      // 自动将现有配置加载到编辑框
      newExamineVerify.value = res.data.data.examineVerify;
      // 自动将当前版本加载到版本编辑框
      newVersion.value = res.data.data.version;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取审核包配置失败');
  }
};

const updateExamineVerify = async () => {
  if (!newExamineVerify.value) {
    ElMessage.warning('请输入渠道认证配置');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/examineConfig', { examineVerify: newExamineVerify.value });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getExamineConfig(); // 重新获取状态（会自动更新输入框）
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新渠道配置失败');
  }
};

const updateVersion = async () => {
  if (!newVersion.value) {
    ElMessage.warning('请输入版本号');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/examineConfig', { version: newVersion.value });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getExamineConfig(); // 重新获取状态（会自动更新输入框）
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新版本失败');
  }
};

// 视频认证示例文案管理相关方法
const getVideoStingConfig = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/videoStingConfig');
    if (res.data.code === 200) {
      currentVideoSting.value = res.data.data.videoSting;
      textLength.value = res.data.data.textLength;
      // 自动将当前文案加载到编辑框
      newVideoSting.value = res.data.data.videoSting;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取视频认证文案失败');
  }
};

const updateVideoSting = async () => {
  if (!newVideoSting.value) {
    ElMessage.warning('请输入示例文案');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/videoStingConfig', { videoSting: newVideoSting.value });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getVideoStingConfig(); // 重新获取状态（会自动更新输入框）
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新文案失败');
  }
};

// 视频认证开关相关方法
const getVideoAuthStatus = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/videoAuthSwitch');
    if (res.data.code === 200) {
      videoAuthStatus.value = res.data.data.status;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取视频认证状态失败');
  }
};

const toggleVideoAuthSwitch = async (isNeed: boolean) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/videoAuthSwitch', { isNeed });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getVideoAuthStatus(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('操作失败');
  }
};

// 同心锁开关相关方法
const getLoveLocksStatus = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/loveLocksSwitch');
    if (res.data.code === 200) {
      loveLocksStatus.value = res.data.data.status;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取同心锁状态失败');
  }
};

const toggleLoveLocksSwitch = async (isOpen: boolean) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/loveLocksSwitch', { isOpen });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getLoveLocksStatus(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('操作失败');
  }
};

// 女用户建联开关相关方法
const getWomanOpenStatus = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/womanOpenSwitch');
    if (res.data.code === 200) {
      womanOpenStatus.value = res.data.data.status;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取女用户建联状态失败');
  }
};

const toggleWomanOpenSwitch = async (isOpen: boolean) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/womanOpenSwitch', { isOpen });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getWomanOpenStatus(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('操作失败');
  }
};

// 男用户建联开关相关方法
const getManOpenStatus = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/manOpenSwitch');
    if (res.data.code === 200) {
      manOpenStatus.value = res.data.data.status;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取男用户建联状态失败');
  }
};

const toggleManOpenSwitch = async (isOpen: boolean) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/manOpenSwitch', { isOpen });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getManOpenStatus(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('操作失败');
  }
};

// 不限制注册安卓ID管理相关方法
const getAndroidIdList = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/androidIdListManager', { action: 'list' });
    if (res.data.code === 200) {
      androidIdArray.value = res.data.data.androidId_array || [];
      androidIdCount.value = res.data.data.count || 0;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取AndroidId列表失败');
  }
};

const addAndroidId = async () => {
  if (!newAndroidId.value.trim()) {
    ElMessage.warning('请输入AndroidId');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/androidIdListManager', {
      action: 'add',
      androidId: newAndroidId.value.trim()
    });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      newAndroidId.value = ''; // 清空输入框
      getAndroidIdList(); // 重新获取列表
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('添加AndroidId失败');
  }
};

const removeAndroidId = async (androidId: string) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/androidIdListManager', {
      action: 'remove',
      androidId
    });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getAndroidIdList(); // 重新获取列表
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('删除AndroidId失败');
  }
};

// 男用户新人标签配置相关方法
const getManLabelConfig = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/manLabelConfig');
    if (res.data.code === 200) {
      manLabelStatus.value = res.data.data.labelStatus;
      manTime.value = res.data.data.manTime;
      newManTime.value = res.data.data.manTime; // 预填充当前值
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取男用户新人标签配置失败');
  }
};

const toggleManLabel = async (isOpen: boolean) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/manLabelConfig', { isOpen });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getManLabelConfig(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('操作失败');
  }
};

const updateManTime = async () => {
  if (!newManTime.value || Number(newManTime.value) <= 0) {
    ElMessage.warning('请输入大于0的天数');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/manLabelConfig', { days: newManTime.value });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getManLabelConfig(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新天数失败');
  }
};

// 女用户新人标签配置相关方法
const getWomanLabelConfig = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/womanLabelConfig');
    if (res.data.code === 200) {
      womanLabelStatus.value = res.data.data.labelStatus;
      womanTime.value = res.data.data.womanTime;
      newWomanTime.value = res.data.data.womanTime; // 预填充当前值
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取女用户新人标签配置失败');
  }
};

const toggleWomanLabel = async (isOpen: boolean) => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/womanLabelConfig', { isOpen });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getWomanLabelConfig(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('操作失败');
  }
};

const updateWomanTime = async () => {
  if (!newWomanTime.value || Number(newWomanTime.value) <= 0) {
    ElMessage.warning('请输入大于0的天数');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/womanLabelConfig', { days: newWomanTime.value });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getWomanLabelConfig(); // 重新获取状态
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新天数失败');
  }
};

// 充值页提示语配置相关方法
const getRechargeTextConfig = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/rechargeTextConfig');
    if (res.data.code === 200) {
      currentRechargeText.value = res.data.data.rechargeText;
      rechargeTextLength.value = res.data.data.rechargeText.length;
      // 自动将当前提示语加载到编辑框
      newRechargeText.value = res.data.data.rechargeText;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取充值页提示语失败');
  }
};

const updateRechargeText = async () => {
  if (!newRechargeText.value.trim()) {
    ElMessage.warning('请输入提示语');
    return;
  }

  try {
    const res = await secretKeyApiClient.post('/app/yuai/rechargeTextConfig', {
      rechargeText: newRechargeText.value.trim()
    });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getRechargeTextConfig(); // 重新获取状态（会自动更新输入框）
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新提示语失败');
  }
};

// 消息卡赠送数量配置相关方法
const getMsgCardConfig = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/msgCardPointLimitConfig');
    if (res.data.code === 200) {
      currentMsgCardLimit.value = res.data.data.msgCardPointLimit;
      // 自动将当前配置加载到编辑框
      newMsgCardLimit.value = res.data.data.msgCardPointLimit;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取消息卡配置失败');
  }
};

const updateMsgCardLimit = async () => {
  if (newMsgCardLimit.value === '' || newMsgCardLimit.value === null) {
    ElMessage.warning('请输入消息卡赠送数量');
    return;
  }

  const limit = Number(newMsgCardLimit.value);
  if (isNaN(limit) || limit < 0) {
    ElMessage.warning('消息卡赠送数量必须为非负整数');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/msgCardPointLimitConfig', {
      msgCardPointLimit: limit
    });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getMsgCardConfig(); // 重新获取状态（会自动更新输入框）
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新消息卡配置失败');
  }
};

// 注册赠送消息卡配置相关方法
const getMsgCardNumConfig = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/msgCardNumConfig');
    if (res.data.code === 200) {
      currentMsgCardNum.value = res.data.data.msgCardNum;
      // 自动将当前配置加载到编辑框
      newMsgCardNum.value = res.data.data.msgCardNum;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取注册赠送消息卡配置失败');
  }
};

const updateMsgCardNumConfig = async () => {
  if (newMsgCardNum.value === '' || newMsgCardNum.value === null) {
    ElMessage.warning('请输入注册赠送消息卡数量');
    return;
  }

  const limit = Number(newMsgCardNum.value);
  if (isNaN(limit) || limit < 0) {
    ElMessage.warning('注册赠送消息卡数量必须为非负整数');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/msgCardNumConfig', {
      msgCardNum: limit
    });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getMsgCardNumConfig(); // 重新获取状态（会自动更新输入框）
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新注册赠送消息卡配置失败');
  }
};

// 每个人使用消息卡张数限制配置相关方法
const getLimitCountConfig = async () => {
  try {
    const res = await secretKeyApiClient.get('/app/yuai/limitCountConfig');
    if (res.data.code === 200) {
      currentLimitCount.value = res.data.data.limitCount;
      // 自动将当前配置加载到编辑框
      newLimitCount.value = res.data.data.limitCount;
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取每个人使用消息卡张数限制配置失败');
  }
};

const updateLimitCountConfig = async () => {
  if (newLimitCount.value === '' || newLimitCount.value === null) {
    ElMessage.warning('请输入每个人使用消息卡张数限制');
    return;
  }

  const limit = Number(newLimitCount.value);
  if (isNaN(limit) || limit < 0) {
    ElMessage.warning('每个人使用消息卡张数限制必须为非负整数');
    return;
  }

  try {
    const res = await secretKeyApiClient.get('/app/yuai/limitCountConfig', {
      limitCount: limit
    });
    if (res.data.code === 200) {
      ElMessage.success(res.data.message);
      getLimitCountConfig(); // 重新获取状态（会自动更新输入框）
    } else {
      ElMessage.error(res.data.message);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('更新每个人使用消息卡张数限制配置失败');
  }
};
</script>

<style scoped lang="scss">
.topbnt {
  margin-right: 10px;
}

// 统一弹窗宽度
:deep(.el-dialog) {
  width: 50% !important;
}

// 禁止充值列表项样式
.ban-item {
  display: inline-flex;
  align-items: center;
  margin: 2px;
  padding: 2px 4px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  background-color: #f5f7fa;
  white-space: nowrap;
  line-height: 1;

  .ban-id {
    margin-right: 4px;
    font-size: 12px;
    color: #606266;
    word-break: keep-all;
    white-space: nowrap;
    line-height: 1;
  }

  .ban-delete-btn {
    margin-left: 4px;
    flex-shrink: 0;
    padding: 1px 3px;
    font-size: 10px;
    min-height: 16px;
    height: 16px;
    line-height: 1;
  }
}

// 安卓ID列表项样式
.android-item {
  display: inline-flex;
  align-items: center;
  margin: 2px;
  padding: 2px 4px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  background-color: #fff;
  white-space: nowrap;
  line-height: 1;

  .android-id {
    margin-right: 4px;
    font-size: 12px;
    color: #606266;
    word-break: keep-all;
    white-space: nowrap;
    line-height: 1;
  }

  .android-delete-btn {
    margin-left: 4px;
    flex-shrink: 0;
    padding: 1px 3px;
    font-size: 10px;
    min-height: 16px;
    height: 16px;
    line-height: 1;
  }
}

.header-nav {
  height: 60px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 32px 0 20px;

  .bar-left {
    display: flex;
    align-items: center;

    .zhuan {
      transform: rotateZ(45deg);
      transition: all 0.3s linear;
      //color: -webkit-linear-gradient(270deg, rgba(119, 33, 184, 0.66) 0%, #B979EF 100%);
      margin-right: 5px;
      cursor: pointer;
    }

    .buzhuan {
      transform: rotateZ(0deg);
      transition: all 0.3s linear;
      margin-right: 5px;
      cursor: pointer;
    }
  }

  .el-button:focus-visible {
    outline: none;
  }

  .bar-right {
    display: flex;
    justify-content: center;
    align-items: center;

    .baracc {
      margin-right: 10px;
      cursor: pointer;
    }


    .el-avatar {
      margin-right: 10px;
    }
  }
}
</style>
