<template>
  <div class="report">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form
              ref="queryFormRef"
              :inline="true"
              :model="queryForm"
              class="demo-form-inline"
          >
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="序号" prop="id">
              <el-input v-model="queryForm.id"/>
            </el-form-item>
            <el-form-item label="举报人ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="被举报人ID" prop="targetUserId">
              <el-input v-model="queryForm.targetUserId"/>
            </el-form-item>
            <el-form-item label="内容ID" prop="contentId">
              <el-input v-model="queryForm.contentId"/>
            </el-form-item>
            <el-form-item label="业务类型" prop="businessType">
              <el-select
                  class="header-select"
                  v-model="queryForm.businessType"
                  placeholder="请选择"
              >
                <el-option label="全部" :value="opNull"/>
                <el-option label="用户举报" :value="0"/>
                <el-option label="动态举报" :value="1"/>
                <el-option label="评论举报" :value="2"/>
              </el-select>
            </el-form-item>
            <el-form-item label="举报类型" prop="type">
              <el-select
                  class="header-select"
                  v-model="queryForm.type"
                  placeholder="请选择"
              >
                <el-option label="全部" :value="opNull"/>
                <el-option label="低俗色情" :value="0"/>
                <el-option label="广告" :value="1"/>
                <el-option label="政治" :value="2"/>
                <el-option label="虚假欺骗" :value="3"/>
                <el-option label="侵权" :value="4"/>
                <el-option label="违禁内容" :value="5"/>
                <el-option label="其他原因" :value="6"/>
              </el-select>
            </el-form-item>
            <el-form-item label="举报进度" prop="handleStatus">
              <el-select
                  class="header-select"
                  v-model="queryForm.handleStatus"
                  placeholder="请选择"
              >
                <el-option label="全部" :value="opNull"/>
                <el-option label="处理中" :value="0"/>
                <el-option label="忽略" :value="1"/>
                <el-option label="处理结束" :value="2"/>
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择" prop="date">
              <el-date-picker
                  v-model="date"
                  :clearable="false"
                  type="daterange"
                  unlink-panels
                  value-format="x"
                  format="YYYY-MM-DD"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :shortcuts="shortcuts"
                  size="default"
                  @change="timeChange"
              />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryReport">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="ReportTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="序号" min-width="80" fixed></el-table-column>
        <el-table-column prop="userId" label="举报人ID" min-width="120"></el-table-column>
        <el-table-column
            prop="targetUserId"
            label="被举报人ID"
            min-width="120"
        ></el-table-column>
        <el-table-column
            prop="contentId"
            label="内容ID"
            min-width="120"
        ></el-table-column>
        <el-table-column prop="businessType" label="业务类型" min-width="120">
          <template #default="scope">{{
              scope.row.businessType == "user"
                  ? "用户举报"
                  : scope.row.businessType == "dynamic"
                      ? "动态举报"
                      : scope.row.businessType == "comment"
                          ? "评论举报"
                          : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="举报类型" min-width="120"></el-table-column>
        <el-table-column
            prop="content"
            label="内容描述"
            min-width="120"
            show-overflow-tooltip
        >
          <template #default="scope">
            <el-tooltip
                class="box-item"
                effect="dark"
                :content="scope.row.content"
                placement="top-start"
            >
              {{ scope.row.content }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
            prop="lastOperator"
            label="最后操作人"
            min-width="120"
        ></el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="180">
          <template #default="scope">
            {{ getYMDHMS("-", ":", scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="handleStatus" label="举报进度" min-width="120">
          <template #default="scope">{{
              scope.row.handleStatus == "wait_confirm"
                  ? "处理中"
                  : scope.row.handleStatus == "ignore"
                      ? "忽略"
                      : scope.row.handleStatus == "finished"
                          ? "处理结束"
                          : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="reportResource" label="举报资源" min-width="240">
          <template #default="scope">
            <div
                v-if="scope.row.reportResourceType == 1"
                style="
                display: grid;
                grid-gap: 5px;
                grid-template-columns: 80px 80px 
              "
            >
              <el-image
                  style="width: 80px; height: 80px"
                  v-for="(item, index) in scope.row.reportResource"
                  :key="index"
                  :src="item"
                  :zoom-rate="1.2"
                  :preview-src-list="scope.row.reportResource"
                  :initial-index="0"
                  fit="cover"
                  :preview-teleported="true"
                  :hide-on-click-modal="true"
              />
            </div>
            <div v-else-if="scope.row.reportResourceType == 2">
              <video
                  width="50"
                  v-for="(v, i) in scope.row.reportResource"
                  :key="i"
                  :src="v"
              ></video>
            </div>
            <div v-else>
              <audio
                  width="50"
                  v-for="(v, i) in scope.row.reportResource"
                  :key="i"
                  :src="v"
                  controls
              ></audio>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reportResourceType" label="举报资源类型" min-width="120">
          <template #default="scope">
            {{
              scope.row.reportResourceType == 1
                  ? "图片"
                  : scope.row.reportResourceType == 2
                      ? "视频"
                      : scope.row.reportResourceType == 3
                          ? "音频"
                          : ""
            }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="260">
          <template #default="scope">
            <div v-if="scope.row.handleStatus != 'ignore'">
              <el-popconfirm title="是否确定?" @confirm="editWaitConfirm(scope.row, 0)">
                <template #reference>
                  <el-button
                      v-show="
                    scope.row.handleStatus != 'wait_confirm' &&
                    scope.row.handleStatus != 'finished'
                  "
                      size="small"
                      type="primary"
                  >处理中
                  </el-button
                  >
                </template>
              </el-popconfirm>
              <el-popconfirm title="是否忽略?" @confirm="editIgnore(scope.row, 1)">
                <template #reference>
                  <el-button
                      v-show="
                    scope.row.handleStatus != 'ignore' &&
                    scope.row.handleStatus != 'finished'
                  "
                      size="small"
                  >忽略
                  </el-button
                  >
                </template>
              </el-popconfirm>
              <el-popconfirm title="是否确定?" @confirm="editFinished(scope.row, 2)">
                <template #reference>
                  <el-button
                      v-show="scope.row.handleStatus != 'finished'"
                      size="small"
                      type="success"
                  >处理结束
                  </el-button
                  >
                </template>
              </el-popconfirm>
              <el-popconfirm title="是否删除?" @confirm="editWaitConfirm(scope.row, 3)">
                <template #reference>
                  <el-button
                      size="small"
                      type="danger"
                  >删除
                  </el-button
                  >
                </template>
              </el-popconfirm>
        <el-button size="small" type="warning" @click="showWarnDialog(scope.row)">
          警告
        </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20]"
          :small="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
          :background="true"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
      <el-dialog v-model="editShow" title="操作原因">
        <el-form :model="descForm">
          <el-form-item label="类型">
            <el-select v-model="descForm.editValue" placeholder="选择操作原因">
              <el-option label="低俗色情" value="低俗色情"/>
              <el-option label="政治" value="政治"/>
              <el-option label="广告" value="广告"/>
              <el-option label="虚假欺骗" value="虚假欺骗"/>
              <el-option label="侵权" value="侵权"/>
              <el-option label="违禁内容" value="违禁内容"/>
              <el-option label="辱骂" value="辱骂"/>
              <el-option label="其他" value="其他"/>
            </el-select>
          </el-form-item>
          <el-form-item label="原因" v-if="descForm.editValue == '其他'">
            <el-input v-model="descForm.editValueIpt"/>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editShow = false">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
  <el-dialog v-model="warnDialogVisible" title="警告" width="40%">
    <el-form :model="warnForm">
      <el-form-item label="警告内容">
        <el-input v-model="warnForm.operatorDesc" type="textarea" rows="6" />
      </el-form-item>
    </el-form>
    <div class="quick-content" @click="warnForm.operatorDesc = `【投诉通知】:收到ID ${currentUserId} 😡用户投诉经核实你存在【礼物解锁、平台屏蔽】、【亲密度解锁联系方式】等涉嫌违规行为请尽快和对方解释沟通。若对方撤诉，则平台不再追究;若在48小时内未处理完纠纷，平台将会对账号进行包括不限于冻结、封禁处理。`">
      【投诉通知】:收到ID用户😡投诉经核实你存在【礼物解锁、平台屏蔽】、【亲密度解锁联系方式】等涉嫌违规行为请尽快和对方解释沟通。若对方撤诉，则平台不再追究;若在48小时内未处理完纠纷，平台将会对账号进行包括不限于冻结、封禁处理。
    </div>
        <div class="quick-content" @click="warnForm.operatorDesc = `【投诉撤销通知】:用户 ${currentUserId} 🥰的投诉已经撤销,请时刻谨记和遵守平台规则,请时刻谨记和遵守平台规则,请时刻谨记和遵守平台规则!!!`">
      【投诉撤销通知】:用户🥰的投诉已经撤销,请时刻谨记和遵守平台规则,请时刻谨记和遵守平台规则,请时刻谨记和遵守平台规则!!!
    </div>
    <template #footer>
      <el-button @click="warnDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="warnUser">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import type {FormInstance} from "element-plus";
import pageHook from "@/hooks/pageHook";
import {getYMDHMS} from "@/utils/date";
import {queryReport_api, modifyReport_api, warnUserzb_api} from "@/api/foregrounduser";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} = pageHook(
    queryReport
);
const queryFormRef = ref<FormInstance>();
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;

let date = ref<[Date, Date]>([new Date(past7daysStart), new Date(todayEnd)]);
let opNull = ref<any>(null);
let ReportTab = ref([]);
let queryForm = reactive<any>({
  id: null,
  userId: null,
  targetUserId: null,
  contentId: null,
  businessType: null,
  type: null,
  handleStatus: null,
  startTime: past7daysStart,
  endTime: todayEnd,
  applicationId: 'com.dongxin.app'
});

let editShow = ref<boolean>(false);
let descForm = reactive<any>({
  id: null,
  editValue: null,
  editValueIpt: null,
  displayStatus: null,
});

const warnDialogVisible = ref(false)
const warnForm = reactive({
  targetUserId: '',
  operatorDesc: ''
})

// 当前选中的举报人ID
const currentUserId = ref('')

const showWarnDialog = (row: any) => {
  currentUserId.value = row.userId
  warnForm.targetUserId = row.targetUserId
  warnForm.operatorDesc = ''
  warnDialogVisible.value = true
}

const warnUser = async () => {
  await warnUserzb_api({
    userId: warnForm.targetUserId,
    operatorDesc: warnForm.operatorDesc
  })
  warnDialogVisible.value = false
  warnForm.operatorDesc = ''
  queryReport()
}



const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

async function getReport() {
  let res = await queryReport_api({
    startTime: past7daysStart,
    endTime: todayEnd,
    applicationId: queryForm.applicationId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });

  let {data, total} = res.data;
  data.forEach((v: any) => {
    if (v.type == "porn") {
      v.type = "低俗色情";
    } else if (v.type == "suspected_politics") {
      v.type = "涉政";
    } else if (v.type == "advertising") {
      v.type = "广告";
    } else if (v.type == "fraud") {
      v.type = "诈骗";
    } else if (v.type == "tort") {
      v.type = "侵权举报";
    } else if (v.type == "prohibited_content") {
      v.type = "违禁类容";
    } else if (v.type == "other") {
      v.type = "其他原因";
    }
  });
  ReportTab.value = data;
  totalNum.value = total;
}

getReport();

// 处理中
const editWaitConfirm = async (row: any, handleStatus: any) => {
  await modifyReport_api({id: row.id, handleStatus});
  queryReport();
};
// 忽略
const editIgnore = async (row: any, handleStatus: any) => {
  await modifyReport_api({id: row.id, handleStatus});
  queryReport();
};
// 处理结束
const editFinished = (row: any, handleStatus: any) => {
  descForm.editValue = null;
  descForm.editValueIpt = null;
  descForm.id = row.id;
  descForm.displayStatus = handleStatus;
  editShow.value = true;
};

const editSubMit = async () => {
  if (descForm.editValue == "其他") {
    descForm.editValue = descForm.editValueIpt;
  }
  await modifyReport_api({
    id: descForm.id,
    handleStatus: descForm.displayStatus,
    operatorDesc: descForm.editValue,
  });
  queryReport();
  editShow.value = false;
};

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function queryReport() {
  let res = await queryReport_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });

  let {data, total} = res.data;
  data.forEach((v: any) => {
    if (v.type == "porn") {
      v.type = "低俗色情";
    } else if (v.type == "suspected_politics") {
      v.type = "涉政";
    } else if (v.type == "advertising") {
      v.type = "广告";
    } else if (v.type == "fraud") {
      v.type = "诈骗";
    } else if (v.type == "tort") {
      v.type = "侵权举报";
    } else if (v.type == "other") {
      v.type = "其他原因";
    }
  });
  ReportTab.value = data;
  totalNum.value = total;
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  date.value = [new Date(past7daysStart), new Date(todayEnd)];
  queryForm.startTime = past7daysStart;
  queryForm.endTime = todayEnd;
  formEl.resetFields();
  queryReport();
};
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  queryReport();
}
</script>

<style lang="scss" scoped>
.quick-content {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  color: #606266;
  line-height: 1.5;
  cursor: pointer;
}
.report {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    .el-select {
      width: 100%;
    }
  }

  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 350px;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
