<template>
  <div class="foregrounduser">
    <!--    //用户列表-->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form
              ref="queryFormRef"
              :inline="true"
              :model="queryForm"
              class="demo-form-inline"
          >
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" :skipFirstSelect="true"
                          @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="queryForm.mobile"/>
            </el-form-item>
            <el-form-item label="邀请码" prop="inviteCode">
              <el-input v-model="queryForm.inviteCode"/>
            </el-form-item>
            <el-form-item label="渠道选择">
              <el-cascader
                  popper-class="cascaderRadio"
                  v-model="queryForm.data"
                  :options="options"
                  :props="{
                  expandTrigger: 'hover',
                  label: 'channelName',
                  children: 'childChannels',
                  value: 'channelCode',
                  checkStrictly: true,
                }"
                  clearable
              >
                <template #default="{ node, data }">
                  <span
                      class="custom-node leaf"
                      v-if="node.isLeaf == 0"
                      @mouseenter="mouseenterLeaf(node)"
                  >{{ data.channelName }}</span
                  >
                  <span
                      v-else
                      class="custom-node noLeaf"
                      @mouseenter="mouseenterSubcat(node)"
                  >{{ data.channelName }}</span
                  >
                </template>
              </el-cascader>
            </el-form-item>
            <el-form-item label="用户性别" prop="gender">
              <el-select
                  class="header-select"
                  v-model="queryForm.gender"
                  placeholder="请选择"
              >
                <el-option label="全部" :value="opNull"/>
                <el-option label="未知" :value="0"/>
                <el-option label="男" :value="1"/>
                <el-option label="女" :value="2"/>
              </el-select>
            </el-form-item>
            <el-form-item label="用户状态" prop="status">
              <el-select v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="正常" :value="0"/>
                <el-option label="已注销" :value="1"/>
                <el-option label="被封禁" :value="2"/>
                <el-option label="临时封禁" :value="3"/>
              </el-select>
            </el-form-item>
            <el-form-item label="真人状态" prop="realAvatarAuthState">
              <el-select v-model="queryForm.realAvatarAuthState" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option
                    v-for="(v, i) in option"
                    :key="i"
                    :label="v.state"
                    :value="i"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="实名状态" prop="realNameAuthState">
              <el-select v-model="queryForm.realNameAuthState" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option
                    v-for="(v, i) in option"
                    :key="i"
                    :label="v.state"
                    :value="i"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="视频认证" prop="videoState">
              <el-select v-model="queryForm.videoState" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option
                    v-for="(v, i) in videoOption"
                    :key="i"
                    :label="v.state"
                    :value="i"
                />
              </el-select>
            </el-form-item>
            <!--                        <el-form-item label="大哥" prop="status">-->
            <!--              <el-select v-model="queryForm.chargeType" placeholder="请选择">-->
            <!--                <el-option label="全部" :value="opNull" />-->
            <!--                <el-option label="是" :value="2" />-->
            <!--                <el-option label="不是" :value="1" />-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
            <el-form-item label="时间选择" prop="date">
              <el-date-picker
                  v-model="date"
                  :clearable="false"
                  type="daterange"
                  unlink-panels
                  value-format="x"
                  format="YYYY-MM-DD"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :shortcuts="shortcuts"
                  size="default"
                  @change="timeChange"
              />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="foreground" style="width: 100%; height: 100%">
          <el-table-column prop="userId" label="用户ID" min-width="100"></el-table-column>
        <el-table-column prop="nickname" label="昵称" min-width="150">
          <template #default="scope">
            <div style="display: flex; align-items: center;">
              <span>{{ scope.row.nickname }}</span>
              <sup v-if="scope.row.applicationId === 'com.dongxin.app'"
                   style="margin-left: 4px; font-size: 10px; color: #f56c6c;border: 1px solid #f56c6c;border-radius: 5px;">遇爱</sup>
              <sup v-if="scope.row.applicationId === 'com.dingkunianwo.app'"
                   style="margin-left: 4px; font-size: 10px; color: #9d52ea;border: 1px solid #9d52ea;border-radius: 5px;">念我</sup>
              <sup v-if="scope.row.channel === 'vivo_vivo_vivo'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">vivo</sup>
              <sup v-else-if="scope.row.channel === 'huawei_huawei_huawei'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">华为</sup>
              <sup v-else-if="scope.row.channel === 'official_official_official'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">官方</sup>
              <sup v-else-if="scope.row.channel === 'oppo_oppo_oppo'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">oppo</sup>
              <sup v-else-if="scope.row.channel === 'new_new_new'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New</sup>
              <sup v-else-if="scope.row.channel === 'new1_new1_new1'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New1</sup>
              <sup v-else-if="scope.row.channel === 'new2_new2_new2'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New2</sup>
              <sup v-else-if="scope.row.channel === 'dage_dage_dage'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">大哥</sup>
              <sup v-else-if="scope.row.channel === 'honor_honor_honor'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">荣耀</sup>
              <sup v-else-if="scope.row.channel === 'ying_ying_ying'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">YY宝</sup>
              <sup v-else-if="scope.row.channel === 'juliang_juliang_juliang'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">抖音</sup>
              <sup v-else-if="scope.row.channel === 'xiaomi_xiaomi_xiaomi'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">小米</sup>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="头像" min-width="113">
          <template #default="scope">
            <el-image
                style="width: 80px; height: 80px"
                :src="scope.row.avatar"
                :zoom-rate="1.2"
                :preview-src-list="avatarList"
                :initial-index="0"
                fit="cover"
                :preview-teleported="true"
                :hide-on-click-modal="true"
                @click="resAvatar(scope.row)"
            />
          </template>
        </el-table-column>
        <!--        <el-table-column label="包名" min-width="180" prop="applicationId"></el-table-column>-->
        <el-table-column prop="mobile" label="手机号" min-width="150"></el-table-column>
        <el-table-column label="性别" min-width="70">
          <template #default="scope">{{
              scope.row.gender == 1 ? "男" : scope.row.gender == 2 ? "女" : "未知"
            }}
          </template>
        </el-table-column>

        <el-table-column label="注册时间" min-width="180">
          <template #default="scope">{{
              getYMDHMS("-", ":", scope.row.registerTime)
            }}
          </template>
        </el-table-column>
        <el-table-column label="真人认证" min-width="100">
          <template #default="scope">{{ scope.row.realAvatarAuthState }}</template>
        </el-table-column>
        <el-table-column label="实名认证" min-width="100">
          <template #default="scope">{{ scope.row.realNameAuthState }}</template>
        </el-table-column>
        <el-table-column label="视频认证" min-width="100">
          <template #default="scope">{{ scope.row.videoState }}</template>
        </el-table-column>
        <el-table-column label="禁言状态" min-width="100">
          <template #default="scope">{{
              scope.row.muteStatus == 2
                  ? "临时禁言"
                  : scope.row.muteStatus == 3
                      ? "永久禁言"
                      : "正常"
            }}
          </template>
        </el-table-column>
        <el-table-column label="用户状态" min-width="100">
          <template #default="scope">{{
              scope.row.status == 0
                  ? "正常"
                  : scope.row.status == 1
                      ? "已注销"
                      : scope.row.status == 2
                          ? "被封禁"
                          : scope.row.status == 3
                              ? "临时封禁"
                              : ""
            }}
          </template>
        </el-table-column>

        <el-table-column label="注册IP" min-width="180" prop="registerIp"></el-table-column>
        <!--        <el-table-column label="城市" min-width="180" prop="city"></el-table-column>-->
        <!--        <el-table-column label="是否不显示城市" min-width="180" prop="notHometown"></el-table-column>-->
        <!--        <el-table-column label="注册时间" min-width="180" prop="forbidChangeAvatarReleaseTime"></el-table-column>-->
        <el-table-column fixed="right" label="操作" width="270">
          <template #default="scope">
            <el-button @click="detailsUser(scope.row)" size="small">查看详情</el-button>
            <el-button
              v-if="!scope.row.bindInviteCode"
              @click="editBind(scope.row)"
              size="small"
              type="primary"
            >绑定邀请人
            </el-button>
            <el-button
              v-if="scope.row.bindInviteCode"
              @click="unbindUser(scope.row.userId)"
              size="small"
              type="danger"
            >解绑</el-button>


            <el-select style="margin-top: 10px;margin-left: 35px"
                       v-model="scope.row.statusPunish"
                       placeholder="请选择类型"
                       @change="editSelect(scope.row)"
                       class="statusPunish"
                       size="small"
            >
              <el-option label="修改收益比例" value="修改收益比例"/>
              <el-option label="重置头像" value="重置头像"/>
              <el-option label="修改昵称" value="修改昵称"/>
              <el-option label="警告" value="警告"/>
              <el-option label="禁言" value="禁言"/>
              <el-option label="封禁账号" value="封禁账号"/>
              <el-option label="冻结提现" value="冻结提现"/>
              <el-option label="限制用户音视频" value="限制用户音视频"/>
              <el-option label="封禁IP" value="封禁IP"/>
              <el-option label="封禁设备" value="封禁设备"/>
              <el-option label="解除封禁" value="解除封禁"/>
              <el-option label="解除禁言" value="解除禁言"/>
              <el-option label="解除限制用户音视频" value="解除限制用户音视频"/>
              <el-option label="解除冻结提现" value="解除冻结提现"/>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[3, 5, 10, 20]"
          :small="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
          :background="true"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />

      <!-- 详情 -->
      <div class="details_dialog">
        <el-dialog
          v-model="detailsShow"
          title="用户详情"
          width="60%"
          :center="true"
          :modal="true"
          :close-on-click-modal="false"
          :close-on-press-escape="true"
          destroy-on-close
        >
          <el-form :model="detailsList" class="detailsForm">
            <el-scrollbar>
              <div class="details-lf">
                <div class="details-box">
                  <p class="details-title">基本资料</p>
                  <div>
                    <el-form-item label="头像:">
                      <el-avatar
                          shape="square"
                          :size="50"
                          fit="cover"
                          :src="detailsList.avatar"
                      />
                    </el-form-item>
                    <el-form-item label="用户ID:">
                      <span>{{ detailsList.userId }}</span>
                    </el-form-item>
                    <el-form-item label="昵称:">
                      <span>{{ detailsList.nickname }}</span>
                    </el-form-item>
                    <el-form-item label="手机号:">
                      <span>{{ detailsList.mobile }}</span>
                    </el-form-item>
                    <el-form-item label="性别:">
                      <span>{{
                          detailsList.gender == 1
                              ? "男"
                              : detailsList.gender == 2
                                  ? "女"
                                  : "未知"
                        }}</span>
                    </el-form-item>
                    <el-form-item label="年龄:">
                      <span>{{ detailsList.age }}</span>
                    </el-form-item>
                    <el-form-item label="用户状态:">
                      <span>{{
                          detailsList.status == 0
                              ? "正常"
                              : detailsList.status == 1
                                  ? "已注销"
                                  : detailsList.status == 2
                                      ? "被封禁"
                                      : ""
                        }}</span>
                    </el-form-item>
                    <el-form-item label="禁言状态:">
                      <span>{{
                          detailsList.status == 1
                              ? "正常"
                              : detailsList.status == 2
                                  ? "临时"
                                  : detailsList.status == 3
                                      ? "永久"
                                      : ""
                        }}</span>
                    </el-form-item>
                    <el-form-item label="在线状态:">
                      <span>{{
                          detailsList.online == 1
                              ? "在线"
                              : detailsList.online == 0
                                  ? "离线"
                                  : ""
                        }}</span>
                    </el-form-item>
                    <el-form-item label="上次在线时间:">
                      <span>{{ detailsList.lastOnlineTime }}</span>
                    </el-form-item>
                    <el-form-item label="邀请码:">
                      <span>{{ detailsList.inviteCode }}</span>
                    </el-form-item>
                    <el-form-item label="绑定的邀请码:">
                      <span>{{ detailsList.bindInviteCode }}</span>
                    </el-form-item>
                    <el-form-item label="首充套餐id:">
                      <span>{{ detailsList.firstChargePackageId }}</span>
                    </el-form-item>
                    <el-form-item label=" 首充金额:">
                      <span>{{ detailsList.firstChargeAmount }}</span>
                    </el-form-item>
                    <el-form-item label=" 首充时间:">
                      <span v-show="detailsList.firstChargeAmountTime">{{
                          getYMDHMS("-", ":", detailsList.firstChargeAmountTime)
                        }}</span>
                    </el-form-item>
                    <el-form-item label="注册时间:">
                      <span>{{ getYMDHMS("-", ":", detailsList.registerTime) }}</span>
                    </el-form-item>
                    <el-form-item label="上报激活时间:">
                      <span v-show="detailsList.reportActiveTime">{{
                          getYMDHMS("-", ":", detailsList.reportActiveTime)
                        }}</span>
                    </el-form-item>
                    <el-form-item label="解除封禁时间:">
                      <span v-show="detailsList.relieveBannedTime">{{
                          getYMDHMS("-", ":", detailsList.relieveBannedTime)
                        }}</span>
                    </el-form-item>
                    <el-form-item label="禁言解除时间:">
                      <span v-show="detailsList.relieveMuteTime">{{
                          getYMDHMS("-", ":", detailsList.relieveMuteTime)
                        }}</span>
                    </el-form-item>
                    <el-form-item
                        label="访问IP："
                        prop="registerIp"
                    >
                    <span v-show="detailsList.registerIp">{{
                        detailsList.registerIp
                      }}</span></el-form-item>
                    <el-form-item label="城市：">
                      <span>{{ detailsList.city }}</span>
                    </el-form-item>
                    <el-form-item
                        label="是否不显示城市："
                    >
                      <span>{{ detailsList.notHometown ? '是' : '否' }}</span>
                    </el-form-item>
                    <el-form-item
                        label="被禁换头像解除时间："
                    >
                      <span>{{ detailsList.forbidChangeAvatarReleaseTime }}</span>
                    </el-form-item>
                    <el-form-item label="操作描述：">
                      <span>{{ detailsList.operatorDesc }}</span>
                    </el-form-item>
                  </div>
                </div>
                <div class="details-box">
                  <p class="details-title">认证状态</p>
                  <div>
                    <el-form-item label="真人状态:">
                      <span>{{
                          detailsList.realAvatarAuthState == 0
                              ? "未提交"
                              : detailsList.realAvatarAuthState == 1
                                  ? "认证审核中"
                                  : detailsList.realAvatarAuthState == 2
                                      ? "认证成功"
                                      : "认证失败"
                        }}</span>
                    </el-form-item>
                    <el-form-item label="实名状态:">
                      <span>{{
                          detailsList.realNameAuthState == 0
                              ? "未提交"
                              : detailsList.realNameAuthState == 1
                                  ? "认证审核中"
                                  : detailsList.realNameAuthState == 2
                                      ? "认证成功"
                                      : "认证失败"
                        }}</span>
                    </el-form-item>
                    <el-form-item label="视频认证:">
                      <span>{{
                          detailsList.videoState == 0
                              ? "未提交认证"
                              : detailsList.videoState == 1
                                  ? "认证审核中"
                                  : detailsList.videoState == 2
                                      ? "认证成功"
                                      : detailsList.videoState == 3
                                          ? "认证失败"
                                          : "未提交认证"
                        }}</span>
                    </el-form-item>
                  </div>
                </div>
                <div class="details-box">
                  <p class="details-title">钱包</p>
                  <div>
                    <el-form-item label="总充值金额:">
                      <span>{{ detailsList.rechargeMoney }}</span>
                    </el-form-item>
                    <el-form-item label="总提现金额:">
                      <span>{{ detailsList.withdrawalMoney }}元</span>
                    </el-form-item>
                    <el-form-item label="当前余额积分:">
                      <span>{{ detailsList.balance }}</span>
                    </el-form-item>
                    <el-form-item label="充值金币:">
                      <span>{{ detailsList.goldsAmount }}</span>
                    </el-form-item>
                    <el-form-item label="免费金币:">
                      <span>{{ detailsList.goldsFreeAmount }}</span>
                    </el-form-item>
                    <el-form-item label="总金币:">
                      <span>{{ detailsList.totalAmount }}</span>
                    </el-form-item>
                  </div>
                </div>
                <div class="details-box">
                  <p class="details-title">收益</p>
                  <div>
                    <el-form-item label="当前收益比例:">
                      <span>{{ detailsList.incomeRatio }}</span>
                    </el-form-item>
                    <el-form-item label="历史总收益:">
                      <span>{{ detailsList.totalIncome }}(积分)</span>
                    </el-form-item>
                    <el-form-item label="昨日总收益:">
                      <span>{{ detailsList.yesterdayIncome }}(积分)</span>
                    </el-form-item>
                    <el-form-item label="今日总收入:">
                      <span>{{ detailsList.todayIncome }}(积分)</span>
                    </el-form-item>
                    <el-form-item label="礼物收益(今日):">
                      <span>{{ detailsList.giftIncome }}</span>
                    </el-form-item>
                    <el-form-item label="聊天收益(今日):">
                      <span>{{ detailsList.chatIncome }}</span>
                    </el-form-item>
                    <el-form-item label="视频收益(今日):">
                      <span>{{ detailsList.videoIncome }}</span>
                    </el-form-item>
                    <el-form-item label="语音收益(今日):">
                      <span>{{ detailsList.voiceIncome }}</span>
                    </el-form-item>
                    <el-form-item label="邀请收益(今日):">
                      <span>{{ detailsList.inviteIncome }}</span>
                    </el-form-item>
                    <el-form-item label="其他收益(今日):">
                      <span>{{ detailsList.otherIncome }}</span>
                    </el-form-item>
                  </div>
                </div>
              </div>
            </el-scrollbar>
            <el-scrollbar>
              <div class="details-ri">
                <div class="details-box">
                  <p class="details-title">设备相关</p>
                  <div>
                    <el-form-item label="操作系统:">
                      <span>{{ detailsList.system }}</span>
                    </el-form-item>
                    <el-form-item label="设备Id:">
                      <span>{{ detailsList.androidId }}</span>
                    </el-form-item>
                    <el-form-item label="手机型号:">
                      <span>{{ detailsList.mobileModel }}</span>
                    </el-form-item>
                    <el-form-item label="oaid:">
                      <span>{{ detailsList.oaid }}</span>
                    </el-form-item>
                    <el-form-item label="imei:">
                      <span>{{ detailsList.imei }}</span>
                    </el-form-item>
                    <el-form-item label="umId:">
                      <span>{{ detailsList.umId }}</span>
                    </el-form-item>
                    <el-form-item label="客户端版本号:">
                      <span>{{ detailsList.versionName }}</span>
                    </el-form-item>
                    <el-form-item label="版本号数字类型:">
                      <span>{{ detailsList.versionCode }}</span>
                    </el-form-item>
                    <el-form-item label="渠道号:">
                      <span>{{ detailsList.channel }}</span>
                    </el-form-item>
                    <el-form-item label="客户端传的渠道号:">
                      <span>{{ detailsList.clientChannel }}</span>
                    </el-form-item>
                    <el-form-item label="个性化设置:">
                      <span>{{ detailsList.disablePersonalization }}</span>
                    </el-form-item>
                    <el-form-item label="包名:">
                      <span>{{ detailsList.applicationId }}</span>
                    </el-form-item>
                    <el-form-item label="城市/所在地:">
                      <span>{{ detailsList.city }}</span>
                    </el-form-item>
                    <el-form-item label="用户请求IP:">
                      <span>{{ detailsList.ip }}</span>
                    </el-form-item>
                    <el-form-item label="数盟ID:">
                      <span>{{ detailsList.smId }}</span>
                    </el-form-item>
                  </div>
                </div>
                <div class="details-box">
                  <p class="details-title">音频视频</p>
                  <div>
                    <el-form-item label="土豪值:">
                      <span>{{ detailsList.wealthVal }}</span>
                    </el-form-item>
                    <el-form-item label="魅力值:">
                      <span>{{ detailsList.charmVal }}</span>
                    </el-form-item>
                    <el-form-item label="文本金币/条:">
                      <span>{{ detailsList.msgDeductionGolds }}</span>
                    </el-form-item>
                    <el-form-item label="是否开启语音通话:">
                      <span>{{ detailsList.enableVoice }}</span>
                    </el-form-item>
                    <el-form-item label="语音通话金币/分钟:">
                      <span>{{ detailsList.voiceDeductionGolds }}</span>
                    </el-form-item>
                    <el-form-item label="是否开启视频通话:">
                      <span>{{ detailsList.enableVideo }}</span>
                    </el-form-item>
                    <el-form-item label="视频通话金币/分钟:">
                      <span>{{ detailsList.videoDeductionGolds }}</span>
                    </el-form-item>
                    <el-form-item label="语音通话状态:">
                      <span>{{ detailsList.rtcStatus == 0 ? "未在通话" : "" }}</span>
                    </el-form-item>
                    <el-form-item label="当前所在房间:">
                      <span>{{ detailsList.roomId }}</span>
                    </el-form-item>
                    <el-form-item label="发布动态:">
                      <span>{{ detailsList.taskIssueDynamic }}</span>
                    </el-form-item>
                    <el-form-item label="发布评论:">
                      <span>{{ detailsList.taskIssueComment }}</span>
                    </el-form-item>
                    <el-form-item label="是否语音通话:">
                      <span>{{ detailsList.taskHasVoiceChat }}</span>
                    </el-form-item>
                    <el-form-item label="是否视频聊天:">
                      <span>{{ detailsList.taskHasVideoChat }}</span>
                    </el-form-item>
                    <el-form-item label="是否关注用户:">
                      <span>{{ detailsList.taskHasFollowUser }}</span>
                    </el-form-item>
                  </div>
                </div>
                <!-- <div class="details-box">
                  <p class="details-title">自动搭讪</p>
                  <div class="auto-accost">
                    <el-form-item label="搭讪女用户类型:">
                      <span>{{ detailsList.autoChatupWomanType }}</span>
                    </el-form-item>
                    <el-form-item label="搭讪女用户类型-计算时间:">
                      <span>{{ detailsList.autoChatupWomanTypeCalTime }}</span>
                    </el-form-item>
                    <el-form-item label="搭讪男用户类型:">
                      <span>{{ detailsList.autoChatupManType }}</span>
                    </el-form-item>
                    <el-form-item label="搭讪男用户类型-计算时间:">
                      <span>{{ detailsList.autoChatupManTypeCalTime }}</span>
                    </el-form-item>
                  </div>
                </div> -->
                <div class="details-box">
                  <p class="details-title">资料相关</p>
                  <div class="profile">
                    <el-form-item label="是否完善过真人头像:">
                      <span>{{ detailsList.taskRealAvatar }}</span>
                    </el-form-item>
                    <el-form-item label="是否完善过基本资料:">
                      <span>{{ detailsList.taskBasicProfile }}</span>
                    </el-form-item>
                    <el-form-item label="是否完善过更多资料:">
                      <span>{{ detailsList.taskMoreProfile }}</span>
                    </el-form-item>
                    <el-form-item label="是否完善过自我介绍:">
                      <span>{{ detailsList.taskSelfIntroduction }}</span>
                    </el-form-item>
                  </div>
                </div>
                <div class="details-box">
                  <p class="details-title">封禁设备相关</p>
                  <div class="profile">
                    <el-form-item label="封禁Ip:">
                      <span>{{ detailsList.ipList }}</span>
                    </el-form-item>
                    <el-form-item label="imei:">
                      <span>{{ detailsList.imeiList }}</span>
                    </el-form-item>
                    <el-form-item label="oaId:">
                      <span>{{ detailsList.oaidList }}</span>
                    </el-form-item>
                    <el-form-item label="安卓ID:">
                      <span>{{ detailsList.androidIdList }}</span>
                    </el-form-item>
                    <el-form-item label="友盟ID:">
                      <span>{{ detailsList.umIdList }}</span>
                    </el-form-item>
                    <el-form-item label="数盟ID:">
                      <span>{{ detailsList.smIdList }}</span>
                    </el-form-item>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </el-form>
        </el-dialog>
      </div>
      <!-- 临时封禁 -->
      <div class="banned_dialog">
        <el-dialog v-model="banned" title="封禁" width="35%">
          <el-form
              ref="bannedFromRef"
              :model="bannedFrom"
              class="banned-from"
              label-width="80px"
              :rules="rules"
          >
            <el-form-item label="昵称" prop="nickname">
              <span>{{ bannedFrom.nickname }}</span>
            </el-form-item>
            <el-form-item label="封禁时间" prop="BannedTime">
              <el-select v-model="bannedFrom.BannedTime" placeholder="请选择时间">
                <el-option label="1天" value="1天"/>
                <el-option label="3天" value="3天"/>
                <el-option label="7天" value="7天"/>
                <el-option label="30天" value="30天"/>
                <el-option label="永久" value="永久"/>
              </el-select>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="bannedFrom.editValue" placeholder="选择操作原因">
                <el-option label="低俗色情" value="低俗色情"/>
                <el-option label="政治" value="政治"/>
                <el-option label="广告" value="广告"/>
                <el-option label="虚假欺骗" value="虚假欺骗"/>
                <el-option label="侵权" value="侵权"/>
                <el-option label="违禁内容" value="违禁内容"/>
                <el-option label="辱骂" value="辱骂"/>
                <el-option label="其他" value="其他"/>
              </el-select>
            </el-form-item>
            <el-form-item label="原因" v-if="bannedFrom.editValue == '其他'">
              <el-input v-model="bannedFrom.editValueIpt"/>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="banned = false">取消</el-button>
              <el-button type="primary" @click="bannedSubmit(bannedFromRef)"
              >确定</el-button
              >
            </span>
          </template>
        </el-dialog>
      </div>
      <!-- 绑定邀请人 -->
      <div class="bind_dialog">
        <el-dialog v-model="bindShow" title="绑定邀请人" width="35%">
          <p>邀请用户ID或邀请码任选其一填写</p>
          <el-form
              :model="bindFrom"
              class="banned-from"
              label-width="100px"
              :rules="rules"
          >
            <el-form-item label="邀请用户ID" prop="bindUserId">
              <el-input v-model="bindFrom.bindUserId"/>
            </el-form-item>
            <el-form-item label="邀请码" prop="inviteCode">
              <el-input v-model="bindFrom.inviteCode"/>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="bindShow = false">取消</el-button>
              <el-button type="primary" @click="bindSubmit">确定</el-button>
            </span>
          </template>
        </el-dialog>
      </div>
      <!-- 禁言 -->
      <div class="metu_dialog">
        <el-dialog v-model="muteShow" title="禁言" width="35%">
          <el-form
              :model="muteFrom"
              class="banned-from"
              label-width="100px"
              :rules="rules"
          >
            <el-form-item label="昵称" prop="nickname">
              <span>{{ muteFrom.nickname }}</span>
            </el-form-item>
            <el-form-item label="禁言状态" prop="muteStatus">
              <el-select
                  v-model="muteFrom.muteStatus"
                  placeholder="请选择禁言状态"
                  @change="chMuteStatus"
              >
                <el-option label="临时" :value="2"/>
                <el-option label="永久" :value="3"/>
              </el-select>
            </el-form-item>
            <el-form-item label="禁言时间" prop="MuteTime">
              <el-select
                  v-model="muteFrom.MuteTime"
                  placeholder="禁言状态为永久不用选择"
                  :disabled="disabledMute"
              >
                <el-option label="15分钟" value="15分钟"/>
                <el-option label="30分钟" value="30分钟"/>
                <el-option label="1小时" value="1小时"/>
                <el-option label="3小时" value="3小时"/>
                <el-option label="6小时" value="6小时"/>
                <el-option label="1天" value="1天"/>
                <el-option label="3天" value="3天"/>
              </el-select>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="muteFrom.editValue" placeholder="选择操作原因">
                <el-option label="低俗色情" value="低俗色情"/>
                <el-option label="政治" value="政治"/>
                <el-option label="广告" value="广告"/>
                <el-option label="虚假欺骗" value="虚假欺骗"/>
                <el-option label="侵权" value="侵权"/>
                <el-option label="违禁内容" value="违禁内容"/>
                <el-option label="辱骂" value="辱骂"/>
                <el-option label="其他" value="其他"/>
              </el-select>
            </el-form-item>
            <el-form-item label="原因" v-if="muteFrom.editValue == '其他'">
              <el-input v-model="muteFrom.editValueIpt"/>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="muteShow = false">取消</el-button>
              <el-button type="primary" @click="muteSubmit">确定</el-button>
            </span>
          </template>
        </el-dialog>
      </div>
      <!-- 警告 -->
      <div class="warn_dialog">
        <el-dialog v-model="warnShow" title="警告" width="35%">
          <el-form
              :model="warnFrom"
              class="banned-from"
              label-width="100px"
              :rules="rules"
          >
            <el-form-item label="昵称" prop="nickname">
              <span>{{ warnFrom.nickname }}</span>
            </el-form-item>
            <el-form-item label="类型">
              <el-select v-model="warnFrom.editValue" placeholder="选择操作原因">
                <el-option label="低俗色情" value="低俗色情"/>
                <el-option label="政治" value="政治"/>
                <el-option label="广告" value="广告"/>
                <el-option label="虚假欺骗" value="虚假欺骗"/>
                <el-option label="侵权" value="侵权"/>
                <el-option label="违禁内容" value="违禁内容"/>
                <el-option label="辱骂" value="辱骂"/>
                <el-option label="其他" value="其他"/>
              </el-select>
            </el-form-item>
            <el-form-item label="原因" v-if="warnFrom.editValue == '其他'">
              <el-input v-model="warnFrom.editValueIpt"/>
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="warnShow = false">取消</el-button>
              <el-button type="primary" @click="warnSubmit">确定</el-button>
            </span>
          </template>
        </el-dialog>
      </div>
      <el-dialog v-model="nameShow" title="修改昵称" width="35%">
        <el-form :model="nameFrom" class="banned-from" label-width="100px" :rules="rules">
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="nameFrom.userName"></el-input>
          </el-form-item>
          <el-form-item label="原因" prop="desc">
            <el-input type="textarea" v-model="nameFrom.desc"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="nameShow = false">取消</el-button>
            <el-button type="primary" @click="nameSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog v-model="avatarShow" title="重置头像" width="35%">
        <el-form
            :model="avatarFrom"
            class="banned-from"
            label-width="100px"
            :rules="rules"
        >
          <el-form-item label="禁止时长" prop="forbidDuration">
            <el-select v-model="avatarFrom.forbidDuration">
              <el-option label="不限制" :value="-1"/>
              <el-option label="1小时" :value="60"/>
              <el-option label="一天" :value="1440"/>
              <el-option label="三天" :value="4320"/>
              <el-option label="七天" :value="10080"/>
              <el-option label="一个月" :value="43200"/>
              <el-option label="永久" :value="525600"/>
            </el-select>
          </el-form-item>
          <el-form-item label="原因" prop="desc">
            <el-input type="textarea" v-model="avatarFrom.desc"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="avatarShow = false">取消</el-button>
            <el-button type="primary" @click="avatarSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog
          v-model="freezeShow"
          :title="freeze.type === 1 ? '冻结提现 (默认永久冻结)' : '解除冻结提现'"
          width="35%"
      >
        <el-form :model="freeze" class="banned-from" label-width="100px" :rules="rules">
          <el-form-item v-if="freeze.type === 1" label="冻结时长" prop="freezeDay">
            <el-radio-group v-model="freeze.freezeDay">
              <el-radio :label="1">1天</el-radio>
              <el-radio :label="3">3天</el-radio>
              <el-radio :label="5">5天</el-radio>
              <el-radio :label="7">7天</el-radio>
              <!--              <el-radio :label="null">永久</el-radio>-->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="原因" prop="desc">
            <el-input
                placeholder="请输入告知用户的处罚原因"
                type="textarea"
                v-model="freeze.desc"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
    <span class="dialog-footer">
      <el-button @click="freezeShow = false">取消</el-button>
      <el-button type="primary" @click="freezeSubmit">确定</el-button>
    </span>
        </template>
      </el-dialog>
      <el-dialog v-model="ratioShow" title="修改收益比例" width="35%">
        <el-form
            :model="ratioFrom"
            class="banned-from"
            label-width="100px"
            :rules="rules"
        >
          <el-form-item label="比例" prop="ratio">
            <el-select v-model="ratioFrom.ratio">
              <el-option label="45%" :value="0.45"/>
              <el-option label="40%" :value="0.4"/>
              <el-option label="35%" :value="0.35"/>
              <el-option label="30%" :value="0.3"/>
              <el-option label="20%" :value="0.2"/>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="ratioShow = false">取消</el-button>
            <el-button type="primary" @click="ratioSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog
          v-model="mediaShow"
          title="限制用户音视频"
          width="35%"
      >
        <el-form :model="mediaFrom" class="banned-from" label-width="100px" :rules="rules">
          <el-form-item label="限制时间" prop="limitHours">
            <el-select v-model="mediaFrom.limitHours">
              <el-option label="1小时" :value="1"/>
              <el-option label="3小时" :value="3"/>
              <el-option label="6小时" :value="6"/>
              <el-option label="12小时" :value="12"/>
              <el-option label="24小时" :value="24"/>
              <el-option label="三天" :value="72"/>
              <el-option label="七天" :value="168"/>
              <el-option label="永久" :value="9999999"/>
            </el-select>
          </el-form-item>
          <el-form-item label="原因" prop="desc">
            <el-input
                placeholder="请输入告知用户的处罚原因"
                type="textarea"
                v-model="mediaFrom.desc"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="mediaShow = false">取消</el-button>
            <el-button type="primary" @click="mediaSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, toRefs, onBeforeUnmount, onBeforeMount} from "vue";
import userIdStore from "@/store/userIdStore";
import pageHook from "@/hooks/pageHook";
import {getYMDHMS, time} from "@/utils/date";
import {ElMessage} from "element-plus";
import type {FormInstance, FormRules} from "element-plus";
import {query_channel_list} from "@/api/datadetail";
import {
  queryUserPage_api,
  queryUser_api,
  income_ratio_api,
  modifyUser_api,
  inviteBind_api,
  muteUser_api,
  modifyAvatar_api,
  warnUser_api,
  ipUser_api,
  deviceUser_api,
  modifyName_api,
  freeze_withdrawal_api,
  limitVideo_api,
  secure_user_bind_api
} from "@/api/foregrounduser";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} = pageHook(
    querySubmit
);
let options = ref<any>();
let store = userIdStore();
let foreground = ref([]); //表格
let isShow = ref(false);
let detailsShow = ref(false);
let detailsList = ref();
let opNull = ref<any>(null);
let banned = ref(false);
// 当前时间
let toData = ref<any>(null);

let setToDate = setInterval(() => {
  toData.value = new Date(new Date()).getTime();
}, 1000);

const unbindUser = async (userId: string) => {
  if (window.confirm("是否进行解绑操作?")) {
    try {
      const response = await secure_user_bind_api({userId});
      if (response.data.code === 200) {
        ElMessage.success('解绑成功');
        querySubmit();  // 重新获取数据
      } else {
        ElMessage.error(response.data.msg || '解绑失败');
      }
    } catch (error) {
      console.error(error);
      ElMessage.error('解绑失败');
    }
  }
};
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
let option = ref([
  {
    state: "未提交",
  },
  {
    state: "认证审核中",
  },
  {
    state: "认证成功",
  },
  {
    state: "认证失败",
  },
]);
let videoOption = ref([
  {
    state: "未提交认证",
  },
  {
    state: "认证审核中",
  },
  {
    state: "认证成功",
  },
  {
    state: "认证失败",
  },
]);
const queryFormRef = ref<FormInstance>();
let date = ref();
let queryForm = reactive<any>({
  userId: null,
  mobile: null,
  gender: null,
  status: null,
  realAvatarAuthState: null,
  realNameAuthState: null,
  videoState: null,
  inviteCode: null,
  startTime: null,
  endTime: null,
  applicationId: '',
  data: []
});
let avatarList = ref<any>([]);
let bannedFrom = reactive<any>({
  nickname: null,
  BannedTime: null,
  editValue: null,
  editValueIpt: null,
});
let bannedId = ref();
let bannedStatus = ref();
const bannedFromRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  BannedTime: [{required: true, message: "不能为空", trigger: "change"}],
});
let nameShow = ref(false);
let bindShow = ref(false);
let bindFrom = reactive<any>({
  userId: null,
  bindUserId: null,
  inviteCode: null,
});
let muteShow = ref(false);
let muteFrom = reactive<any>({
  userId: null,
  nickname: null,
  muteStatus: null,
  MuteTime: null,
  editValue: null,
  editValueIpt: null,
});
let nameFrom = reactive<any>({
  userId: null,
  userName: null,
  desc: null,
});
let avatarFrom = reactive<any>({
  userId: null,
  forbidDuration: null,
  desc: null,
});
let avatarShow = ref(false);
let warnShow = ref(false);
let warnFrom = reactive<any>({
  userId: null,
  nickname: null,
  editValue: null,
  editValueIpt: null,
});
let freezeShow = ref(false);
let disabledMute = ref(false);
let mediaFrom = ref<any>({
  userId: null,
  desc: null,
})
let mediaShow = ref(false);

let freeze = reactive<any>({});
let ratioFrom = reactive<any>({
  userId: null,
  ratio: null,
});
let ratioShow = ref(false);

async function getforeground() {
  let res = await queryUserPage_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId: queryForm.applicationId,
  });

  let {data, total} = res.data;
  foreground.value = data;
  totalNum.value = total;
  data.forEach((v: any) => {
    if (v.realAvatarAuthState == 0) {
      v.realAvatarAuthState = "未提交";
    } else if (v.realAvatarAuthState == 1) {
      v.realAvatarAuthState = "认证审核中";
    } else if (v.realAvatarAuthState == 2) {
      v.realAvatarAuthState = "认证成功";
    } else {
      v.realAvatarAuthState = "认证失败";
    }
    if (v.realNameAuthState == 0) {
      v.realNameAuthState = "未提交";
    } else if (v.realNameAuthState == 1) {
      v.realNameAuthState = "认证审核中";
    } else if (v.realNameAuthState == 2) {
      v.realNameAuthState = "认证成功";
    } else {
      v.realNameAuthState = "认证失败";
    }
    if (v.videoState == 0) {
      v.videoState = "未提交认证";
    } else if (v.videoState == 1) {
      v.videoState = "认证审核中";
    } else if (v.videoState == 2) {
      v.videoState = "认证成功";
    } else if (v.videoState == 3) {
      v.videoState = "认证失败";
    } else {
      v.videoState = "未提交认证";
    }
    avatarList.value.push(v.avatar);
    v.statusPunish = "";
  });
}


onBeforeMount(() => {
  if (store.userId) {
    queryForm.userId = store.userId;
    querySubmit();
  } else {
    getforeground();
  }


})

//查询筛选列表
async function query_list() {
  let res = await query_channel_list();
  let {data} = res.data;
  options.value = data;
}

query_list();
const resAvatar = (row: any) => {
  avatarList.value = [];
  if (avatarList.value.length == 0) {
    avatarList.value.push(row.avatar);
  }
};

const ediRatio = (row: any) => {
  ratioShow.value = true;
  ratioFrom.userId = row.userId;
  ratioFrom.ratio = row.ratio;
};

// 编辑
const editSelect = (row: any) => {
  let data = row;
  if (row.statusPunish == "修改收益比例") {
    ediRatio(data);
  }
  if (row.statusPunish == "重置头像") {
    editavatar(data);
  }
  if (row.statusPunish == "修改昵称") {
    editname(data);
  }
  if (row.statusPunish == "警告") {
    editWarn(data);
  }
  if (row.statusPunish == "禁言") {
    editMute(data);
  }
  if (row.statusPunish == "封禁账号") {
    editbanned(data);
  }
  if (row.statusPunish == "封禁IP") {
    editIP(data);
  }
  if (row.statusPunish == "冻结提现") {
    handlefreeze(data, 1);
  }
  if (row.statusPunish == "封禁设备") {
    editDevice(data);
  }
  if (row.statusPunish == "解除封禁") {
    unseal(data, 0);
  }
  if (row.statusPunish == "解除禁言") {
    unMute(data, 1);
  }
  if (row.statusPunish == "解除冻结提现") {
    handlefreeze(data, 0);
  }
  if (row.statusPunish == "限制用户音视频") {
    handleMedia(data, 0);
  }
  if (row.statusPunish == "解除限制用户音视频") {
    handleMedia(data, 1);
  }
};


const handleMedia = (data: any, type: number) => {
  mediaFrom.value.userId = data.userId;
  if (!type) {
    mediaShow.value = true;
  } else {
    limitVideo_api({...mediaFrom.value, type: 1})
  }
}

const handlefreeze = (data: any, val: number) => {
  freeze.userId = data.userId;
  freeze.type = val;
  freezeShow.value = true;
};

const editname = (row: any) => {
  nameFrom.userId = row.userId;
  nameFrom.userName = row.nickname;
  nameShow.value = true;
};
const editavatar = (row: any) => {
  avatarFrom.userId = row.userId;
  avatarShow.value = true;
};
//警告
const editWarn = (row: any) => {
  warnFrom.id = row.userId;
  warnFrom.nickname = row.nickname;
  warnFrom.editValueIpt = null;
  warnFrom.editValue = null;
  warnShow.value = true;
};
const freezeSubmit = () => {
  freezeShow.value = false;
  freeze_withdrawal_api({...freeze}).then(() => {
    querySubmit();
  });
};

const nameSubmit = () => {
  nameShow.value = false;
  modifyName_api({...nameFrom}).then(() => {
    querySubmit();
  });
};
const avatarSubmit = () => {
  avatarShow.value = false;
  modifyAvatar_api({...avatarFrom}).then(() => {
    querySubmit();
  });
};
const ratioSubmit = () => {
  ratioShow.value = false;
  income_ratio_api({...ratioFrom}).then(() => {
    querySubmit();
  });
};
const warnSubmit = async () => {
  if (warnFrom.editValue == "其他") {
    warnFrom.editValue = warnFrom.editValueIpt;
  }
  await warnUser_api({
    operatorDesc: warnFrom.editValue,
    userId: warnFrom.id,
  });
  querySubmit();
  warnShow.value = false;
};

//限制用户音视频

const mediaSubmit = () => {

  limitVideo_api({...mediaFrom.value, type: 0}).then((_res: any) => {
    mediaShow.value = false;
  })


}

//封禁
const editbanned = (row: any) => {
  bannedFrom.relieveBannedTime = null;
  bannedFrom.editValueIpt = null;
  bannedFrom.editValue = null;
  bannedFrom.nickname = row.nickname;
  bannedId.value = row.userId;
  banned.value = true;
};
const bannedSubmit = (bannedFromRef: FormInstance | undefined) => {
  if (!bannedFromRef) return;
  let relieveBannedTime = ref(null);
  if (bannedFrom.BannedTime == "永久") {
    bannedStatus.value = 2;
  } else {
    bannedStatus.value = 3;
  }
  if (bannedFrom.BannedTime == "1天") {
    relieveBannedTime.value = toData.value + 1 * 3600 * 24 * 1000;
  } else if (bannedFrom.BannedTime == "3天") {
    relieveBannedTime.value = toData.value + 3 * 3600 * 24 * 1000;
  } else if (bannedFrom.BannedTime == "7天") {
    relieveBannedTime.value = toData.value + 7 * 3600 * 24 * 1000;
  } else if (bannedFrom.BannedTime == "30天") {
    relieveBannedTime.value = toData.value + 30 * 3600 * 24 * 1000;
  } else {
    relieveBannedTime.value = null;
  }
  console.log(relieveBannedTime.value, bannedStatus.value);

  bannedFromRef.validate(async (valid: any) => {
    if (bannedFrom.editValue == "其他") {
      bannedFrom.editValue = bannedFrom.editValueIpt;
    }
    if (valid) {
      await modifyUser_api({
        id: bannedId.value,
        relieveBannedTime: relieveBannedTime.value,
        status: bannedStatus.value,
        operatorDesc: bannedFrom.editValue,
      });
      banned.value = false;
      querySubmit();
    } else {
      return false;
    }
  });
};

// 解封
const unseal = async (row: any, status: any) => {
  await modifyUser_api({id: row.userId, status});
  isShow.value = false;
  querySubmit();
};

//绑定邀请人
const editBind = (row: any) => {
  bindFrom.bindUserId = null;
  bindFrom.inviteCode = null;
  bindFrom.userId = row.userId;
  bindShow.value = true;
};
const bindSubmit = async () => {
  if (bindFrom.bindUserId != null || bindFrom.inviteCode != null) {
    await inviteBind_api({...bindFrom, applicationId: queryForm.applicationId});
    bindShow.value = false;
    querySubmit();
  } else {
    ElMessage({
      message: "请填写邀请码或邀请用户ID",
      type: "warning",
    });
  }
};

//禁言
const editMute = (row: any) => {
  muteFrom.editValueIpt = null;
  muteFrom.editValue = null;
  muteFrom.muteStatus = null;
  muteFrom.MuteTime = null;
  muteFrom.nickname = row.nickname;
  muteFrom.userId = row.userId;
  muteShow.value = true;
};
const muteSubmit = async () => {
  let {muteStatus, userId, MuteTime} = toRefs(muteFrom);
  let relieveMuteTime = ref(null);
  if (MuteTime.value == "15分钟") {
    relieveMuteTime.value = toData.value + 0.25 * 60 * 60 * 1000;
  } else if (MuteTime.value == "30分钟") {
    relieveMuteTime.value = toData.value + 0.5 * 60 * 60 * 1000;
  } else if (MuteTime.value == "1小时") {
    relieveMuteTime.value = toData.value + 1 * 60 * 60 * 1000;
  } else if (MuteTime.value == "3小时") {
    relieveMuteTime.value = toData.value + 3 * 60 * 60 * 1000;
  } else if (MuteTime.value == "6小时") {
    relieveMuteTime.value = toData.value + 6 * 60 * 60 * 1000;
  } else if (MuteTime.value == "1天") {
    relieveMuteTime.value = toData.value + 1 * 3600 * 24 * 1000;
  } else if (MuteTime.value == "3天") {
    relieveMuteTime.value = toData.value + 3 * 3600 * 24 * 1000;
  }
  if (muteFrom.muteStatus != null && muteFrom.relieveMuteTime != "") {
    if (muteFrom.editValue == "其他") {
      muteFrom.editValue = muteFrom.editValueIpt;
    }
    await muteUser_api({
      userId: userId.value,
      muteStatus: muteStatus.value,
      relieveMuteTime: relieveMuteTime.value,
      operatorDesc: muteFrom.editValue,
    });
    muteShow.value = false;
    querySubmit();
  } else {
    ElMessage({
      message: "请选择状态",
      type: "warning",
    });
  }
};
//解除禁言
const unMute = async (row: any, muteStatus: any) => {
  await await muteUser_api({userId: row.userId, muteStatus});
  querySubmit();
};
//禁言状态
const chMuteStatus = (val: any) => {
  if (val == 3) {
    disabledMute.value = true;
  } else {
    disabledMute.value = false;
  }
};

// 封禁 IP  设备
const editIP = async (row: any) => {
  await ipUser_api({userId: row.userId});
  querySubmit();
};
const editDevice = async (row: any) => {
  await deviceUser_api({userId: row.userId});
  querySubmit();
};
// 查看用户详情
const detailsUser = async (row: any) => {
  let res = await queryUser_api({id: row.userId});
  let {data} = res.data;
  if (data.autoChatupWomanTypeCalTime) {
    data.autoChatupWomanTypeCalTime = time(data.autoChatupWomanTypeCalTime);
  }
  if (data.autoChatupManTypeCalTime) {
    data.autoChatupManTypeCalTime = time(data.autoChatupManTypeCalTime);
  }
  detailsList.value = data;
  detailsShow.value = true;
};
// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function querySubmit() {
  let params = {};
  if (queryForm.data) {
    params = {
      oneLevel: queryForm.data[0],
      twoLevel: queryForm.data[1],
      threeLevel: queryForm.data[2],
    };
  }
  let res = await queryUserPage_api({
    ...queryForm, ...params,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let {data, total} = res.data;
  data.forEach((v: any) => {
    if (v.realAvatarAuthState == 0) {
      v.realAvatarAuthState = "未提交";
    } else if (v.realAvatarAuthState == 1) {
      v.realAvatarAuthState = "认证审核中";
    } else if (v.realAvatarAuthState == 2) {
      v.realAvatarAuthState = "认证成功";
    } else {
      v.realAvatarAuthState = "认证失败";
    }
    if (v.realNameAuthState == 0) {
      v.realNameAuthState = "未提交";
    } else if (v.realNameAuthState == 1) {
      v.realNameAuthState = "认证审核中";
    } else if (v.realNameAuthState == 2) {
      v.realNameAuthState = "认证成功";
    } else {
      v.realNameAuthState = "认证失败";
    }
    if (v.videoState == 0) {
      v.videoState = "未提交认证";
    } else if (v.videoState == 1) {
      v.videoState = "认证审核中";
    } else if (v.videoState == 2) {
      v.videoState = "认证成功";
    } else if (v.videoState == 3) {
      v.videoState = "认证失败";
    } else {
      v.videoState = "未提交认证";
    }
  });
  foreground.value = data;
  totalNum.value = total;
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  date.value = null;
  formEl.resetFields();
  getforeground();
};

function mouseenterSubcat(node: any) {
  let el_node = document.querySelectorAll(
      ".el-popper.el-cascader__dropdown.specialCascaderRadio .el-cascader-panel .el-cascader-menu"
  );
  if (el_node[node.level] && el_node.length > 0) {
    (el_node[node.level] as HTMLElement).style.display = "none";
    cascaderRecursion(el_node, node);
  }
}

function mouseenterLeaf(node: any) {
  let el_node = document.querySelectorAll(
      ".el-popper.el-cascader__dropdown.specialCascaderRadio .el-cascader-panel .el-cascader-menu"
  );
  if (el_node[node.level] && el_node.length > 0) {
    (el_node[node.level] as HTMLElement).style.display = "block";
  }
}

function cascaderRecursion(el_node: any, node: any) {
  function handle(i: any) {
    if (el_node[node.level + i]) {
      el_node[node.level + i].style.display = "none";
      i++;
      handle(i);
    } else {
      return;
    }
  }

  handle(1);
}

onBeforeUnmount(() => {
  clearInterval(setToDate);
});
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  querySubmit();
}
</script>

<style lang="scss" scoped>
.foregrounduser {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .statusPunish {
      width: 150px;
      margin-left: 16px;
    }

    .details_dialog {
      :deep(.el-dialog) {
        min-width: 1000px;
        max-width: none;
        max-height: 95vh;
        margin: 2vh auto;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        overflow: hidden;
      }

      :deep(.el-dialog__body) {
        max-height: 85vh;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 15px;
        box-sizing: border-box;
      }

      :deep(.el-overlay) {
        background-color: rgba(0, 0, 0, 0.5);
      }

      .detailsForm {
        display: grid;
        grid-template-columns: 40% 60%;
        gap: 20px;
        font-size: 13px;
        overflow-x: hidden;
        box-sizing: border-box;
        width: 100%;

        .details-lf,
        .details-ri {
          min-width: 0;
          width: 100%;
          overflow: hidden;
          box-sizing: border-box;
        }

        .details-box {
          width: 100%;
          margin-bottom: 10px;
          overflow: hidden;
          box-sizing: border-box;

          & > div {
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 0;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-color: #fafafa;
            overflow: hidden;
            box-sizing: border-box;
            width: 100%;
          }

          .auto-accost,
          .profile {
            & > div {
              display: flex;
              flex-direction: column;
            }
          }

          .details-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 6px;
            color: #333;
            border-left: 3px solid #409eff;
            padding-left: 8px;
            background-color: #f0f8ff;
            padding: 16px 0 6px 8px;
            margin: -10px -10px 6px -10px;
          }
        }

        .el-form-item {
          margin: 0;
          display: grid;
          grid-template-columns: 160px 1fr;
          align-items: center;
          height: 32px;
          padding: 4px 8px;
          border-bottom: 1px solid #f0f0f0;
          gap: 15px;
          overflow: hidden;
          box-sizing: border-box;
          width: 100%;

          &:last-child {
            border-bottom: none;
          }

          span {
            display: inline-block;
            line-height: 1.4;
            color: #666;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            box-sizing: border-box;
          }
        }

        :deep(.el-form-item__label) {
          justify-content: flex-start;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 14px;
          font-weight: 500;
          color: #333;
          line-height: 1.4;
          padding: 0;
          margin: 0;
          align-self: center;
          max-width: 100%;
          box-sizing: border-box;
        }

        :deep(.el-form-item__content) {
          line-height: 1.4;
          padding: 0;
          margin: 0;
          min-width: 0;
          max-width: 100%;
          align-self: center;
          overflow: hidden;
          box-sizing: border-box;
        }

        /* 头像项特殊处理 */
        .el-form-item:first-child {
          height: 60px;
          align-items: center;

          .el-avatar {
            width: 50px;
            height: 50px;
          }
        }

        /* 长标签特殊处理 */
        .el-form-item {
          &[data-long-label] {
            grid-template-columns: 180px 1fr;
            height: 40px;
            overflow: hidden;

            :deep(.el-form-item__label) {
              white-space: normal;
              line-height: 1.3;
              align-self: center;
              max-width: 100%;
              overflow: hidden;
              word-wrap: break-word;
            }

            :deep(.el-form-item__content) {
              overflow: hidden;
              max-width: 100%;
            }
          }
        }

        /* 数据过长的项目 */
        .el-form-item {
          span {
            &[title] {
              cursor: help;
            }
          }
        }
      }
    }

    .bind_dialog {
      p {
        text-align: center;
        margin-bottom: 10px;
      }
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
