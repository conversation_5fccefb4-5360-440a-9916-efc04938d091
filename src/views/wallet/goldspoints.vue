<template>
  <div class="goldspoints">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="排序" prop="hasPointsSort">
              <el-select class="dia-ipt" v-model="queryForm.hasPointsSort" placeholder="请选择排序方式">
                <el-option label="金币" :value="false" />
                <el-option label="积分" :value="true" />
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryGold">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
          <div><span>累计金币余额 : {{ goldsTotal }}</span> <span>累计积分余额: {{ pointsTotal }}</span> <span>累计金币余额（赠送所得）: {{ golds_freeTotal }}</span></div>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="goldspointsTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" min-width="100" fixed></el-table-column>
        <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
        <el-table-column prop="golds" label="金币余额" min-width="120"></el-table-column>
        <el-table-column prop="goldsFree" label="赠送金币余额" min-width="120"></el-table-column>
        <el-table-column prop="points" label="积分余额" min-width="120"></el-table-column>
        <el-table-column prop="goldsModifyTime" label="金币最新变动时间" min-width="240">
          <template #default="scope">
            <span v-show="scope.row.goldsModifyTime">{{ getYMDHMS("-", ":", scope.row.goldsModifyTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="goldsModifyTime" label="积分最新变动时间" min-width="240">
          <template #default="scope">
            <span v-show="scope.row.pointsModifyTime">{{ getYMDHMS("-", ":", scope.row.pointsModifyTime)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editGolds(scope.row)" size="small" type="primary" :icon="Edit">金币</el-button>
            <el-button @click="editPoints(scope.row)" size="small" type="primary" :icon="Edit">积分</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />

      <el-dialog v-model="goldsShow" title="增加或减少金币" class="add">
        <el-form ref="goldsFormRef" :model="addFormGolds" status-icon class="demo-ruleForm" label-width="100px"
          :rules="rules">
          <el-form-item label="金币" prop="golds">
            <el-input v-model="addFormGolds.golds" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="赠送金币" prop="goldsFree">
            <el-input v-model="addFormGolds.goldsFree" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="增加或减少" prop="hasAdd">
            <el-select class="dia-ipt" v-model="addFormGolds.hasAdd" placeholder="请选择" @change="chGolds">
              <el-option label="增加" :value="true" />
              <el-option label="减少" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item label="业务类型" prop="goldsBusinessType">
            <el-select class="dia-ipt" v-model="addFormGolds.goldsBusinessType" placeholder="请选择">
              <el-option v-for="v in goldsType" :label="v.value" :value="v.key" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="editGoldsSubmit(goldsFormRef)">确定</el-button>
            <el-button @click="goldsShow = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog v-model="pointsShow" title="增加或减少积分" class="add">
        <el-form ref="pointsFormRef" :model="addFormPoints" status-icon class="demo-ruleForm" label-width="100px"
          :rules="rules">
          <el-form-item label="积分" prop="points">
            <el-input v-model="addFormPoints.points" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="增加或减少" prop="hasAdd">
            <el-select class="dia-ipt" v-model="addFormPoints.hasAdd" placeholder="请选择" @change="chPoints">
              <el-option label="增加" :value="true" />
              <el-option label="减少" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item label="业务类型" prop="pointsBusinessType">
            <el-select class="dia-ipt" v-model="addFormPoints.pointsBusinessType" placeholder="请选择">
              <el-option v-for="v in pointsType" :label="v.value" :value="v.key" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="editPointsSubmit(pointsFormRef)">确定</el-button>
            <el-button @click="pointsShow = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-card>
  </div>
</template>
<script setup lang="ts">
import {ref, reactive, onMounted} from "vue";
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from "element-plus";
import { Edit } from '@element-plus/icons-vue'
import pageHook from "@/hooks/pageHook";
import { getYMDHMS } from "@/utils/date"
import {queryGoldsPoints_api, modifyUserWallet_api, queryBusinessType_api, queryjbbdtjTotal_api} from "@/api/wallet"

let goldsTotal = ref(0);
let pointsTotal = ref(0);
let golds_freeTotal = ref(0);


let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(queryGold);
let goldspointsTab = ref([]);

const queryFormRef = ref<FormInstance>()
const goldsFormRef = ref<FormInstance>()
const pointsFormRef = ref<FormInstance>()
let queryForm = reactive<any>({
  id: null,
  userId: null,
  hasPointsSort: false,
  applicationId:'com.dongxin.app'
})
let goldsShow = ref(false)
let pointsShow = ref(false)
let addGolds = ref<any>([])
let addPoints = ref<any>([])
let reducedGolds = ref<any>([])
let reducedPoints = ref<any>([])
let goldsType = ref(addGolds.value)
let pointsType = ref(addPoints.value)
// 增加金币
const addFormGolds = reactive<any>({
  id: null,
  golds: null,
  goldsFree: null,
  hasAdd: true,
  goldsBusinessType: null
});
// 增加积分
const addFormPoints = reactive<any>({
  id: null,
  points: null,
  hasAdd: true,
  pointsBusinessType: null
});
const rules = reactive<FormRules>({
  goldsBusinessType: [{ required: true, message: "不能为空", trigger: "change" }],
  pointsBusinessType: [{ required: true, message: "不能为空", trigger: "change" }],
});

async function queryGoldsPoints() {
  let res = await queryGoldsPoints_api({
    applicationId:queryForm.applicationId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let { data, total } = res.data
  goldspointsTab.value = data
  totalNum.value = total
}
queryGoldsPoints()


async function fetchTotalWithdrawal(queryData = {}) {
  const res = await queryjbbdtjTotal_api  (queryData);
  const data = res.data;
  console.log(data);  // 打印返回的数据
  if (data.code === 200 && data.success) {
    goldsTotal.value = data.data.goldsTotal;
    pointsTotal.value = data.data.pointsTotal;
    golds_freeTotal.value = data.data.golds_freeTotal;
    console.log(goldsTotal.value, pointsTotal.value, golds_freeTotal.value);  // 打印赋值后的变量
  }
}

onMounted(() => {
  fetchTotalWithdrawal();
});

// 编辑金币
const editGolds = (row: any) => {
  addFormGolds.golds = null;
  addFormGolds.goldsFree = null;
  addFormGolds.goldsBusinessType = null;

  addFormGolds.id = row.id
  goldsShow.value = true
}

const chGolds = (val: any) => {
  if (val) {
    goldsType.value = addGolds.value
  } else {
    goldsType.value = reducedGolds.value
  }

  addFormGolds.goldsBusinessType = null
}

const editGoldsSubmit = (goldsFormRef: FormInstance | undefined) => {
  if (!goldsFormRef) return
  goldsFormRef.validate(async (valid: any) => {
    if (valid) {
      if (addFormGolds.golds != null || addFormGolds.goldsFree != null) {
        await modifyUserWallet_api({ ...addFormGolds })
        queryGold()
        goldsShow.value = false
        goldsType.value = addGolds.value
        goldsFormRef.resetFields()
      } else {
        ElMessage({
          message: "请填写金币数量",
          type: "warning",
        });
      }
    } else {
      return false
    }
  })

}
// 编辑积分
const editPoints = (row: any) => {
  addFormPoints.points = null;
  addFormPoints.pointsBusinessType = null;

  addFormPoints.id = row.id
  pointsShow.value = true
}
const chPoints = (val: any) => {
  if (val) {
    pointsType.value = addPoints.value
  } else {
    pointsType.value = reducedPoints.value
  }
  addFormPoints.pointsBusinessType = null
}
const editPointsSubmit = (pointsFormRef: FormInstance | undefined) => {
  if (!pointsFormRef) return
  pointsFormRef.validate(async (valid: any) => {
    if (valid) {
      if (addFormPoints.points != null) {
        await modifyUserWallet_api({ ...addFormPoints })
        queryGold()
        pointsShow.value = false;
        pointsType.value = addPoints.value
        pointsFormRef.resetFields()
      } else {
        ElMessage({
          message: "请填写积分数量",
          type: "warning",
        });
      }

    } else {
      return false
    }
  })

}
// 查询
async function queryGold() {
  let res = await queryGoldsPoints_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let { data, total } = res.data
  goldspointsTab.value = data
  totalNum.value = total

      fetchTotalWithdrawal({
    ...queryForm,
  });
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  queryGoldsPoints()
}


// 积分金币业务类型查询
const queryBusinessType = async () => {
  let res = await queryBusinessType_api()
  let { data } = res.data

  data.goldsBusinessType.forEach((v: any) => {
    if (v.type == "add") {
      addGolds.value.push(v)
    } else {
      reducedGolds.value.push(v)
    }
  })

  data.pointsBusinessType.forEach((v: any) => {
    if (v.type == "add") {
      addPoints.value.push(v)
    } else {
      reducedPoints.value.push(v)
    }
  })
}
queryBusinessType()
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  queryGold();
}
</script>

<style lang="scss" scoped>
.goldspoints {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>