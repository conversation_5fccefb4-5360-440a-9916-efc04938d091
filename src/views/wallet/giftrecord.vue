<template>
  <div class="goldspoints">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="queryForm"
            class="demo-form-inline"
          >
          <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="赠送人后台帐号" prop="adminAccount">
              <el-input v-model="queryForm.adminAccount" />
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="赠送类型" prop="giveType">
              <el-select
                class="dia-ipt"
                v-model="queryForm.giveType"
                placeholder="请选择排序方式"
              >
                <el-option label="金币" :value="1" />
                <el-option label="积分" :value="0" />
                <el-option label="赠送金币" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="操作类型" prop="giveType">
              <el-select
                class="dia-ipt"
                v-model="queryForm.flowOperateType"
                placeholder="请选择排序方式"
              >
                <el-option label="增加" :value="1" />
                <el-option label="减少" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryGold">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button class="ml" type="primary" @click="handleExportExcel">
                <el-icon style="vertical-align: middle">
                  <Download />
                </el-icon>
                <span style="vertical-align: middle">导出Excel</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table
          :data="goldspointsTab"
          style="width: 100%; height: 100%"
          v-loading="listLoading"
        >
        <el-table-column
          prop="adminAccount"
          label="赠送人后台帐号"
          min-width="100"
          fixed
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="操作时间"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="userId"
          label="被赠送人用户id"
          min-width="120"
        ></el-table-column>
        <el-table-column prop="giveType" label="赠送类型" min-width="120">
          <template #default="scope">
            <span>{{
              scope.row.giveType === 1
                ? "积分"
                : scope.row.giveType === 2
                ? "金币"
                : scope.row.giveType === 3
                ? "赠送金币"
                : ""
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="changeType" label="操作类型" min-width="120">
          <template #default="scope">
            <span>{{
              scope.row.changeType === 0
                ? "减少"
                : scope.row.changeType === 1
                ? "增加"
                : ""
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          min-width="240"
        ></el-table-column>
      </el-table>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import pageHook from "@/hooks/pageHook";
import { queryWallet_api } from "@/api/wallet";
import { aoaToSheetXlsx } from "@/utils/excel";
import {flattenArray} from '@/utils/list';
import {concurrencyRequest} from '@/utils/concurrencyRequest'
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  queryGold
);
let goldspointsTab = ref([]);
let listLoading = ref(false);
const queryFormRef = ref<FormInstance>();
let queryForm = reactive<any>({
  adminAccount: null,
  userId: null,
  giveType: null,
  flowOperateType: null,
  applicationId:'com.dongxin.app'
});

async function queryGoldsPoints() {
  let res = await queryWallet_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId,
  });
  let { data, total } = res.data;
  goldspointsTab.value = data;
  totalNum.value = total;
}
queryGoldsPoints();

async function getKeys() {
  let queue:any = [];
      let pageNum=Math.ceil(totalNum.value/10000);
        for (let i=1; i<=pageNum; i++){
          let urls={
          data:{  ...queryForm,
            pageNum:i,
            pageSize:10000},
            url:'/admin/give_manage/golds_points_free/query_user_give_record' }
        
          queue.push(urls);
        }
  let res = await concurrencyRequest(queue, 2);
    return flattenArray(res);
	}


async function queryGold() {
  let res = await queryWallet_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  goldspointsTab.value = data;

  totalNum.value = total;
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  queryGoldsPoints();
};
const handleExportExcel = async () => {
  //获取后端数据
  listLoading.value = true;
  //标题数组
  let titleArr = [
    "赠送人后台帐号",
    "操作时间",
    "被赠送人用户id",
    "赠送类型",
    "操作类型",
    "描述",
  ];

  let header = titleArr;
  let res = await getKeys();
  let data =res.map((item: any, _index: any) => {
    switch (item.giveType) {
      case 1:
        item.giveType = "积分";
        break;
      case 2:
        item.giveType = "金币";
        break;
      case 3:
        item.giveType = "赠送金币";
        break;

      default:
        break;
    }
    if(item.changeType){
      item.changeType = "增加";
    }else{
      item.changeType = "减少";
    }
    let { adminAccount, createTime, userId, giveType, changeType, description } = item;
    return [adminAccount, createTime, userId, giveType, changeType, description];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `后台赠送记录.xlsx`,
  });
  listLoading.value = false;
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  queryGold();
}
</script>

<style lang="scss" scoped>
.goldspoints {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
