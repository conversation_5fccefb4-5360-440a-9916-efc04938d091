<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="邀请人" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="被绑定用户ID" prop="bindUserId">
              <el-input v-model="queryForm.bindUserId"/>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="listData" style="width: 100%; height: 100%">
          <el-table-column prop="userId" label="邀请人ID" min-width="60"></el-table-column>
          <el-table-column prop="bindUserId" label="被绑定用户ID" min-width="60"></el-table-column>
          <el-table-column prop="imgs" label="图片证明" width="540">
            <template #default="scope">
              <div style="display: flex; flex-wrap: wrap;">
                <el-image
                    v-for="(item,index) in scope.row.imgs"
                    :key="index"
                    style="height: 90px; margin-right:10px; margin-bottom:20px;"
                    :src="item"
                    :preview-src-list="idcaBa"
                    :initial-index="0"
                    fit="cover"
                    :preview-teleported="true"
                    :hide-on-click-modal="true"
                    @click="resIdcaBa(item)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="applyStatus" label="申请状态" min-width="60">
            <template #default="scope">
              <span :class="{
                'status-success': scope.row.applyStatus === 2,
                'status-rejected': scope.row.applyStatus === 3
              }">
                {{
                  scope.row.applyStatus === 1 ? "申请中" :
                  scope.row.applyStatus === 2 ? "绑定成功" :
                  scope.row.applyStatus === 3 ? "拒绝绑定" : ""
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="applyTime" label="申请时间" min-width="70"></el-table-column>
          <el-table-column prop="gmtModified" label="操作时间" min-width="70"></el-table-column>
          <el-table-column prop="operateAccount" label="操作人" min-width="50"></el-table-column>
          <el-table-column fixed="right" label="操作" width="150">
            <template #default="scope">
              <el-button @click="handleBind(scope.row,true)" type="success" v-if="scope.row.applyStatus!==2">通过绑定
              </el-button><br>
              <el-button @click="handleBind(scope.row,false)" type="danger" v-if="scope.row.applyStatus===1">拒绝绑定
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20, 50]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import {bind_applying_api, handle_bind_applying_api} from "@/api/wallet"
import {FormInstance} from 'element-plus'
import {getYMDHMS} from '@/utils/date'
import pageHook from "@/hooks/pageHook";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} = pageHook(query);
let listData = ref([])
const queryFormRef = ref<FormInstance>()
let idcaBa = ref<any>([])
let queryForm = reactive({
  userId: null,
  bindUserId: null,
  applicationId: 'com.dongxin.app'
});

//查询
async function query() {
  let res = await bind_applying_api({
    ...queryForm, size: pageSize.value,
    page: currentPage.value
  });
  let {data, total} = res.data
  data.forEach((item: any) => {
    item.applyTime = getYMDHMS('-', ':', item.applyTime);
    if (item.gmtModified) {
      item.gmtModified = getYMDHMS('-', ':', item.gmtModified);
    }
    item.imgs = item.imgs.split(',')
  })
  totalNum.value = total
  listData.value = data
}

query();
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  queryForm.userId = null;
  queryForm.bindUserId = null;
  query();
};

const handleBind = (row: { id: any; }, type: any) => {
  handle_bind_applying_api({id: row.id, pass: type}).then(_res => {
    query();
  })
}

const resIdcaBa = (e: any) => {
  idcaBa.value = []
  if (idcaBa.value.length == 0) {
    idcaBa.value.push(e)
  }
}

</script>

<style lang="scss" scoped>
.status-success {
  color: green;
}

.status-rejected {
  color: red;
}

.illegallist {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>