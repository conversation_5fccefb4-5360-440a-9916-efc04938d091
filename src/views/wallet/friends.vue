<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="邀请人ID" prop="id">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="被邀请人ID" prop="id">
              <el-input v-model="queryForm.toUserId" />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button class="ml" type="primary" @click="handleExportExcel">
                <el-icon style="vertical-align: middle">
                  <Download />
                </el-icon>
                <span style="vertical-align: middle">导出Excel</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="listData" style="width: 100%; height: 100%">
        <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
        <el-table-column prop="userName" label="昵称" min-width="userName"></el-table-column>
        <el-table-column prop="avatar" label="头像" min-width="100">
          <template #default="scope">
            <el-avatar shape="square" :size="70" :src="scope.row.avatar" fit="cover" />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="用户状态" min-width="100">
          <template #default="scope">
            {{ scope.row.status===1?"已注销":scope.row.status===2?"被封禁":scope.row.status===3?"临时封禁":"正常" }}
          </template>
        </el-table-column>
        <el-table-column prop="bannedNum" label="封禁次数" min-width="100"></el-table-column>
        <el-table-column prop="totalIncome" label="总收益" min-width="100"></el-table-column>
        <el-table-column prop="totalRechargeAmount" label="总充值" min-width="100"></el-table-column>
        <el-table-column prop="registerTime" label="注册时间" min-width="100">
          <template #default="scope">{{ getYMDHMS("-", ":", scope.row.registerTime) }}</template>
        </el-table-column>
        <el-table-column prop="bindTime" label="绑定时间" min-width="100">
          <template #default="scope">{{ getYMDHMS("-", ":", scope.row.bindTime) }}</template>
        </el-table-column>
        <el-table-column prop="realAvatarAuthState" label="真人认证状态" min-width="100">
          <template #default="scope">
            {{ scope.row.realAvatarAuthState===1?"认证审核中":scope.row.realAvatarAuthState===2?"认证成功":scope.row.realAvatarAuthState===3?"认证失败":"未提交" }}
          </template>
        </el-table-column>
        <el-table-column prop="realNameAuthState" label="实名认证状态" min-width="100">
          <template #default="scope">
            {{ scope.row.realNameAuthState===1?"认证审核中":scope.row.realNameAuthState===2?"认证成功":scope.row.realNameAuthState===3?"认证失败":"未提交" }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="300">
          <template #default="scope">
            <el-button @click="handleLook(scope.row)" type="primary">查看补发记录</el-button>
            <el-popconfirm title="你确定补发充值收益吗?" @confirm="handleIncome(scope.row)">
              <template #reference>
                <el-button type="success">补发充值收益</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      <el-dialog v-model="detailsShow" title="补发详情" width="100%" :fullscreen="true">
        <el-table :data="listRecord" style="width: 100%">
          <el-table-column prop="userId" label="邀请人ID" min-width="120" fixed></el-table-column>
          <el-table-column prop="toUserId" label="用户ID" min-width="120"></el-table-column>
          <el-table-column prop="operatePrice" label="补发金额" min-width="120">
            <template #default="scope">{{scope.row.operatePrice}}元</template>
          </el-table-column>
          <el-table-column prop="operateTime" label="操作时间" min-width="120">
            <template #default="scope">{{ getYMDHMS("-", ":", scope.row.operateTime) }}</template>
          </el-table-column>
          <el-table-column prop="operateAccount" label="操作人" min-width="120"></el-table-column>
        </el-table>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, unref } from "vue";
import { queryfriendsList_api, reissue_recharge_record_api, reissue_recharge_income_api } from "@/api/wallet";
import { FormInstance } from "element-plus";
import { getYMDHMS } from "@/utils/date";
import pageHook from "@/hooks/pageHook";
import { ElMessage } from "element-plus";
import { aoaToSheetXlsx } from "@/utils/excel";
import { Download } from "@element-plus/icons-vue";
import { concurrencyRequest } from "@/utils/concurrencyRequest";
import { flattenArray } from "@/utils/list";

let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(query);
let listData = ref([]);
let listLoading = ref(false);
let detailsShow = ref(false);
let listRecord = ref<any>([]);
const queryFormRef = ref<FormInstance>();
let queryForm = reactive({
  userId: null,
  toUserId: null as string | null | undefined,
  applicationId: "com.dongxin.app",
});

interface User {
  userId: string;
  userName: string;
  avatar: string;
  status: number;
  bannedNum: number;
  totalIncome: number;
  totalRechargeAmount: number;
  registerTime: string;
  bindTime: string;
  realAvatarAuthState: number;
  realNameAuthState: number;
}

//查询
async function query() {
  // 1. 先查询邀请人的数据
  let res = await queryfriendsList_api({
    userId: queryForm.userId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;

  // 2. 过滤出满足条件的记录
  let filteredData = data;
  if (queryForm.userId && queryForm.toUserId) {
    // 同时查询邀请人和被邀请人
    filteredData = data.filter((item: User) => {
      return queryForm.toUserId?.includes(item.userId);
    });
  } else if (queryForm.toUserId) {
    // 单独查询被邀请人
    let res = await queryfriendsList_api({
      toUserId: queryForm.toUserId,
      pageSize: pageSize.value,
      pageNum: currentPage.value,
    });
    filteredData = res.data.data;
    total = res.data.total;
  }

  // 3. 更新总数和数据列表
  totalNum.value = total;
  listData.value = filteredData;
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  queryForm.userId = null;
  listData.value = [];
};

async function getKeys() {
  let queue: any = [];
  let pageNum = Math.ceil(totalNum.value / 10000);
  for (let i = 1; i <= pageNum; i++) {
    let urls = {
      data: {
        ...queryForm,
        pageNum: i,
        pageSize: 10000,
      },
      url: "/admin/user/invite_friends_detail/query_invite_friends_detail",
    };
    queue.push(urls);
  }
  let res = await concurrencyRequest(queue, 2);
  return flattenArray(res);
}

// 导出Excel表格
const fileName = ref("邀请人明细");
const handleExportExcel = async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }
  listLoading.value = true;
  //标题数组
  let titleArr = [
    "用户ID",
    "昵称",
    "头像",
    "用户状态",
    "封禁次数",
    "总收益",
    "总充值",
    "注册时间",
    "绑定时间",
    "真人认证状态",
    "实名认证状态",
  ];
  let table = await getKeys();
  table.forEach((item: any) => {
    item.status = item.status === 1 ? "已注销" : item.status === 2 ? "被封禁" : item.status === 3 ? "临时封禁" : "正常";
    item.realAvatarAuthState =
      item.realAvatarAuthState === 1
        ? "认证审核中"
        : item.realAvatarAuthState === 2
        ? "认证成功"
        : item.realAvatarAuthState === 3
        ? "认证失败"
        : "未提交";
    item.realNameAuthState =
      item.realNameAuthState === 1
        ? "认证审核中"
        : item.realNameAuthState === 2
        ? "认证成功"
        : item.realNameAuthState === 3
        ? "认证失败"
        : "未提交";
  });
  let header = titleArr;
  let data = table.map((item: any, _index: any) => {
    let {
      userId,
      userName,
      status,
      bannedNum,
      totalIncome,
      totalRechargeAmount,
      registerTime,
      bindTime,
      realAvatarAuthState,
      realNameAuthState,
    } = item;
    return [
      userId,
      userName,
      status,
      bannedNum,
      totalIncome,
      totalRechargeAmount,
      registerTime,
      bindTime,
      realAvatarAuthState,
      realNameAuthState,
    ];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });

  setInterval(() => {
    listLoading.value = false;
  }, 1000);
};

const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  query();
};

const handleLook = async (e: any) => {
  detailsShow.value = true;
  let res = await reissue_recharge_record_api({
    userId: queryForm.userId,
    toUserId: e.userId,
  });
  let { data } = res.data;
  listRecord.value = data;
};

const handleIncome = (e: any) => {
  reissue_recharge_income_api({ userId: queryForm.userId, toUserId: e.userId });
};
</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>