<template>
  <div class="femaleTask">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <majaSelect :applicationId="applicationId" @changeSelect="changeSelect" />
          <el-button type="primary" @click="handleadd">添加任务</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="TableLists" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="序号" min-width="60"></el-table-column>
        <el-table-column prop="titleDesc" label="任务名称" min-width="120"></el-table-column>
        <el-table-column prop="rewards" label="任务奖励" min-width="120"></el-table-column>
        <el-table-column prop="icon" label="奖品图片" min-width="120">
          <template #default="{ row }">
            <el-image  style="height: 100px;" :src="row.icon" :preview-src-list="idcaBa"
              :initial-index="0" fit="cover" :preview-teleported="true" :hide-on-click-modal="true"
              @click="resIdcaBa(row.icon)" />
          </template>
        </el-table-column>
        <el-table-column prop="refreshType" label="刷新时间" min-width="120">
          <template #default="{ row }">
            {{ row.refreshType === 1 ? "每周刷新" : '每日刷新' }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="120">
          <template #default="{ row:{status} }">
            {{ status === 0 ? "上架" : '下架' }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editreqly(scope.row)" size="small" type="primary" :icon="Edit">编辑</el-button>

          </template>
        </el-table-column>

      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />

      <el-dialog v-model="isShow" :activityName="addForm.id ? '修改任务' : '创建任务'" destroy-on-close @close="close"
        width="40%">
        <el-form ref="addFormRef" :model="addForm" label-width="130px" :rules="rules">
          <el-form-item label="马甲包">
              <majaSelect
                :applicationId="addForm.applicationId"
                @changeSelect="changeSelectAdd"
                :disabled="addForm.id"
              />
              </el-form-item>
          <el-form-item prop="title" label="任务名称" fixed>
            <el-select v-model="addForm.title" placeholder="请选择" style="width: 200px;">
              <el-option label="当日累计搭讪%d名用户" value="当日累计搭讪%d名用户" />
              <el-option label="当日累计时长视频大于或等于%d分钟" value="当日累计时长视频大于或等于%d分钟" />
              <el-option label="送%d个金币礼物" value="送%d个金币礼物" />
              <el-option label="邀请%d个用户" value="邀请%d个用户" />
              <el-option label="完成%d次视频通话" value="完成%d次视频通话" />
              <el-option label="收到%d个礼物" value="收到%d个礼物" />
              <el-option label="关注%d名用户" value="关注%d名用户" />
              <el-option label="点赞/评论%d条动态" value="点赞/评论%d条动态" />
              <el-option label="与亲密老友完成文字、语音、视频且收益大于%d积分" value="与亲密老友完成文字、语音、视频且收益大于%d积分" />
            </el-select>
          </el-form-item>
          <el-form-item prop="taskCnt" label="需完成次数" fixed>
            <el-input v-model="addForm.taskCnt" style="width: 200px;"> </el-input>
          </el-form-item>
          <el-form-item prop="taskNum" label="任务详情" fixed>
            <el-input v-model="addForm.taskNum" style="width: 200px;"> </el-input>
          </el-form-item>
          <el-form-item prop="type" label="任务类型" fixed>
            <el-select v-model="addForm.type" placeholder="请选择" style="width: 200px;">
              <el-option label="搭讪次数" :value="1" />
              <el-option label="视频时长" :value="2" />
              <el-option label="收到礼物" :value="3" />
              <el-option label="送出礼物" :value="4" />
              <el-option label="邀请人数" :value="5" />
              <el-option label="关注数量" :value="6" />
              <el-option label="点赞、评论数" :value="7" />
              <el-option label="视频通话次数" :value="8" />
              <el-option label="亲密老友" :value="9" />
            </el-select>
          </el-form-item>
          <el-form-item prop="icon" label="奖励图标" fixed>
            <CosUpload :imageUrl="addForm.icon" @handleImagUrl="changeImgurl" />
          </el-form-item>
          <el-form-item prop="rewards" label="奖励名称" fixed>
            <el-input v-model="addForm.rewards"   style=" width: 200px;"/>
          </el-form-item>

          <el-form-item prop="rewardsType" label="奖励类型" fixed>
            <el-select v-model="addForm.rewardsType" placeholder="请选择" style="width: 200px;">
              <el-option label="积分" :value="1" />
              <el-option label="金币" :value="2" />
              <el-option label="礼物" :value="3" />
              <el-option label="搭讪卡" :value="4" />
              <el-option label="视频体验卡" :value="5"/> 
              <el-option label="赠送金币" :value="6"/> 
              <el-option label="消息卡" :value="7"/> 
            </el-select>
          </el-form-item>
       
          <el-form-item v-if="addForm.rewardsType===3" prop="rewardsGiftId" label="礼物ID" fixed>
            <el-input-number :controls="false" v-model="addForm.rewardsGiftId"/>
          </el-form-item>
          <el-form-item prop="rewardsNum">
            <el-input-number :controls="false" v-model="addForm.rewardsNum" style="width: 200px;" placeholder="自定义数量" />
          </el-form-item>
          <el-form-item prop="refreshType" label="刷新时间" fixed>
            <el-select v-model="addForm.refreshType" placeholder="请选择" style="width: 200px;">
              <el-option label="每日3:00刷新" :value="0" />
              <el-option label="每周一3:00刷新" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item prop="status" label="状态" fixed>
            <el-radio-group v-model="addForm.status" class="ml-4">
              <el-radio :label="0">上架</el-radio>
              <el-radio :label="1">下架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="addintimacy(addFormRef)">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { addUser_daily_task, queryUser_daily_task } from '@/api/index'
import { ref, reactive} from 'vue'
import CosUpload from '@/components/CosUpload.vue'
import { Edit } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
let  applicationId=ref('com.dongxin.app')
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(query);
  let idcaBa = ref<any>([])
let TableLists = ref([]);
let addFormRef = ref<any>(null);
let isShow = ref<boolean>(false);
let addForm = reactive<any>({
  type: null,
  title: null,
  refreshType: null,
  status: 1,
  rewardsType:null,
  rewardsNum: null,
  gender: 2,
  taskNum: null,
  icon:null,
  rewards:null,
  rewardsGiftId:null,
  taskCnt:null,
  applicationId:'com.dongxin.app'
});
async function query() {
  let { data: { data, total } } = await queryUser_daily_task({
    gender: 2,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:applicationId.value,
  });

  TableLists.value = data;
  totalNum = total;
}
const resIdcaBa = (e: any) => {
  idcaBa.value = []
  if (idcaBa.value.length == 0) {
    idcaBa.value.push(e)
  }
}

query();

const rules = reactive<any>({
  title: [{ required: true, message: "请输入", trigger: "blur" }],
  rewards: [{ required: true, message: "请输入", trigger: "blur" }],
  type: [{ required: true, message: "请选择", trigger: "blur" }],
  refreshType: [{ required: true, message: "请选择", trigger: "change" }],
  status: [{ required: true, message: "请选择", trigger: "change" }],
  rewardsType: [{ required: true, message: "请选择", trigger: "change" }],
  icon:[{ required: true, message: "请上传", trigger: "change" }],
  taskCnt:[{ required: true, message: "请输入", trigger: "change" }],
  taskNum:[{ required: true, message: "请输入", trigger: "change" }],
  rewardsGiftId:[{ required: true, message: "请输入", trigger: "blur" }],
});


const changeImgurl = (e: string) => {
  addForm.icon = e;
  addFormRef._rawValue.validateField("icon");
}

const editreqly = (e: any) => {
  addForm =reactive(JSON.parse(JSON.stringify( e)));
  isShow.value = true;


}
const close = () => {
  addForm = reactive<any>({
    chatupCardCount: null,
    type: null,
    title: null,
    refreshType: null,
    enable: null,
    rewardsNum: null,
    gender: 2,
    rewardsType:null,
    taskNum: null,
    rewards:null,
    status: 1,
    taskCnt:null,
    rewardsGiftId:null,
  });
  isShow.value = false;
}

const handleadd = () => {
  isShow.value = true
}

const addintimacy = (_e: any) => {
  addFormRef._rawValue.validate(async (_valid: any) => {
    if (_valid) {
        await addUser_daily_task({ ...addForm })
        query();
        isShow.value = false
    }
  })
}
const changeSelect=(e:string)=>{
  applicationId.value=e;
  query();
}
const changeSelectAdd=(e:string)=>{
  addForm.applicationId=e;
}
</script>

<style lang="scss" scoped>
.femaleTask {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    width: 100%;

    .el-form {
      width: 100%;
    }
  }
}
</style>