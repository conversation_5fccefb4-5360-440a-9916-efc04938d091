<template>
  <div class="taskBoard">
    <el-card class="box-card">
    <template #header>
      <div class="card-header">
        <majaSelect :applicationId="applicationId" @changeSelect="changeSelect" />
        <el-select v-model="gender" placeholder="请选择" @change="handleChange" style="width: 200px;">
          <el-option label="男" :value="1" />
          <el-option label="女" :value="2" />
        </el-select>
      </div>
    </template>
    <div class="table-container">
      <el-table :data="intimacyListTab" style="width: 100%; height: 100%">
        <el-table-column :prop="item.prop" :label="item.title" fixed v-for=" (item, index) in tableList" :key="index">
        </el-table-column>
      </el-table>
    </div>

    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
  </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { daily_taskList_api } from '@/api/index'
import pageHook from "@/hooks/pageHook";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(query);
  let  applicationId=ref('com.dongxin.app')
let gender = ref(1);
let intimacyListTab = ref([]);
let manList = ref([
  {
    title: '时间',
    prop: 'date'
  },
  {
    title: '当日累计搭讪5名用户',
    prop: 'taskNameOne',
  },
  {
    title: '当日累计时长视频大于或等于5分钟',
    prop: 'taskNameTwo',
  },
  {
    title: '送3个金币礼物',
    prop: 'taskNameTree',
  },
  {
    title: '全部完成/人',
    prop: 'allNum'
  },])

let femaleList = ref([
  {
    title: '时间',
    prop: 'date'
  },
  {
    title: '当日累计搭讪20名用户',
    prop: 'taskNameOne',
  },
  {
    title: '当日累计时长视频大于或等于50分钟',
    prop: 'taskNameTwo',
  },
  {
    title: '收到8个礼物',
    prop: 'taskNameTree',
  },
  {
    title: '全部完成/人',
    prop: 'allNum'
  },])
let tableList = ref<any>(manList.value);

async function query(){
  let res = await daily_taskList_api({ 
    gender: gender.value ,
     pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId: applicationId.value,
  });
  intimacyListTab.value = res.data.data;
  totalNum.value = res.data.total;

}

const handleChange = (e: any) => {
  if (e === 1) {
    tableList.value = manList.value;
  } else {
    tableList.value = femaleList.value;
  }
  query();
}
query();
const changeSelect=(e:string)=>{
  applicationId.value=e;
  query();
}
</script>

<style lang="scss" scoped>
.taskBoard {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }
}
</style>