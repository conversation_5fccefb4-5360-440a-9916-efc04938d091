<template>
  <div class="invitesettlement">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="queryForm"
            class="demo-form-inline"
          >
          <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="状态" prop="checkStatus">
              <el-select v-model="queryForm.checkStatus" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="待审核" :value="1" />
                <el-option label="审核通过" :value="2" />
                <el-option label="审核失败" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="profileType">
              <el-select v-model="queryForm.profileType" placeholder="请选择">
                <el-option label="全部" :value="opNulltype" />
                <el-option label="昵称" :value="1" />
                <el-option label="个性签名" :value="2" />
                <el-option label="自我介绍" :value="3" />
                <el-option label="职业" :value="4" />
                <el-option label="注册昵称" :value="5" />
                <el-option label="头像" :value="6" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择" prop="id">
              <el-date-picker
                v-model="date"
                :clearable="false"
                type="daterange"
                unlink-panels
                value-format="x"
                format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                size="default"
                @change="timeChange"
              />
            </el-form-item>

            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryData">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="settlementTab" style="width: 100%; height: 100%">
        <el-table-column prop="userId" label="uid" min-width="120" fixed></el-table-column>
        <el-table-column prop="profileType" label="类型"  fixed>
          <template #default="{ row }">
            {{row.profileType===1?'昵称':row.profileType===2?'个性签名':row.profileType===3?'自我介绍':row.profileType===4?'职业':row.profileType===5?'注册昵称':row.profileType===6?'头像':''}}
          </template>
        </el-table-column>
        <el-table-column prop="profileNickname" label="昵称" min-width="120" ></el-table-column>
        <el-table-column prop="profileIntroduction" label="个性签名" min-width="120" ></el-table-column>
        <el-table-column prop="selfIntroduction" label="自我介绍文本" min-width="350"></el-table-column>
        <el-table-column prop="selfPicture" label="自我介绍图片" min-width="120" >
          <template #default="scope">
            <el-image
              v-if="scope.row.selfPicture"
              style="width: 100px; height: 100px"
              :src="scope.row.selfPicture"
              :zoom-rate="1.2"
              :preview-src-list="faceList"
              :initial-index="0"
              fit="cover"
              :preview-teleported="true"
              :hide-on-click-modal="true"
              @click="fdimg(scope.row.selfPicture)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="profileOccupation" label="职业" min-width="120" ></el-table-column>
        <el-table-column prop="registerNickname" label="注册昵称" min-width="120" ></el-table-column>
        <el-table-column prop="avatar" label="头像" min-width="150">
          <template #default="{ row }">
            <div class="img-box" v-if="row.avatar">
              <el-image
                style="width: 70px; height: 70px"
                :src="row.avatar"
                :zoom-rate="1.2"
                :preview-src-list="faceList"
                :initial-index="0"
                fit="cover"
                :preview-teleported="true"
                :hide-on-click-modal="true"
                @click="fdimg(row.avatar)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="realFace" label="真人认证头像" min-width="150">
          <template #default="{ row }">
            <div class="img-box" v-if="row.realFace">
              <el-image
                style="width: 70px; height: 70px"
                :src="row.realFace"
                :zoom-rate="1.2"
                :preview-src-list="faceList"
                :initial-index="0"
                fit="cover"
                :preview-teleported="true"
                :hide-on-click-modal="true"
                @click="fdimg(row.realFace)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="checkStatus" label="审核状态" min-width="150">
          <template #default="{ row }">
            {{ row.checkStatus === 2 ? "审核通过" : row.checkStatus === 3 ? "审核失败" : "待审核" }}
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" min-width="180">
          <template #default="{ row }">
            {{ getYMDHMS("-", ":", row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="lastOperator"
          label="最后操作人"
          min-width="120"
        ></el-table-column>
        <el-table-column prop="operatorTime" label="操作时间" min-width="180">
          <template #default="{ row }">
          <span  v-if="row.operatorTime">
            {{ row.operatorTime ? getYMDHMS("-", ":", row.operatorTime) : "" }}
          </span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button
              @click="editSettlement(scope.row, 2)"
              v-if="scope.row.checkStatus === 1 &&scope.row.profileType !== 5"
              size="small"
              type="primary"
              >审核通过</el-button
            >
            <el-button
              @click="editSettlement(scope.row, 3)"
              v-if="scope.row.checkStatus ===1"
              size="small"
              type="success"
              >不通过</el-button
            >
            <el-button
              @click="editSettlement(scope.row, 2)"
              v-if="scope.row.checkStatus === 1&&scope.row.profileType === 5"
              size="small"
              type="primary"
              >忽略</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog
        v-model="isShow"
        title="理由"
        destroy-on-close
        @close="close"
        width="35%"
      >
        <el-form ref="addFormRef" label-width="100px">
          <el-form-item prop="title" label="理由" fixed>
            <el-select v-model="reason">
              <el-option value="色情诱导" label="色情诱导"></el-option>
              <el-option value="性感低俗" label="性感低俗"></el-option>
              <el-option value="广告" label="广告"></el-option>
              <el-option
                value="招呼语图片与本人不符"
                label="招呼语图片与本人不符"
              ></el-option>
              <el-option value="AI合成图片" label="AI合成图片"></el-option>
              <el-option
                value="不能包含距离过近等词汇"
                label="不能包含距离过近等词汇"
              ></el-option>
              <el-option value="其它" label="其它"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="handleAudit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import pageHook from "@/hooks/pageHook";
import { getYMDHMS } from "@/utils/date";
import { query_user_profile_check_api, update_user_profile_check_api } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  queryData
);
const queryFormRef = ref<FormInstance>();
let settlementTab = ref([]);
let faceList = ref<any>([]);
let opNull = ref<any>(null);
let opNulltype = ref<any>(null);
let queryForm = reactive<any>({
  userId: null,
  checkStatus: 1,
  profileType: null,
  applicationId:''
});
let reason = ref("色情诱导");
let isShow = ref(false);
let date = ref();
let activeId = ref();
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

const handleAudit = async () => {
  await update_user_profile_check_api({ id: activeId.value, checkStatus: 3, reason: reason.value });
  close();
  queryData();
};

// 编辑
const editSettlement = async (row: any, checkStatus: any) => {
  if (checkStatus === 3) {
    isShow.value = true;
    activeId.value = row.id;
    return;
  }
  await update_user_profile_check_api({ id: row.id, checkStatus });
  queryData();
};
const timeChange = (data: any) => {
  queryForm.begin = data[0];
  queryForm.end = data[1];
};

const fdimg = (img: any) => {
  faceList.value = [img];
};

// 查询
function queryData() {
  query_user_profile_check_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  }).then((res: any) => {
    let { data, total } = res.data;
    settlementTab.value = data;
    totalNum.value = total;
  });
}
queryData();
const close = () => {
  isShow.value = false;
};

const onreset = (formEl: FormInstance | undefined) => {
  date.value = [];
  if (!formEl) return;
  formEl.resetFields();
  queryData();
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  queryData();
}
</script>

<style lang="scss" scoped>
.invitesettlement {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>
