<template>
  <div class="invitesettlement">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="状态" prop="quickReplyStatus">
              <el-select v-model="queryForm.quickReplyStatus" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="待审核" :value="1" />
                <el-option label="审核通过" :value="2" />
                <el-option label="审核失败" :value="3" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="类型" prop="type">
              <el-select v-model="queryForm.type" placeholder="请选择">
                <el-option label="全部" :value="opNulltype" />
                <el-option label="文字打招呼" :value="1" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="时间选择" prop="id">
              <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :shortcuts="shortcuts" size="default" @change="timeChange" />
            </el-form-item>

            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query_user_quick_replyData">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="settlementTab" style="width: 100%; height: 100%" v-loading="loading">
        <el-table-column prop="id" label="序号" min-width="80" fixed></el-table-column>
        <el-table-column prop="userId" label="uid" min-width="120"></el-table-column>
        <el-table-column prop="quickReplyText" label="文本消息" min-width="150"></el-table-column>
        <el-table-column prop="quickReplyStatus" label="认证状态" min-width="150">
          <template #default="{ row }">
            {{ row.quickReplyStatus === 2 ? '审核通过' : row.quickReplyStatus === 3 ? '审核失败' : '待审核' }}
          </template>
        </el-table-column>
        <el-table-column prop="usedPayGolds" label="认证时间" min-width="180">
          <template #default="{ row }">
            {{ getYMDHMS('-', ':', row.createdTime * 1000) }}
          </template>
        </el-table-column>
        <el-table-column prop="operatorTime" label="操作时间" min-width="180">
          <template #default="{ row }">
            {{ row.operatorTime?getYMDHMS('-', ':', row.operatorTime):'' }}
          </template>
        </el-table-column>
        <el-table-column prop="lastOperator" label="最后操作人" min-width="120"></el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editSettlement(scope.row, 2)" v-if="scope.row.quickReplyStatus !== 2" size="small"
              type="primary">审核通过</el-button>
            <el-button @click="editSettlement(scope.row, 3)" v-if="scope.row.quickReplyStatus !== 3" size="small"
              type="success">不通过</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[20,50,100]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        <el-dialog v-model="isShow" title="理由" destroy-on-close @close="close" width="35%">
        <el-form ref="addFormRef" label-width="100px">
          <el-form-item prop="title" label="理由" fixed>
            <el-select v-model="reason">
              <el-option value="色情诱导" label="色情诱导"></el-option>
              <el-option value="性感低俗" label="性感低俗"></el-option>
              <el-option value="广告" label="广告"></el-option>
              <el-option value="其它" label="其它"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button> 
            <el-button type="primary" @click="handleAudit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>
  
<script setup lang="ts">
import { ref, reactive  } from "vue";
import type { FormInstance } from "element-plus";
import pageHook from "@/hooks/pageHook";
import { getYMDHMS } from '@/utils/date'
import { query_user_quick_reply, update_user_quick_reply } from '@/api/index'
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(query_user_quick_replyData);
const queryFormRef = ref<FormInstance>();
let settlementTab = ref([]);
let opNull = ref<any>(null);
// let opNulltype = ref<any>(null)
let queryForm = reactive<any>({
  userId: null,
  quickReplyStatus: 1,
  applicationId:''
});
let reason=ref('色情诱导')
let isShow = ref(false);
let date = ref();
let activeId = ref();
let loading=ref(false);
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];


const handleAudit = async()=>{
  loading.value=true;
  await update_user_quick_reply({ id: activeId.value, quickReplyStatus:3, reason:reason.value})
  close();
  query_user_quick_replyData()
}

// 编辑
const editSettlement = async (row: any, quickReplyStatus: any) => {
  if(quickReplyStatus===3){
    isShow.value=true;
    activeId.value=row.id;
    return;
  };
  await update_user_quick_reply({ id: row.id, quickReplyStatus })
  query_user_quick_replyData()
}
const timeChange = (data: any) => {
  queryForm.begin = data[0];
  queryForm.end = data[1];
};



// 查询
 function query_user_quick_replyData() {
  loading.value=true;
  query_user_quick_reply({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  }).then((res:any)=>{
    let { data, total } = res.data;
  settlementTab.value = data;
  totalNum.value = total;
  loading.value=false;
  })

}
query_user_quick_replyData();
const close = ()=>{
  isShow.value=false;
}

const onreset = (formEl: FormInstance | undefined) => {
  date.value = [];
  if (!formEl) return;
  formEl.resetFields();
  query_user_quick_replyData();
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query_user_quick_replyData();
}

</script>
  
<style lang="scss" scoped>
.invitesettlement {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>
  