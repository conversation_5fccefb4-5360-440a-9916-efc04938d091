<template>
  <div class="album">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <!-- <el-form-item label="ID" prop="id">
              <el-input v-model="queryForm.id" />
            </el-form-item> -->
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="通过" :value="0" />
                <el-option label="待审核" :value="1" />
                <el-option label="违规" :value="2" />
                <el-option label="自己可见" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择" prop="date">
              <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :shortcuts="shortcuts" size="default" @change="timeChange" />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryAlbum">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="albumTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" min-width="80" fixed></el-table-column>
        <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
        <el-table-column label="性别" min-width="60">
          <template #default="scope">{{ scope.row.gender == 1 ? "男" : scope.row.gender == 2 ? "女" : "" }}</template>
        </el-table-column>
        <el-table-column prop="realPerson" label="真人头像" min-width="150">
          <template #default="scope">
            <el-image style="width: 100px; height: 100px" :src="scope.row.realPerson" :zoom-rate="1.2"
              :preview-src-list="photoList" :initial-index="0" fit="cover" :preview-teleported="true"
              :hide-on-click-modal="true" @click="resPerson(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="photo" label="照片" min-width="150">
          <template #default="scope">
            <el-image style="width: 100px; height: 100px" :src="scope.row.photo" :zoom-rate="1.2"
              :preview-src-list="photoList" :initial-index="0" fit="cover" :preview-teleported="true"
              :hide-on-click-modal="true" @click="resPhoto(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="uploadTime" label="上传时间" min-width="180">
          <template #default="scope">
            <span v-show="scope.row.uploadTime">{{ getYMDHMS("-", ":", scope.row.uploadTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" min-width="120">
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="120">
          <template #default="scope">
            {{ scope.row.status == 0 ? "通过" : scope.row.status == 1 ? "待审核" : scope.row.status == 2 ? "违规" :
              scope.row.status == 3 ? "自己可见" : "" }}
          </template>
        </el-table-column>
        <el-table-column prop="operator" label="最后操作人" min-width="120"></el-table-column>
        <el-table-column label="操作描述" width="120">
          <template #default="scope">
            {{ scope.row.operatorDesc }}</template>
        </el-table-column>
        <el-table-column prop="operatorTime" label="操作时间" min-width="180">
          <template #default="scope">
            <span v-show="scope.row.operatorTime">{{ getYMDHMS("-", ":", scope.row.operatorTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button @click="editAlbum(scope.row, 0)" v-show="scope.row.status != 0" size="small"
              type="success">通过</el-button>
            <el-button @click="editDesc(scope.row, 2)" v-show="scope.row.status != 2" size="small"
              type="danger">违规</el-button>
            <el-button @click="editAlbum(scope.row, 3)" v-show="scope.row.status != 3" size="small"
              type="primary">自己可见</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      <el-dialog v-model="editShow" title="操作原因">
        <el-form :model="descForm">
          <el-form-item label="类型">
            <el-select v-model="descForm.editValue" placeholder="选择操作原因">
              <el-option label="动态色情" value="动态色情" />
              <el-option label="性感低俗" value="性感低俗" />
              <el-option label="广告" value="广告" />
              <el-option label="二维码" value="二维码" />
              <el-option label="暴恐" value="暴恐" />
              <el-option label="违禁" value="违禁" />
              <el-option label="涉证" value="涉证" />
              <el-option label="恶心类" value="恶心类" />
              <el-option label="涉价值观" value="涉价值观" />
              <el-option label="相似度过低" value="相似度过低" />
              <el-option label="AI合成图片" value="AI合成图片" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          <el-form-item label="原因" v-if="descForm.editValue == '其他'">
            <el-input v-model="descForm.editValueIpt" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editShow = false">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import { getYMDHMS } from "@/utils/date";
import pageHook from "@/hooks/pageHook";
import { queryAlbum_api, modifyAlbum_api } from "@/api/contentsecurity";

let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(queryAlbum);

let albumTab = ref<any>([]);
let photoList = ref<any>([]);
let opNull = ref<any>(null);
const queryFormRef = ref<FormInstance>();
let date = ref();
const queryForm = reactive<any>({
  id: null,
  userId: null,
  status: 1,
  endTime: null,
  startTime: null,
  applicationId:''
});
let editShow = ref<boolean>(false);
let descForm = reactive<any>({
  id: null,
  editValue: null,
  editValueIpt: null,
  displayStatus: null,
});
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

async function getAlbum() {
  let res = await queryAlbum_api({
    status: queryForm.status,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId,
  });
  let { data, total } = res.data;
  albumTab.value = data;
  totalNum.value = total;
}
getAlbum();

const resPhoto = (row: any) => {
  photoList.value = [];
  if (photoList.value.length == 0) {
    photoList.value.push(row.photo);
  }
};
const resPerson = (row: any) => {
  photoList.value = [];
  if (photoList.value.length == 0) {
    photoList.value.push(row.realPerson);
  }
};

//编辑
const editAlbum = async (row: any, status: any) => {
  await modifyAlbum_api({ id: row.id, status });
  queryAlbum();
};

const editDesc = (row: any, status: any) => {
  descForm.id = row.id;
  descForm.displayStatus = status;
  descForm.editValueIpt = null
  descForm.editValue = null
  editShow.value = true;
};

const editSubMit = async () => {
  if (descForm.editValue == "其他") { descForm.editValue = descForm.editValueIpt }
  await modifyAlbum_api({
    id: descForm.id,
    status: descForm.displayStatus,
    operatorDesc: descForm.editValue
  });
  queryAlbum();
  editShow.value = false;
};

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};
async function queryAlbum() {
  let res = await queryAlbum_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  albumTab.value = data;
  totalNum.value = total;
}
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  date.value = null;
  queryForm.status = 1
  formEl.resetFields();
  getAlbum();
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  queryAlbum();
}
</script>

<style lang="scss" scoped>
.album {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .el-form {
    width: 100%;

    .el-select {
      width: 100%;
    }
  }

  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 350px;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>