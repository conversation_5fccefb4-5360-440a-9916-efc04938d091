<template>
  <div class="dynamic">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-query">
            <el-form :inline="true" :model="queryForm" class="query-form-inline">
              <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
              <el-form-item label="用户ID">
                <el-input v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item label="动态ID">
                <el-input v-model="queryForm.id" />
              </el-form-item>
              <el-form-item label="动态类型">
                <el-select v-model="queryForm.type" placeholder="选择动态类型" class="header-select">
                  <el-option label="全部" :value="opNull" />
                  <el-option v-for="(item, i) in optionType" :key="i" :label="item.lab" :value="i" />
                </el-select>
              </el-form-item>
              <el-form-item label="展示状态">
                <el-select v-model="queryForm.displayStatus" placeholder="选择展示状态" class="header-select">
                  <el-option label="全部" :value="opNull" />
                  <el-option v-for="(item, i) in option" :key="i" :label="item.lab" :value="i" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间选择">
                <el-date-picker v-model="queryForm.date" :clearable="false" type="daterange" unlink-panels
                  value-format="x" format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  :shortcuts="shortcuts" size="default" @change="timeChange" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="querySubmit">查询</el-button>
                <el-button @click="onreset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="userRef" :data="dynamicData" style="width: 100%; height: 100%" lazy>
          <el-table-column fixed label="用户ID" width="100">
            <template #default="scope">{{ scope.row.userId }}</template>
          </el-table-column>
          <el-table-column label="动态ID" width="80">
            <template #default="scope">{{ scope.row.id }}</template>
          </el-table-column>
          <el-table-column label="动态类型" width="120">
            <template #default="scope">{{ scope.row.type }}</template>
          </el-table-column>
          <el-table-column label="展示状态" width="120">
            <template #default="scope">{{ scope.row.displayStatus }}</template>
          </el-table-column>
          <el-table-column label="动态文本" width="200">
            <template #default="scope">{{ scope.row.text }}</template>
          </el-table-column>
          <el-table-column label="(文本)易盾状态" width="120">
            <template #default="scope">{{ scope.row.textSuggestion }}</template>
          </el-table-column>
          <el-table-column label="(文本)失败原因" width="120">
            <template #default="scope">{{ scope.row.textLabels }}</template>
          </el-table-column>
          <el-table-column label="图片" min-width="150">
            <template #default="scope">
              <el-image v-for="(v, i) in scope.row.resourceInfo" :key="i" @click="onImgshow(scope.row.resourceInfo)"
               :src="v.imageUrl" :zoom-rate="1.2" :preview-src-list="imgList" :hide-on-click-modal="true"
                :initial-index="0" fit="cover" :preview-teleported="true">
                <template #error>
                  <div class="image-slot">
                    {{ v.imageUrl == "" ? "无" : "" }}
                  </div>
                </template>
              </el-image> 
              <!-- <div class="eimage">
                <el-image @click="onImgshow(scope.row.resourceInfo)" :src="scope.row.resourceInfo[0].imageUrl" :hide-on-click-modal="true"
                  :zoom-rate="1.2" :preview-src-list="imgList" :initial-index="0" fit="cover" :preview-teleported="true">
                  <template #error>
                    <div class="image-slot">
                      {{ scope.row.resourceInfo[0].imageUrl == "" ? "无" : "" }}
                    </div>
                  </template>
                </el-image>
                <span>{{ scope.row.resourceInfo.length > 1 ? "......" : "" }}</span>
              </div> -->
            </template>
          </el-table-column>
          <el-table-column label="视频" width="150">
            <template #default="scope">
              <video @click="onVideoshow(scope.row.resourceInfo)" width="50" v-for="(v, i) in scope.row.resourceInfo"
                :key="i" :src="v.videoUrl"></video>
            </template>
          </el-table-column>
          <el-table-column label="媒体状态" width="150">
            <template #default="scope">
              <span class="labels" v-for="(v, i) in scope.row.resourceInfo" :key="i">{{ v.suggestion }}</span>
            </template>
          </el-table-column>
          <el-table-column label="媒体失败原因" width="150">
            <template #default="scope">
              <span class="labels" v-for="(v, i) in scope.row.resourceInfo" :key="i">{{ v.labels }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发布时间" width="200">
            <template #default="scope">{{
              getYMDHMS("-", ":",scope.row.publishTime)
            }}</template>
          </el-table-column>
          <el-table-column label="最后操作人" width="120">
            <template #default="scope">{{ scope.row.lastOperator }}</template>
          </el-table-column>
          <el-table-column label="操作描述" width="120">
            <template #default="scope">
              {{ scope.row.operatorDesc }}</template>
          </el-table-column>
          <el-table-column label="修改时间" width="200">
            <template #default="scope">
              {{ getYMDHMS("-",":", scope.row.modifyTime) }}</template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-select v-model="scope.row.displayStatus" placeholder="选择展示状态" @change="editDynamic(scope.row)">
                <el-option v-for="(item, i) in option" :key="i" :label="item.lab" :value="i" />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      <el-dialog v-model="isShow" @close="videoClose">
        <video ref="videoRef" class="dialog-video" :src="videoList" controls autoplay muted loop></video>
      </el-dialog>
      <el-dialog v-model="editShow" title="操作原因" @close="editCancel">
        <el-form :model="descForm">
          <el-form-item label="类型">
            <el-select v-model="descForm.editValue" placeholder="选择操作原因">
              <el-option label="动态色情" value="动态色情" />
              <el-option label="性感低俗" value="性感低俗" />
              <el-option label="广告" value="广告" />
              <el-option label="二维码" value="二维码" />
              <el-option label="暴恐" value="暴恐" />
              <el-option label="违禁" value="违禁" />
              <el-option label="涉证" value="涉证" />
              <el-option label="恶心类" value="恶心类" />
              <el-option label="涉价值观" value="涉价值观" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          <el-form-item label="原因" v-if="descForm.editValue == '其他'">
            <el-input v-model="descForm.editValueIpt" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editCancel">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import pageHook from "@/hooks/pageHook";
import { queryDynamic_api, modifyDynamic_api } from "@/api/contentsecurity";
import { getYMDHMS} from "@/utils/date";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(getDynamic);
let dynamicData = ref<any>([]); //表格数据
let isShow = ref<boolean>(false);
let editShow = ref<boolean>(false);
let imgList = ref<any>([]);
let videoList = ref<any>([]);
let videoRef = ref<any>(null)
let descForm = reactive<any>({
  editValue: null,
  editValueIpt: null,
  displayStatus:null,
});
let dongId = ref<any>(null);
let opNull = ref<any>(null)
let option = ref<any>([
  { lab: "公开" },
  { lab: "违规" },
  { lab: "仅自己可见" },
  { lab: "已删除" },
  { lab: "待审核" },
  { lab: "推荐" },
]);
let optionType = ref<any>([
  { lab: "单独文本" },
  { lab: "图片" },
  { lab: "视频" },
  { lab: "语音" },
]);
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
let queryForm = reactive<any>({
  //查询
  date: [],
  userId: null,
  id: null,
  type: null,
  displayStatus: 4,
  startTime: null,
  endTime: null,
  applicationId:''
});

async function getDynamic() {
  let res = await queryDynamic_api({
    displayStatus: queryForm.displayStatus,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId
  });
  let { data, total } = res.data;
  data.forEach((v: any) => {
    if (v.type == "TEXT") {
      v.type = "文本";
    } else if (v.type == "PICTURE") {
      v.type = "图片";
    } else if (v.type == "VIDEO") {
      v.type = "视频";
    } else {
      v.type = "语音";
    }

    if (v.displayStatus == "OPEN") {
      v.displayStatus = "公开";
      v.statusValue = 0;
    } else if (v.displayStatus == "VIOLATION") {
      v.displayStatus = "违规";
      v.statusValue = 1;
    } else if (v.displayStatus == "YOURSELF") {
      v.displayStatus = "仅自己可见";
      v.statusValue = 2;
    } else if (v.displayStatus == "DELETED") {
      v.displayStatus = "已删除";
      v.statusValue = 3;
    } else if (v.displayStatus == "PENDING") {
      v.displayStatus = "待审核";
      v.statusValue = 4;
    } else if (v.displayStatus == "RECOMMEND") {
      v.displayStatus = "推荐";
      v.statusValue = 5;
    } else {
      v.displayStatus = "待审核";
      v.statusValue = 4;
    }

    if (v.textSuggestion == -1) {
      v.textSuggestion = "暂未有结果";
    } else if (v.textSuggestion == 0) {
      v.textSuggestion = "通过";
    } else if (v.textSuggestion == 1) {
      v.textSuggestion = "嫌疑";
    } else {
      v.textSuggestion = "不通过";
    }

    v.resourceInfo.forEach((item: any) => {
      if (item.suggestion == -1) {
        item.suggestion = "暂未有结果";
      } else if (item.suggestion == 0) {
        item.suggestion = "通过";
      } else if (item.suggestion == 1) {
        item.suggestion = "嫌疑";
      } else {
        item.suggestion = "违规";
      }
    })
  });

  dynamicData.value = data;
  totalNum.value = total;
}
getDynamic();

// 编辑
const editDynamic = (row: any) => {
  dongId.value = row.id;
  if (row.displayStatus == 1 || row.displayStatus == 3) {
    descForm.editValue = null
    descForm.editValueIpt = null
    descForm.displayStatus = row.displayStatus
    editShow.value = true
  }
  if (row.displayStatus == 0  || row.displayStatus == 2 || row.displayStatus == 4 || row.displayStatus == 5) {
    dynamicData.value.forEach(async (v: any) => {
      if (v.id == row.id) {
        await modifyDynamic_api({
          id: dongId.value,
          displayStatus: row.displayStatus,
        });
        querySubmit()
      }
    })
  }
};
const editSubMit = async () => {
  if(descForm.editValue == "其他"){descForm.editValue = descForm.editValueIpt}
  await modifyDynamic_api({
    id: dongId.value,
    displayStatus: descForm.displayStatus,
    operatorDesc:descForm.editValue
  });
  querySubmit()
  editShow.value = false;
};

const editCancel = () => {
  querySubmit()
  editShow.value = false;
}
// 点击图片
const onImgshow = (row: any) => {
  imgList.value = [];
  row.forEach((v: any) => {
    imgList.value.push(v.imageUrl);
  });
};
// 点击视频
const onVideoshow = (row: any) => {
  videoList.value = row[0].videoUrl;
  isShow.value = true;
};
const videoClose = () => {
  videoRef.value.pause()
}

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

const querySubmit = async () => {
  let { userId, id, type, displayStatus, startTime, endTime,applicationId } = queryForm;
  let res = await queryDynamic_api({
    userId,
    id,
    type,
    displayStatus,
    startTime,
    endTime,
    applicationId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  dynamicData.value = data;
  totalNum.value = total;
  data.forEach((v: any) => {
    if (v.type == "TEXT") {
      v.type = "文本";
    } else if (v.type == "PICTURE") {
      v.type = "图片";
    } else if (v.type == "VIDEO") {
      v.type = "视频";
    } else {
      v.type = "语音";
    }

    if (v.displayStatus == "OPEN") {
      v.displayStatus = "公开";
      v.statusValue = 0;
    } else if (v.displayStatus == "VIOLATION") {
      v.displayStatus = "违规";
      v.statusValue = 1;
    } else if (v.displayStatus == "YOURSELF") {
      v.displayStatus = "仅自己可见";
      v.statusValue = 2;
    } else if (v.displayStatus == "DELETED") {
      v.displayStatus = "已删除";
      v.statusValue = 3;
    } else if (v.displayStatus == "PENDING") {
      v.displayStatus = "待审核";
      v.statusValue = 4;
    } else if (v.displayStatus == "RECOMMEND") {
      v.displayStatus = "推荐";
      v.statusValue = 5;
    } else {
      v.displayStatus = "待审核";
      v.statusValue = 4;
    }

    if (v.textSuggestion == -1) {
      v.textSuggestion = "暂未有结果";
    } else if (v.textSuggestion == 0) {
      v.textSuggestion = "通过";
    } else if (v.textSuggestion == 1) {
      v.textSuggestion = "嫌疑";
    } else {
      v.textSuggestion = "不通过";
    }

    v.resourceInfo.forEach((item: any) => {
      if (item.suggestion == -1) {
        item.suggestion = "暂未有结果";
      } else if (item.suggestion == 0) {
        item.suggestion = "通过";
      } else if (item.suggestion == 1) {
        item.suggestion = "嫌疑";
      } else {
        item.suggestion = "违规";
      }
    })
  });
};

const onreset = () => {
  queryForm.date = [];
  queryForm.userId = null;
  queryForm.id = null;
  queryForm.type = null;
  queryForm.displayStatus = 4;
  queryForm.startTime = null;
  queryForm.endTime = null;
  getDynamic();
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  getDynamic();
}
</script>

<style lang="scss" scoped>
.dynamic {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
    .card-header {
      display: flex;
      align-items: center;

      .header-query {
        // height: 50px;

        .el-button {
          margin-left: 10px;
        }

        .query-form-inline {
          align-items: center;
          width: 100%;

          .el-input {
            width: 150px;
            height: 30px;
          }

          :deep(.header-select) {
            width: 150px;
            height: 30px;

            .el-input__wrapper {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }

    .dynamic-main {
      .el-image {
        width: 50px;
        height: 50px;
      }
    }

    .labels {
      margin-right: 5px;

      &:last-child {
        margin-right: 0;
      }
    }

    .dialog-video {
      width: 60%;
      height: 100%;
    }

    // :deep(.el-pagination__sizes) {
    //   width: 180px;
    // }

    :deep(.el-dialog) {
      width: 35%;
    }

    .el-form {
      width: 350px;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
