<template>
  <div class="face">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <!--            <el-form-item label="马甲包">-->
            <!--              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>-->
            <!--            </el-form-item>-->
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <!--                <el-option label="未提交" :value="0"/>-->
                <el-option label="认证审核中" :value="1"/>
                <el-option label="认证成功" :value="2"/>
                <el-option label="认证失败" :value="3"/>
                <!--                <el-option label="不允许再认证" :value="5"/>-->
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker v-model="queryForm.date" :clearable="false" type="daterange" unlink-panels
                              value-format="x"
                              format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :shortcuts="shortcuts" size="default" @change="timeChange"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="faceRef" :data="faceData" style="width: 100%; height: 100%">
          <el-table-column label="用户ID" width="120" fixed>
            <template #default="scope">{{ scope.row.userId }}</template>
          </el-table-column>
          <el-table-column label="头像" min-width="120">
            <template #default="scope">
              <div class="img-box">
                <el-image style="width: 70px; height: 70px" :src="scope.row.avatarUrl" :zoom-rate="1.2"
                          :preview-src-list="faceList" :initial-index="0" fite="cover" :preview-teleported="true"
                          :hide-on-click-modal="true" @click="fdimg(scope.row.avatarUrl)"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="昵称" min-width="90">
            <template #default="scope">{{ scope.row.nickName }}</template>
          </el-table-column>
          <el-table-column label="群名称" min-width="90">
            <template #default="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="群人数" min-width="90">
            <template #default="scope">{{ scope.row.number }}</template>
          </el-table-column>
          <el-table-column label="群介绍" min-width="140">
            <template #default="scope">{{ scope.row.labels }}</template>
          </el-table-column>
          <el-table-column label="入群需知" min-width="140">
            <template #default="scope">{{ scope.row.joinKnow }}</template>
          </el-table-column>
          <el-table-column label="入群条件" min-width="140">
            <template #default="scope">{{ scope.row.joinCondition }}</template>
          </el-table-column>
          <el-table-column label="群二维码" min-width="120">
            <template #default="scope">
              <div class="img-box">
                <el-image style="width: 70px; height: 70px" :src="scope.row.qrCode" :zoom-rate="1.2"
                          :preview-src-list="faceList" :initial-index="0" fite="cover" :preview-teleported="true"
                          :hide-on-click-modal="true" @click="fdimg(scope.row.qrCode)"/>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="群标签" min-width="140">
            <template #default="scope">{{ scope.row.labels }}</template>
          </el-table-column>
          <el-table-column label="群相册" min-width="150">
            <template #default="scope">
              <div style="display: flex;">
                <div class="img-box" v-for="(picture, index) in scope.row.pictures.split(',')" :key="index"
                     style="margin-right: 5px;">
                  <el-image style="width: 35px; height: 35px" :src="picture" :zoom-rate="1.2"
                            :preview-src-list="faceList" :initial-index="0" fit="cover" :preview-teleported="true"
                            :hide-on-click-modal="true" @click="fdimg(picture)"/>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="入群金币" min-width="140">
            <template #default="scope">{{ scope.row.joinGold }}</template>
          </el-table-column>

          <el-table-column label="认证状态" min-width="120">
            <template #default="scope">
              <span v-if="scope.row.status == '拒绝'" style="color: #c93838;">{{ scope.row.status }}</span>
              <span v-if="scope.row.status == '认证成功'" style="color: #548a42;">{{ scope.row.status }}</span>
              <span v-if="scope.row.status == '认证审核中'" style="color: #007bff;">{{ scope.row.status }}</span>
            </template>
          </el-table-column>
          <!--          <el-table-column label="描述" min-width="200">-->
          <!--            <template #default="scope">{{ scope.row.failDesc }}</template>-->
          <!--          </el-table-column>-->
          <el-table-column label="提交时间" min-width="200">
            <template #default="scope">
              <span v-show="scope.row.createTime">{{ getYMDHMS('-', ':', scope.row.submitTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作时间" min-width="200">
            <template #default="scope">
              <span v-show="scope.row.updateTime">{{ getYMDHMS('-', ':', scope.row.submitTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最后操作人" min-width="120">
            <template #default="scope">{{ scope.row.updateBy }}</template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <div v-if="scope.row.status !== 5">
                <el-button @click="faceEdit(scope.row, 2)" size="small" type="success"
                           v-if="scope.row.status !== '认证成功'">通过
                </el-button>
                <el-button @click="openModifyDialog(scope.row)" size="small" type="primary"
                           style="background-color: #06a200">修改
                </el-button>
                <!--                <el-button @click="openRateEditDialog(scope.row)" size="small" type="warning"-->
                <!--                           v-if="scope.row.status !== '拒绝' && scope.row.status !== '认证审核中'">提成比例-->
                <!--                </el-button>-->
                <el-button @click="faceEdit(scope.row, 3)" size="small" type="primary"
                           v-if="scope.row.status != '拒绝'">取消认证
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>

      <el-dialog v-model="isRateEditDialogOpen" title="修改提成比例" destroy-on-close @close="closeRateEditDialog"
                 width="35%">
        <el-form ref="rateEditFormRef" label-width="100px">
          <el-form-item prop="rate" label="提成比例">
            <el-input-number v-model="rateEditForm.rate" :min="0" :max="100" label="提成比例"/>
          </el-form-item>
        </el-form>
        <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeRateEditDialog">取消</el-button>
        <el-button type="primary" @click="handleRateEdit">确认</el-button>
      </span>
        </template>
      </el-dialog>
      <el-dialog v-model="isModifyDialogOpen" title="修改群信息" destroy-on-close @close="closeModifyDialog"
                 width="35%">
        <el-form ref="modifyFormRef" label-width="100px">
          <el-form-item prop="name" label="群名称">
            <el-input v-model="modifyForm.name"/>
          </el-form-item>
          <el-form-item label="二维码">
            <CosUpload
                v-for="(imageUrl, index) in modifyForm.qrCode.split(',')"
                :key="index"
                :imageUrl="imageUrl"
                @handleImagUrl="newUrl => changeGroupewm(newUrl, index)"
            />
          </el-form-item>
          <el-form-item prop="joinKnow" label="入群需知">
            <el-input type="textarea" v-model="modifyForm.joinKnow"/>
          </el-form-item>
          <el-form-item prop="joinCondition" label="入群条件">
            <el-input v-model="modifyForm.joinCondition"/>
          </el-form-item>
          <el-form-item prop="joinGold" label="入群金币">
            <el-input v-model="modifyForm.joinGold"/>
          </el-form-item>
          <el-form-item prop="labels" label="标签">
            <el-input v-model="modifyForm.labels"/>
          </el-form-item>
          <!--          <el-form-item label="群相册">-->
          <!--            <CosUpload-->
          <!--                v-for="(imageUrl, index) in modifyForm.pictures.split(',')"-->
          <!--                :key="index"-->
          <!--                :imageUrl="imageUrl"-->
          <!--                @handleImagUrl="newUrl => changeGroupImage(newUrl, index)"-->
          <!--            />-->
          <!--          </el-form-item>-->
          <!-- Add other form items here -->
        </el-form>
        <template #footer>
    <span class="dialog-footer">
      <el-button @click="closeModifyDialog">Cancel</el-button>
      <el-button type="primary" @click="handleModify">Confirm</el-button>
    </span>
        </template>
      </el-dialog>
      <el-dialog v-model="isShow" title="理由" destroy-on-close @close="close" width="35%">
        <el-form ref="addFormRef" label-width="100px">
          <el-form-item prop="title" label="理由">
            <el-select v-model="failDesc">
              <el-option value="广告" label="广告"></el-option>
              <el-option value="头像与真人照片不符" label="头像与真人照片不符"></el-option>
              <el-option value="AI合成图片" label="AI合成图片"></el-option>
              <el-option value="其它" label="其它"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="handleAudit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import pageHook from "@/hooks/pageHook";
// import { Edit } from "@element-plus/icons-vue";
import {getYMDHMS} from "@/utils/date"
import {getMatchmakerGroupList, examineMatchmakerGroup, modifyMatchmakerGroup, updateRate} from "@/api/authentication";
import CosUpload from '@/components/CosUploadnew.vue'

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(querySubmit);

let modifyForm = reactive<any>({
  id: null,
  name: null,
  qrCode: null,
  joinKnow: null,
  joinCondition: null,
  joinGold: null,
  pictures: null,
  labels: null,
  number: null,
  groupImages: [],
});

let isModifyDialogOpen = ref<boolean>(false);

const openModifyDialog = (row: any) => {
  modifyForm.id = row.id;
  modifyForm.name = row.name;
  modifyForm.qrCode = row.qrCode;
  modifyForm.joinKnow = row.joinKnow;
  modifyForm.joinCondition = row.joinCondition;
  modifyForm.joinGold = row.joinGold;
  modifyForm.pictures = row.pictures;
  modifyForm.labels = row.labels;
  isModifyDialogOpen.value = true;
};
// const changeGroupImage = (newUrl: string, index: number) => {
//   let picturesArray = modifyForm.pictures.split(',');
//   picturesArray[index] = newUrl;
//   modifyForm.pictures = picturesArray.join(',');
// };
const changeGroupewm = (newUrl: string, index: number) => {
  let picturesArray = modifyForm.qrCode.split(',');
  picturesArray[index] = newUrl;
  modifyForm.qrCode = picturesArray.join(',');
};

let faceData = ref([]); // 表格数据
let faceList = ref<any>([]);
let opNull = ref<any>(null);
let failDesc = ref('广告')
let isShow = ref<boolean>(false);
const queryForm = reactive<any>({
  userId: null,
  date: [],
  status: null,
  startTime: null,
  endTime: null,
  applicationId: 'com.dongxin.app'
});
let isRateEditDialogOpen = ref<boolean>(false);
let rateEditForm = reactive<any>({
  id: null,
  rate: null,
});

// const openRateEditDialog = (row: any) => {
//   rateEditForm.id = row.id;
//   rateEditForm.rate = row.rate;
//   isRateEditDialogOpen.value = true;
// };

const closeRateEditDialog = () => {
  isRateEditDialogOpen.value = false;
};
const handleModify = async () => {
  await modifyMatchmakerGroup(modifyForm);
  querySubmit();
  closeModifyDialog();
};

const closeModifyDialog = () => {
  isModifyDialogOpen.value = false;
};
const handleRateEdit = async () => {
  await updateRate(rateEditForm);
  querySubmit();
  closeRateEditDialog();
};
let userId = ref<number>();
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 请求列表数据
async function getfaceList() {
  let res = await getMatchmakerGroupList({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId: queryForm.applicationId,
    status: queryForm.status
  });

  let {data, total} = res.data;
  data.forEach((v: any) => {
    if (v.status == 0) {
      v.status = "未提交";
    } else if (v.status == 1) {
      v.status = "认证审核中";
    } else if (v.status == 2) {
      v.status = "认证成功";
    } else if (v.status == 3) {
      v.status = "拒绝";
    } else if (v.status == 5) {
      v.status = "不允许再认证";
      return;
    } else {
      v.status = "认证失败";
    }
  });
  faceData.value = data;
  totalNum = total;
}

getfaceList();
// 编辑
const faceEdit = async (row: any, status: any) => {
  if (status === 3) {
    isShow.value = true;
    userId.value = row.id;
    return;
  }
  await examineMatchmakerGroup({id: row.id, status});
  querySubmit();
};

const fdimg = (img: any) => {
  faceList.value = [img];
};

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function querySubmit() {
  let {userId, status, startTime, endTime, applicationId} = queryForm;
  let res = await getMatchmakerGroupList({
    userId,
    status,
    startTime,
    endTime,
    applicationId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let {data, total} = res.data;
  data.forEach((v: any) => {
    if (v.status == 0) {
      v.status = "未提交";
    } else if (v.status == 1) {
      v.status = "认证审核中";
    } else if (v.status == 2) {
      v.status = "认证成功";
    } else if (v.status == 3) {
      v.status = "拒绝";
    } else if (v.status == 5) {
      v.status = "不允许再认证";
      return;
    } else {
      v.status = "认证失败";
    }
  });
  faceData.value = data;
  totalNum = total;
};
const onreset = () => {
  queryForm.userId = null;
  queryForm.date = [];
  queryForm.status = 1;
  getfaceList();
};

const handleAudit = async () => {
  await examineMatchmakerGroup({id: userId.value, status: 3, failDesc: failDesc.value})
  querySubmit()
  close();

}

const close = () => {
  isShow.value = false;
}
// const changeSelect = (e: string) => {
//   queryForm.applicationId = e;
//   querySubmit();
// }
</script>

<style scoped lang="scss">
.face {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .card-header {
      display: flex;
      align-items: center;
      width: 100%;

      .el-form {
        width: 100%;
      }
    }

    .none {
      display: none;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .image-slot {
      font-size: 30px;
    }

    .image-slot .el-icon {
      font-size: 30px;
    }

    .el-image {
      width: 100%;
    }

    :deep(.el-dialog) {
      width: 35%;
    }

    :deep(.dia-ipt) {
      width: 215px;
    }
  }
}
</style>
