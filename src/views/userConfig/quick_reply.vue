<template>
  <div class="quick_reply">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm"   class="demo-form-inline">
            <el-form-item>
              <el-button :icon="Plus" @click="addReqlylist">增加</el-button>
            </el-form-item>
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="快捷语ID" prop="text">
              <el-input v-model="queryForm.id" />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query_reqly">查询</el-button>
              <el-button @click="onreset()">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="reqlyListTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" width="80" fixed></el-table-column>
        <el-table-column prop="applicationId" label="马甲包" width="80" fixed></el-table-column>
        <el-table-column prop="text" label="快捷语" > 
          <template #default="scope">
          <span v-if="!scope.row.isShow">{{ scope.row.text }}</span>
            <el-input v-else v-model="scope.row.text" />
          </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-switch v-model="scope.row.status" @change="(e)=>handleSwitchChange(e,scope.row.id)" />
          </template>
          </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editreqly(scope.row)" size="small" :type="scope.row.isShow ? 'success' : 'primary'"
              :icon="Edit">{{ scope.row.isShow ? "完成" : "编辑" }}</el-button>
            <el-popconfirm title="你确定删除吗?" @confirm="delreqly(scope.row)">
              <template #reference>
                <el-button type="danger" :icon="Delete" size="small"></el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog v-model="addShow" title="添加快捷语" width="300">
        <el-form ref="addFormRef" :model="addForm" label-width="100px">
          <el-form-item label="添加快捷语" v-for="(item,index) in addForm" :key="index" >
            <el-form-item label="马甲包" style="margin-bottom: 20px;">   
            <majaSelect :applicationId="item.applicationId" @changeSelect="(e:string)=>changeSelect1(e,index)"/>
            </el-form-item>
            <el-form-item label="文本" style="margin-bottom: 20px;">
            <el-input type="textarea" v-model="item.text" autocomplete="off" style="width: 300px;" />
            </el-form-item>
            <el-form-item label="状态"><el-switch v-model="item.status" /></el-form-item>
            <el-icon style="margin-left: 20px; cursor: pointer; font-size: 25px;"  @click="appendForm()" v-if="index+1===addForm.length*1"><CirclePlusFilled /></el-icon>
            <el-icon style="margin-left: 20px; cursor: pointer; font-size: 25px;"  @click="RemoveForm(index)" v-else><RemoveFilled  /></el-icon>
          </el-form-item>
        
        </el-form>
      
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="addShow = false">取消</el-button>
            <el-button type="primary" @click="addreqly">确认</el-button>
          </span>
        </template>

      </el-dialog>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import { Edit, Plus, Delete } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
import { query_reqly_api, modify_reqly_api, add_reqly_api, del_reqly_api } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(query_reqly);
const queryFormRef = ref<FormInstance>();
let reqlyListTab = ref([]);
let queryForm = reactive<any>({
 id:null,
 applicationId:'com.dongxin.app'
});
let addShow = ref(false)
let addForm = reactive<any>([
  {
    text:null,
    status:null,
    applicationId:'com.dongxin.app'
}
])

async function getreqlylist() {
  let res = await query_reqly_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId
  });
  let { data, total } = res.data;
  data.forEach((v: any) => {
    v.isShow = false;
    v.status? v.status=false: v.status=true;
  });
    reqlyListTab.value = data;
  totalNum.value = total;
}

const handleSwitchChange= async (e:any,id:number)=>{
  await modify_reqly_api({
      id: id,
      status: e?0:1,
    });

}
getreqlylist();

// 修改
const editreqly = async (row: any) => {
  row.isShow = !row.isShow;
  if (!row.isShow) {
    await modify_reqly_api({
      id: row.id,
      text: row.text,
    });
  }
};



// 增加
const addReqlylist = () => {
  addShow.value = true
}
const addreqly = async () => {
  console.log(addForm);
  
let list= [...addForm];
  let parmas= list.filter((item: { status: number; text:String})=>{
   item.status? item.status=0:item.status=1;
 if(item.text!=""&&item.text!=null){
  return item;
 }
  })

 
  await add_reqly_api({quickReplyList:parmas })
  query_reqly()
 let datas=reactive<any>([{
    text:null,
    status:false,
  }])
  addForm=datas
  addShow.value = false
}


//删除
const delreqly = async (row: any) => {
  await del_reqly_api({ id: row.id })
  query_reqly()
}

// 查询
async function query_reqly() {
  let res = await query_reqly_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  data.forEach((v: any) => {
    v.isShow = false;
    v.status? v.status=false: v.status=true;
  });
  reqlyListTab.value = data;
  totalNum.value = total;
}

const appendForm = ()=>{
let parmas={
  text:null,
  status:null
}
addForm.push(parmas);
}

const RemoveForm =(index:Number)=>{
  addForm.splice(index,1);

}

const onreset = () => {
queryForm.id=null;
  query_reqly();
};

const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query_reqly();
}

let changeSelect1=(e:string,index: number)=>{
  addForm[index].applicationId=e
}

</script>

<style lang="scss" scoped>
.quick_reply {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }
  :deep(.el-dialog) {
    width: 35%;
    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }

  .el-pagination {
    margin-top: auto;
    margin-bottom: 0;
    background-color: #fff;
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
    z-index: 10;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
  }
}
</style>
