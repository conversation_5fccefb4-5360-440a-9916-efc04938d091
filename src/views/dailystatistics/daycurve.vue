<template>
  <div class="daycurve">
    <el-card class="box-card">
      <template #header>
        <majaSelect :applicationId="applicationId" @changeSelect="changeSelect"/>
      </template>
      <div class="chart-container">
        <div id="dayEchar" style="width: 100%; height: 100%"></div>
      </div>

      <div class="tabBtn">
        <el-button v-for="(v, i) in tabList" :key="i" @click="onTab(i)" :color="active == v.id ? '#8242D0' : ''">{{ v.text
        }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ECharts, EChartsOption, init } from "echarts";
import { queryDay_api } from "@/api/statistics";
import { getYMD } from "@/utils/date"

let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
// let past7daysStart = toData - 7 * 3600 * 24 * 1000;
// //最近30天
let past30daysStart = toData - 30 * 3600 * 24 * 1000
let active = ref(0);
let  applicationId=ref('com.dongxin.app')
const tabList = ref([
  {
    id: 0,
    type: [0, 3],
    title: ["充值成功", "充值未付款"],
    text: "充值",
  }, {
    id: 1,
    type: [1, 2],
    title: ["申请提现", "提现打款"],
    text: "提现",
  }, {
    id: 2,
    type: [4],
    title: ["当日最高在线人数"],
    text: "用户在线",
  },{
    id: 3,
    type: [17,18],
    title: ["男生在线人数", "女生在线人数"],
    text: "昨日注册用户在线",
  },{
    id: 4,
    type: [19,20],
    title: ["男生在线人数", "女生在线人数"],
    text: "昨日注册付费用户在线",
  },
  {
    id: 5,
    type: [5],
    title: [],
    text: "渠道注册统计",
  }, {
    id: 6,
    type: [6],
    title: [],
    text: "渠道注册用户充值统计",
  }, {
    id: 7,
    type: [7],
    title: [],
    text: "渠道人均充值",
  }, {
    id: 8,
    type: [8],
    title: [],
    text: "渠道注册用户充值人数",
  }, {
    id: 9,
    type: [9],
    title: [],
    text: "渠道付费人均付费",
  }, {
    id: 10,
    type: [10],
    title: [],
    text: "渠道付费率",
  }, {
    id: 11,
    type: [11],
    title: ["注册统计"],
    text: "注册统计",
  }, {
    id: 12,
    type: [12],
    title: ["注册用户充值统计"],
    text: "注册用户充值统计",
  }, {
    id: 13,
    type: [13],
    title: ["人均充值"],
    text: "人均充值",
  }, {
    id: 14,
    type: [14],
    title: ["注册用户充值人数"],
    text: "注册用户充值人数",
  },
  {
    id: 15,
    type: [101,104,105,106,107,108,109,112,114,115,116,117,118,120,121,122,123,124,125,126,127],
    title: ['金币充值','签到获得','新人任务赠送完善基本资料奖励金币','新人任务赠送完善更多资料奖励金币'
    ,"新人任务赠送上传真人头像奖励金币","新人任务赠送上传真人头像奖励金币",
    "新人任务赠送完善资料奖励金币","管理员赠送",'金币退回','礼品退回','新人任务赠送','日常任务赠送'
    ,'充值赠送免费币','注册赠送免费币','更新最新版本赠送金币','新用户充值活动获得金币','新用户充值活动赠送金币','新用户半屏充值礼包','新用户半屏充值赠送金币','新人任务赠送','解锁亲密度礼包'],
    text: "金币产出",
  },
  {
    id: 16,
    type: [102,103,110,111,113],
    title: ['聊天消耗金币','送礼物','语音通话消耗','视频通话消耗','管理员扣除'],
    text: "金币消耗",
  },
  {
    id: 17,
    type: [1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1015,1016,1018,1019,1020,1021],
    title: ['回复消息','视频通话','语音通话','邀请收益','新人任务赠送','礼品','签到','新人任务赠送完善基本资料奖励积分','新人任务赠送完善更多资料奖励积分',
    '新人任务赠送完善自我介绍奖励积分','新人任务赠送上传真人头像奖励积','新人任务赠送上传真人头像奖励积分','邀请收益邀请好友提现奖励积分','提现退回','后台赠送','新人任务赠送','日常任务赠送','推广人结算','回复緣分消息',''],
    text: "积分产出",
  },
  {
    id: 18,
    type: [1014,1017],
    title: ['提现扣除','后台扣除'],
    text: "积分消耗",
  },
]);
let time = ref<any>([]);
let types = ref([0, 3])
let cz = ref<any>([])
let datext = tabList.value[0].title
let arr0 = ref<any>([]);
let arr3 = ref<any>([]);
// 渠道
let nameArr = ref<any>([])
let qudaoList = [5, 6, 7, 8, 9, 10]
let qudaoId = [5, 6, 7, 8, 9, 10]
let xm = ref<any>([])
let hw = ref<any>([])
let viv = ref<any>([])
let opo = ref<any>([])
let kuaishou = ref<any>([])
let toutiao = ref<any>([])
let official = ref<any>([])
let xy = ref<any>([])
let xy1 = ref<any>([])
let honor = ref<any>([])
let share = ref<any>([])
let qilu01 = ref<any>([])
let yunlai01 = ref<any>([])
// let tql01 = ref<any>([])
let tql02 = ref<any>([])
let kql01 = ref<any>([])
let kql02 = ref<any>([])
let tyl01 = ref<any>([])
let tyl02 = ref<any>([])
let kyl01 = ref<any>([])
let kyl02 = ref<any>([])
let ttx01 = ref<any>([])
let ttx02 = ref<any>([])

interface ApiResponse {
  dayType: number;
  data: {
    statisticsData?: number;
    channelStatistics?: number;
    createTime: string;
    channelName: string;
    list?: {
      createTime: string;
      channelStatistics: number;
    }[];
  }[];
}

const getDaydata = async () => {
  let res = await queryDay_api({
    startTime: past30daysStart,
    endTime: todayEnd,
    applicationId: applicationId.value,
    types: types.value
  });
  let { data } = res.data as { data: ApiResponse[] }
  
  // 清空之前的数据
  time.value = []
  cz.value = []
  
  if (qudaoList.includes(types.value[0])) {
    // 处理渠道数据
    if (data?.[0]?.data) {
      // 获取所有渠道名称
      const channels = data[0].data.map(item => item.channelName)
      
      // 获取所有日期并排序
      const allDates = new Set<string>()
      data[0].data.forEach(channel => {
        channel.list?.forEach(item => {
          // 确保 getYMD 返回字符串
          allDates.add(getYMD("-", new Date(item.createTime).getTime()))
        })
      })
      time.value = [...allDates].sort()
      
      // 为每个渠道创建数据系列
      channels.forEach(channelName => {
        const channelData = data[0].data.find(d => d.channelName === channelName)
        if (channelData?.list) {
          const seriesData = time.value.map((date: string) => {
            const dataPoint = channelData.list?.find(item => 
              getYMD("-", new Date(item.createTime).getTime()) === date
            )
            return dataPoint ? dataPoint.channelStatistics : 0
          })
          
          cz.value.push({
            name: channelName,
            type: "line", 
            data: seriesData
          })
        }
      })
      
      datext = channels
    }
  } else if (types.value[0] === 4) {
    // 处理在线人数数据
    data.forEach(v => {
      v.data.forEach(item => {
        const date = getYMD("-", new Date(item.createTime).getTime())
        time.value.push(date)
        arr0.value.push(item.statisticsData)
      })
      time.value = [...new Set(time.value)].sort()
      cz.value = [{
        name: "当日最高在线人数",
        type: "line",
        data: arr0.value
      }]
    })
  } else {
    // 处理充值/提现数据
    data.forEach(v => {
      v.data.forEach(item => {
        const date = getYMD("-", new Date(item.createTime).getTime())
        time.value.push(date)
        if (v.dayType === types.value[0]) {
          arr0.value.push(item.statisticsData)
        }
        if (v.dayType === types.value[1]) {
          arr3.value.push(item.statisticsData) 
        }
      })
      time.value = [...new Set(time.value)].sort()
    })

    const name0 = types.value[0] === 1 ? '申请提现' : '充值成功'
    const name1 = types.value[1] === 2 ? '提现打款' : '充值未付款'
    
    cz.value = [
      { name: name0, type: "line", data: arr0.value },
      { name: name1, type: "line", data: arr3.value }
    ]
  }

  dayEchar(time.value, cz.value, datext)
}
const onTab = (i: any) => {
  arr0.value = []
  arr3.value = []
  time.value = []
  active.value = i;
  tabList.value.forEach((v: any) => {
    if (v.id == i) {
      types.value = v.type
      datext = v.title
      if (qudaoId.includes(v.id)) {
        official.value = []
        xm.value = []
        hw.value = []
        viv.value = []
        opo.value = []
        toutiao.value = []
        kuaishou.value = []
        xy.value = []
        honor.value = []
        share.value = []
        qilu01.value = []
        // tql01.value = []
        tql02.value = []
        kql01.value = []
        kql02.value = []
        tyl01.value = []
        tyl02.value = []
        kyl01.value = []
        kyl02.value = []
        ttx01.value = []
        ttx02.value = []
        xy1.value = []
        yunlai01.value = []
        v.title = nameArr.value
        datext = v.title
      }
      if(v.id ===3||v.id ===4||v.id ===15||v.id ===16||v.id ===17||v.id ===18) {
        getDaydata1(v.id );
        return;
      }
    
      getDaydata();
    }
  });
};
const getDaydata1 = async (n:number) => {
  let lines:any = reactive([])
  let res = await queryDay_api({
    startTime: past30daysStart,
    endTime: todayEnd,
    applicationId:applicationId.value,
    types: types.value
  })
  let { data } = res.data
  data.forEach((v: any,index:number) => {
    let params:any={
      name:tabList.value[n].title[index],
      type: "line",
      data: [], 
    };
    v.data.forEach((item: any) => {
      params.data.push(item.statisticsData)
        item.createTime = getYMD("-", item.createTime)
     if(!index){
         time.value.push(item.createTime)
     }
    })
    lines.push(params)
  })
  dayEchar(time.value, lines, datext);
}


const dayEchar = (day: any, datal: any, title: any) => {
  const myDaychars = document.getElementById("dayEchar") as any;
  myDaychars.removeAttribute("_echarts_instance_");
  const charEch: ECharts = init(myDaychars);
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: title,
      bottom: "2%"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: day,
      axisLabel: {
        //x轴文字的配置
        show: true,
        interval: 0,//使x轴文字显示全
        formatter: function (params) {
          let newParamsName = '';
          const paramsNameNumber = params.length; // 文字总长度
          const provideNumber = 5; //一行显示几个字
          const rowNumber = Math.ceil(paramsNameNumber / provideNumber);
          if (paramsNameNumber > provideNumber) {
            for (let p = 0; p < rowNumber; p++) {
              const start = p * provideNumber;
              const end = start + provideNumber;
              const tempStr = p === rowNumber - 1 ? params.substring(start, paramsNameNumber) : params.substring(start, end) + '\n';
              newParamsName += tempStr;
            }
          } else {
            newParamsName = params;
          }
          return newParamsName;
        }
      },

    },
    yAxis: {
      type: "value",
    },
    series: datal,
    opts: {
      width: "100%",
      height: "100%",
    },
  };
  window.addEventListener("resize", () => {
    charEch.resize();
  });
  charEch.setOption(option);
};
onMounted(() => {
  getDaydata();
});
const changeSelect=(e:string)=>{
  arr0.value = []
  arr3.value = []
  time.value = []
  applicationId.value=e;
  getDaydata();
}
</script>

<style lang="scss" scoped>
.daycurve {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .chart-container {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }

    .tabBtn {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;

      .el-button {
        margin-bottom: 12px;
        margin-right: 8px;
      }
    }
  }
}
</style>
