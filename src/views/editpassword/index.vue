<template>
  <div class="editpassword">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>个人中心</span>
          <el-button type="primary" @click="onEdit()">确定修改</el-button>
        </div>
      </template>
      <div class="edit-password">
        <p class="title">修改密码</p>
        <span class="span">新密码</span>
        <el-input v-model="password" placeholder="请输入新密码"></el-input>
      </div>
      <el-divider />
      <div class="edit-mobile">
        <p class="title">修改手机号</p>
        <span class="span">手机号</span>
        <el-input v-model="mobile" placeholder="请输入手机号"></el-input>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { modUserInfo_api } from "@/api/user"
import { useRouter } from "vue-router";
import userStore from "@/store/userStore";
import { ElMessage } from 'element-plus'
let { userid, clear_store } = userStore()
let password = ref("")
let mobile = ref("")
const $router = useRouter()


const onEdit = async () => {
  if (password.value.trim() != "" || mobile.value.trim() != "") {
    await modUserInfo_api({ id: userid, password: password.value, mobile: mobile.value })

    setTimeout(() => {
      ElMessage({
        message: '修改成功，请重新登录',
        type: 'success',
      })
      clear_store()
      $router.push("/login")
    }, 500)

  }


}

</script>

<style lang="scss" scoped>
.editpassword {
  height: 100%;
  width: 100%;

  .el-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .edit-password,
    .edit-mobile {
      .title {
        padding-bottom: 26px;
      }

      .span {
        margin-right: 10px;
      }

      .el-input {
        width: 300px;
        margin-right: 50px;
      }
    }

  }
}
</style>