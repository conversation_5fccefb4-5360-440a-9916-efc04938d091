<template>
  <div class="hourcurve">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query-time">
            <majaSelect :applicationId="applicationId" @changeSelect="changeSelect"/>
            <el-date-picker v-model="dateHour" type="datetimerange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" :clearable="false" @change="timeChange" />
          </div>
          <el-button type="primary" @click="queryHourcurve">查询</el-button>
          <el-button @click="queryReset">重置</el-button>
        </div>
      </template>
      <div class="chart-container">
        <div id="hourEchar" style="width: 100%; height: 100%"></div>
      </div>

      <div class="tabBtn">
        <el-button v-for="(v) in tabList" :key="v.id" @click="onTab(v.id)" :color="active == v.id ? '#8242D0' : ''">{{
          v.text }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ECharts, EChartsOption, init } from "echarts";
import { queryHour_api } from "@/api/statistics";
import { getHMS } from "@/utils/date"
import { selectionSort } from "@/utils/sort"
let toData = new Date(new Date().toLocaleDateString()).getTime()
//今天
let todayStart = toData //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1 //结束时间
// 当前
// let newDate = new Date().getTime()
// 前2小时
// let frontOneHour = new Date().getTime() - 2 * 60 * 60 * 1000;
let dateHour = ref<any>()
  let  applicationId=ref('com.dongxin.app')
let active = ref(0)
const tabList = ref<any>([
  {
    id: 0,
    type: [0, 3],
    title: ["充值成功", "充值未付款"],
    text: "充值",
  }, {
    id: 1,
    type: [1, 2],
    title: ["申请提现", "提现打款"],
    text: "提现",
  }, {
    id: 2,
    type: [4],
    title: ["男生在线人数", "女生在线人数"],
    text: "用户在线",
  }, {
    id: 3,
    type: [11],
    title: ["男生在线人数", "女生在线人数"],
    text: "昨日注册用户在线",
  },{
    id: 4,
    type: [12],
    title: ["男生在线人数", "女生在线人数"],
    text: "昨日注册付费用户在线",
  }, {
    id: 5,
    type: [5],
    title: [],
    text: "渠道注册统计",
  }, {
    id: 6,
    type: [6],
    title: [],
    text: "渠道注册用户充值统计",
  }, {
    id: 7,
    type: [7],
    title: [],
    text: "渠道人均充值",
  }, {
    id: 8,
    type: [8],
    title: [],
    text: "注册用户充值人数",
  }, {
    id: 9,
    type: [9],
    title: [],
    text: "付费人均付费",
  }, {
    id: 10,
    type: [10],
    title: [],
    text: "渠道付费率",
  },
  {
    id: 11,
    type: [101,104,105,106,107,108,109,112,114,115,116,117,118,120,121,122,123,124,125,126,127],
    title: ['金币充值','签到获得','新人任务赠送完善基本资料奖励金币','新人任务赠送完善更多资料奖励金币'
    ,"新人任务赠送上传真人头像奖励金币","新人任务赠送上传真人头像奖励金币",
    "新人任务赠送完善资料奖励金币","管理员赠送",'金币退回','礼品退回','新人任务赠送','日常任务赠送'
    ,'充值赠送免费币','注册赠送免费币','更新最新版本赠送金币','新用户充值活动获得金币','新用户充值活动赠送金币','新用户半屏充值礼包','新用户半屏充值赠送金币','新人任务赠送','解锁亲密度礼包'],
    text: "金币产出",
  },
  {
    id: 12,
    type: [102,103,110,111,113],
    title: ['聊天消耗金币','送礼物','语音通话消耗','视频通话消耗','管理员扣除'],
    text: "金币消耗",
  },
  {
    id: 13,
    type: [1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1015,1016,1018,1019,1020,1021],
    title: ['回复消息','视频通话','语音通话','邀请收益','新人任务赠送','礼品','签到','新人任务赠送完善基本资料奖励积分','新人任务赠送完善更多资料奖励积分',
    '新人任务赠送完善自我介绍奖励积分','新人任务赠送上传真人头像奖励积','新人任务赠送上传真人头像奖励积分','邀请收益邀请好友提现奖励积分','提现退回','后台赠送','新人任务赠送','日常任务赠送','推广人结算','回复緣分消息'],
    text: "积分产出",
  },
  {
    id: 14,
    type: [1014,1017],
    title: ['提现扣除','后台扣除'],
    text: "积分消耗",
  },
]);

let startDate = todayStart
let endDate = todayEnd
let time = ref<any>([]);
let timeclone = ref<any>([]);
let types = ref([0, 3])
let cz = ref<any>([])
let datext = tabList.value[0].title
let arr0 = ref<any>([]);
let arr3 = ref<any>([]);
// 渠道
// let qudaoTime = ref<any>([])
let nameArr = ref<any>([])
let qudaoList = [5, 6, 7, 8, 9, 10]
let qudaoId = [5, 6, 7, 8, 9, 10]
let xm = ref<any>([])
let hw = ref<any>([])
let viv = ref<any>([])
let opo = ref<any>([])
let kuaishou = ref<any>([])
let toutiao = ref<any>([])
let official = ref<any>([])
let sharetrace = ref<any>([])
let tql01 = ref<any>([])
let tql02 = ref<any>([])
let kql01 = ref<any>([])
let kql02 = ref<any>([])
let tyl01 = ref<any>([])
let tyl02 = ref<any>([])
let kyl01 = ref<any>([])
let kyl02 = ref<any>([])
let ttx01 = ref<any>([])
let ttx02 = ref<any>([])

// 删除所有未使用的 line0 到 line17 变量
// let line0 = reactive<any>({})
// let line1 = reactive<any>({})
// ... 
// let line17 = reactive<any>({})

const gethourdata = async (startDate: any, endDate: any) => {
  let res = await queryHour_api({
    startTime: startDate,
    endTime: endDate,
    types: types.value,
    applicationId:applicationId.value,
  })
  let { data } = res.data
  let lines: any[] = []
  time.value = []
  timeclone.value = []
  const setArr = new Set(time.value)

  data.forEach((v: any) => {
    if (qudaoList.includes(v.hourType)) {
      // 处理渠道数据
      v.data.forEach((channel: any) => {
        const channelData = {
          name: channel.channelName,
          type: "line",
          data: new Array(24).fill(0)
        }
        
        channel.list.forEach((hourData: any) => {
          channelData.data[hourData.hour] = hourData.channelStatistics
          if (!timeclone.value.includes(hourData.hour)) {
            timeclone.value.push(hourData.hour)
          }
        })
        
        lines.push(channelData)
      })

      for (let i = 0; i < 24; i++) {
        time.value.push(`${i}时`)
      }
      
      cz.value = lines
    } else {
      v.data.forEach((item: any) => {
        item.statisticsTime = getHMS(":", item.statisticsTime)
        item.hour = item.hour + "时"
        if (v.hourType == 4||v.hourType === 11||v.hourType === 12) {
          time.value.push(item.statisticsTime)
          arr0.value.push(item.manOnlineAmount)
          arr3.value.push(item.womanOnlineAmount)
          // line0 = { name: "男生在线人数", type: "line", data: arr0 }
          // line3 = { name: "女生在线人数", type: "line", data: arr3 }
        } else if (qudaoList.includes(v.hourType)) {
          console.log(item);
          nameArr.value.push(item.channelName)
          item.list.forEach((ele: any) => {
            timeclone.value.push(ele.hour)
            timeclone.value = [...new Set(timeclone.value)]
            selectionSort(timeclone.value)
            
            if (ele.channelName == 'official') {
              official.value.push(ele.channelStatistics)
              // line0 = { name: "official", type: "line", data: official.value }
            }
            if (item.channelName == '小米') {
              xm.value.push(ele.channelStatistics)
              // line1 = { name: "小米", type: "line", data: xm.value }
            }
            if (item.channelName == 'huawei') {
              hw.value.push(ele.channelStatistics)
              // line3 = { name: "huawei", type: "line", data: hw.value }
            }
            if (item.channelName == 'vivo') {
              viv.value.push(ele.channelStatistics)
              // line2 = { name: "vivo", type: "line", data: viv.value }
            }
            if (item.channelName == 'oppo') {
              opo.value.push(ele.channelStatistics)
              // line4 = { name: "oppo", type: "line", data: opo.value }
            }
            if (item.channelName == '头条') {
              toutiao.value.push(ele.channelStatistics)
              // line5 = { name: "头条", type: "line", data: toutiao.value }
            }
            if (item.channelName == '快手') {
              kuaishou.value.push(ele.channelStatistics)
              // line6 = { name: "快手", type: "line", data: kuaishou.value }
            }
            if (item.channelName == '头条_七录_七录01') {
              tql01.value.push(ele.channelStatistics)
              // line7 = { name: "头条_七录_七录01", type: "line", data: tql01.value }
            }
            if (item.channelName == '头条_七录_七录02') {
              tql02.value.push(ele.channelStatistics)
              // line8 = { name: "头条_七录_七录02", type: "line", data: tql02.value }
            }
            if (item.channelName == '快手_七录_七录01') {
              kql01.value.push(ele.channelStatistics)
              // line9 = { name: "快手_七录_七录01", type: "line", data: kql01.value }
            }
            if (item.channelName == '快手_七录_七录02') {
              kql02.value.push(ele.channelStatistics)
              // line10 = { name: "快手_七录_七录02", type: "line", data: kql02.value }
            }
            if (item.channelName == '头条_韵莱_韵莱01') {
              tyl01.value.push(ele.channelStatistics)
              // line11 = { name: "头条_韵莱_韵莱01", type: "line", data: tyl01.value }
            }
            if (item.channelName == '头条_韵莱_韵莱02') {
              tyl02.value.push(ele.channelStatistics)
              // line12 = { name: "头条_韵莱_韵莱02", type: "line", data: tyl02.value }
            }
            if (item.channelName == '快手_韵来_韵莱01') {
              kyl01.value.push(ele.channelStatistics)
              // line13 = { name: "快手_韵来_韵莱01", type: "line", data: kyl01.value }
            }
            if (item.channelName == '快手_韵来_韵莱02') {
              kyl02.value.push(ele.channelStatistics)
              // line14 = { name: "快手_韵来_韵莱02", type: "line", data: kyl02.value }
            }
            if (item.channelName == '头条_田信_田信01') {
              ttx01.value.push(ele.channelStatistics)
              // line15 = { name: "头条_田信_田信01", type: "line", data: ttx01.value }
            }
            if (item.channelName == '头条_田信_田信02') {
              ttx02.value.push(ele.channelStatistics)
              // line16 = { name: "头条_田信_田信02", type: "line", data: ttx02.value }
            }
            if (item.channelName == '分享人渠道') {
              sharetrace.value.push(ele.channelStatistics)
              // line17 = { name: "分享人渠道", type: "line", data: sharetrace.value }
            }
          })
        } else if (v.hourType == types.value[0]) {
          time.value.push(item.hour)
          time.value = [...setArr]
          arr0.value.push(item.statisticsData)
          // line0 = { name: name, type: "line", data: arr0 }
        }
        if (v.hourType == types.value[1]) {
          time.value.push(item.hour)
          arr3.value.push(item.statisticsData)
          // line3 = { name: name, type: "line", data: arr3 }
          // line3 = { name: name, type: "line", data: arr3 }
          return;
        }
      })
    }
  })
  if (qudaoList.includes(types.value[0])) {
    cz.value = lines
    timeclone.value.forEach((v:any) => time.value.push(`${v}时`))
  } else {
    cz.value = [
      { name: "男生在线人数", type: "line", data: arr0.value },
      { name: "女生在线人数", type: "line", data: arr3.value }
    ]
  }
  hourEchar(time.value, cz.value, datext);
}

const onTab = (i: any) => {
  arr0.value = []
  arr3.value = []
  time.value = []
  active.value = i;
  tabList.value.forEach((v: any) => {
    if (v.id == i) {
      types.value = v.type
      datext = v.title
      if (qudaoId.includes(v.id)) {
        official.value = []
        xm.value = []
        hw.value = []
        viv.value = []
        opo.value = []
        toutiao.value = []
        kuaishou.value = []
        sharetrace.value = []
        tql01.value = []
        tql02.value = []
        kql01.value = []
        kql02.value = []
        tyl01.value = []
        tyl02.value = []
        kyl01.value = []
        kyl02.value = []
        ttx01.value = []
        ttx02.value = []
        v.title = nameArr.value
        datext = v.title
      }

      if(v.id ===13||v.id ===12||v.id ===11) {
        gethourdata1(startDate, endDate,v.id );
        return;
      }
      gethourdata(startDate, endDate);
    }
  });
};
const gethourdata1 = async (startDate: any, endDate: any,n:number) => {
  let lines:any = reactive([])
  let res = await queryHour_api({
    startTime: startDate,
    endTime: endDate,
    types: types.value,
    applicationId:applicationId.value
  })
  let { data } = res.data
  
  data.forEach((v: any,index:number) => {
    let params:any={
      name:tabList.value[n].title[index],
      type: "line",
      data: [], 
    };
    v.data.forEach((item: any) => {
      params.data.push(item.statisticsData)
      item.statisticsTime = getHMS(":", item.statisticsTime)
      item.hour = item.hour + "时"
     if(!index){
      time.value.push( item.hour)
     }
    })
    lines.push(params)
  })
 hourEchar(time.value, lines, datext);
}
// 时间选择查询
const timeChange = (value: any) => {
  startDate = Math.round(value[0])
  endDate = Math.round(value[1])
}

const queryHourcurve = () => {

  arr0.value = []
  arr3.value = []
  time.value = []
  gethourdata(startDate, endDate)
}

const queryReset = () => {
  arr0.value = []
  arr3.value = []
  time.value = []
  dateHour.value = ''
  startDate = todayStart
  endDate = todayEnd
  gethourdata(startDate, endDate)
}

//echars
const hourEchar = (timer: any, datal: any, title: any) => {
  const myhourchars = document.getElementById("hourEchar") as any;
  myhourchars.removeAttribute("_echarts_instance_");
  const charEch: ECharts = init(myhourchars);
  const option: EChartsOption = {
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: title,
      bottom: "2%"
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "10%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: timer,
      axisLabel: {
        //x轴文字的配置
        show: true,
        interval: 0,//使x轴文字显示全
      },
    },
    yAxis: {
      type: "value",
    },
    series: datal,
    opts: {
      width: "100%",
      height: "100%",
    },
  };
  window.addEventListener("resize", () => {
    charEch.resize();
  });
  charEch.setOption(option);
};
onMounted(() => {
  gethourdata(startDate, endDate)
})
const changeSelect=(e:string)=>{
  applicationId.value=e;
  arr0.value = []
  arr3.value = []
  time.value = []
  queryHourcurve();
}
</script>

<style lang="scss" scoped>
.hourcurve {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .chart-container {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }

    .tabBtn {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;

      .el-button {
        margin-bottom: 12px;
        margin-right: 8px;
      }

      .yonghu {
        position: absolute;
        top: 0;
        left: 0;
      }
    }

    .card-header {
      display: flex;
      align-items: center;

      .query-time {
        margin-right: 16px;
      }
    }
  }
}
</style>