<template>
  <div class="role">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button @click="addRole" :icon="Plus">添加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="roleRef" :data="roleData" style="width: 100%; height: 100%">
        <el-table-column label="ID" width="120">
          <template #default="scope">{{ scope.row.id }}</template>
        </el-table-column>
        <el-table-column label="角色名">
          <template #default="scope">{{ scope.row.roleName }}</template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="300">
          <template #default="scope">
            <el-button @click="userAndRole(scope.row)" size="small" :icon="Edit">权限角色绑定修改</el-button>
            <el-button @click="editRole(scope.row)" size="small" type="primary" :icon="Edit" />
            <el-popconfirm title="你确定删除吗?" @confirm="delRole(scope.row)">
              <template #reference>
                <el-button type="danger" size="small" :icon="Delete" />
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
      <el-dialog v-model="isShow" title="编辑角色">
        <el-form :model="diaData">
          <el-form-item label="ID" label-width="60">
            <el-input class="dia-ipt" v-model="diaData.id" autocomplete="off" disabled />
          </el-form-item>
          <el-form-item label="角色名" label-width="60">
            <el-select class="dia-ipt" v-model="diaData.roleName" placeholder="请选择角色">
              <el-option label="admin" value="admin" />
              <el-option label="tsets" value="tsets" />
              <el-option label="super" value="super" />
              <el-option label="ordinary" value="ordinary" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isShow = false">取消</el-button>
            <el-button type="primary" @click="subMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog v-model="addShow" title="添加角色">
        <span class="add-name">角色名</span>
        <el-input class="dia-ipt" v-model="addDia" autocomplete="off" />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="addShow = false">取消</el-button>
            <el-button type="primary" @click="addSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 抽屉 -->
      <el-drawer v-model="drawer" title="权限跟角色绑定修改" direction="rtl" :before-close="handleClose">
        <div class="tree-list">
          <el-tree ref="menuTree" :default-checked-keys="defaultCheckId" :data="roleMenuData" show-checkbox node-key="id"
            :props="defaultProps" @check="handleCheckChange" />
        </div>
        <div class="drawer-btn">
          <el-button type="success" @click="CloseSueccs">
            <el-icon class="el-icon--left">
              <SuccessFilled />
            </el-icon>
            确定
          </el-button>
        </div>
        <template #footer>
          <div class="tree-pagination">
            <el-pagination class="tree-size" v-model:current-page="currentPageTree" v-model:page-size="pageSizeTree"
              :page-sizes="[50]" :small="true" layout="total, sizes, prev, pager, next, jumper" :background="true"
              :total="totalNumTree" @size-change="SizeChange" @current-change="CurrentChange" />
          </div>
        </template>
      </el-drawer>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import pageHook from "@/hooks/pageHook";
import { Delete, Edit, Plus, SuccessFilled } from "@element-plus/icons-vue";
import { ElDrawer, ElMessageBox } from "element-plus";
import {
  addRoler_api,
  delRoler_api,
  modifyRoler_api,
  queryRoler_api,
  modifyResource_api,
  queryResource_api,
  resource_api,
} from "@/api/user";

let roleData = ref([]); // 表格数据
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(getroleList);
let diaData = ref(); //表格弹窗回填的数据
let isShow = ref<boolean>(false); //弹窗是否打开
let addShow = ref<boolean>(false);
let addDia = ref("");
// 抽屉
let currentPageTree = ref<number>(1);
let pageSizeTree = ref<number>(50);
let totalNumTree = ref<any>(null);
let menuTree = ref<any>(null);
const drawer = ref(false);
let roleMenuid = ref<any>();
let roleMenuList = ref([]); // 权限跟角色绑定数据
let roleMenuArr = ref<any>(); //权限跟角色绑定数组
let roleMenuData = ref([]);
let CloneArr: any = [];
let idsArr: any = []; //资源id
const defaultProps = {
  children: "children",
  label: "catalogueName",
};
let defaultCheckId = ref([]) //默认选中id
// 请求列表数据
async function getroleList() {
  let res = await queryRoler_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { total, data } = res.data;
  totalNum.value = total;
  roleData.value = data;

  // console.log(res);
}

getroleList();
 // 权限跟角色绑定查询
const queryResource = async () => {
  let res = await queryResource_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data } = res.data;
  roleMenuList.value = data;
  // console.log(res);
};
queryResource();

// 查询权限列表
const queryResourceList = async () => {
  let res = await resource_api({
    pageSize: pageSizeTree.value,
    pageNum: currentPageTree.value,
  });
  let { data, total } = res.data;
  totalNumTree.value = total;
  CloneArr = JSON.parse(JSON.stringify(data));
  let menuArr = CloneArr.map((item: any) => {
    let children = item.menuList.map((ele: any) => {
      return {
        id: ele.id,
        catalogueName: ele.name,
      };
    });
    return {
      id: item.id,
      catalogueName: item.name,
      children,
    };
  });
  // console.log(res);

  roleMenuData.value = menuArr;
};
queryResourceList();
// 分页 每条页数更改
const SizeChange = (val: number) => {
  pageSize.value = val;
  queryResourceList();
};
// 当前页码改变
const CurrentChange = (val: number) => {
  currentPage.value = val;
  queryResourceList();
};
// 编辑
const editRole = (row: role) => {
  diaData.value = { ...row };
  isShow.value = true;
};

const subMit = async () => {
  await modifyRoler_api(diaData.value);
  isShow.value = false;
  getroleList();
  addDia.value = "";
};

// 添加
const addRole = async () => {
  addDia.value = "";
  addShow.value = true;
};

const addSubMit = async () => {
  if (addDia.value.trim().length > 0) {
    await addRoler_api({ roleName: addDia.value });
      addShow.value = false;
      getroleList();

  }
};

// 删除
const delRole = async (row: any) => {
  if (row.id) await delRoler_api({ id: row.id });
  getroleList();
};
//编辑权限
const userAndRole = (row: any) => {
  roleMenuArr.value = []
  // console.log(roleMenuList.value);
  idsArr = [];
  roleMenuList.value.forEach((v: any) => {
    if (v.roleName == row.roleName) {
      let idArr1: any = []
      let idArr2: any = []
      v.catalogueList.forEach((item: any) => {
        idArr1.push(item.catalogueId)
        item.menuVOList.forEach((ele: any) => {
          idArr2.push(ele.menuId)
        })
      })
      roleMenuArr.value = idArr2
      defaultCheckId.value = roleMenuArr.value
      idsArr = [...idArr1, ...idArr2]
    }
  })
  roleMenuid.value = row.id;
  drawer.value = true;
};

const handleClose = (done: () => void) => {
  ElMessageBox.confirm("你确定要关闭并提交吗?")
    .then(async () => {
      done();
      await modifyResource_api({
        roleId: roleMenuid.value,
        resourceIds: idsArr,
      });
      menuTree.value.setCheckedKeys([]);
      queryResource();
    })
    .catch(() => {
      menuTree.value.setCheckedKeys([]);
      drawer.value = false;
    });
};

const CloseSueccs = () => {
  ElMessageBox.confirm("你确定要关闭并提交吗?")
    .then(async () => {
      await modifyResource_api({
        roleId: roleMenuid.value,
        resourceIds: idsArr,
      });
      menuTree.value.setCheckedKeys([]);
      queryResource();
      drawer.value = false;
    })
    .catch(() => {
      menuTree.value.setCheckedKeys([]);
      drawer.value = false;
    });
}

const handleCheckChange = (_data: any, checked: any) => {
  // console.log(data, checked);
  // console.log(checked.checkedKeys);
  // 选中数组和半选数组合并
  idsArr = checked.checkedKeys.concat(menuTree.value.getHalfCheckedKeys());
  // console.log(idsArr);
};
</script>

<style scoped lang="scss">
.role {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .card-header {
      display: flex;
      align-items: center;

      .el-button {
        margin-left: 10px;
      }
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .tree-pagination {
      padding-top: 20px;
    }

    .el-drawer {
      display: flex;
      flex-direction: column;

      .drawer-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 16px 0;

        .el-button {
          width: 40%;
        }
      }

      .tree-list {
        flex: 1;
      }
    }

    .img {
      width: 50px;
      height: 50px;
      border-radius: 50%;
    }

    :deep(.el-dialog) {
      width: 35%;

      .el-form {
        width: 100%;

        .el-select {
          width: 100%;
        }
      }

      .add-name {
        display: inline-block;
        width: 60px;
      }
    }
  }
}
</style>
