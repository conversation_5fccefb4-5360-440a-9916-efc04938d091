<template>
  <div class="backgrounduser">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="query-form-inline">
            <el-form-item>
              <el-button @click="open" :icon="Plus">添加</el-button>
            </el-form-item>
            <!-- <el-form-item label="ID">
              <el-input v-model="queryForm.quId" />
            </el-form-item> -->
            <el-form-item label="账号">
              <el-input v-model="queryForm.quName" />
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="queryForm.quMobile" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="userRef" :data="userData" style="width: 100%; height: 100%">
          <el-table-column label="用户ID" width="100">
            <template #default="scope">{{ scope.row.id }}</template>
          </el-table-column>
          <el-table-column label="账号" min-width="120">
            <template #default="scope">{{ scope.row.account }}</template>
          </el-table-column>
          <el-table-column label="名称" min-width="120">
            <template #default="scope">{{ scope.row.username }}</template>
          </el-table-column>
          <el-table-column label="手机号" min-width="200">
            <template #default="scope">{{ scope.row.mobile }}</template>
          </el-table-column>
          <el-table-column label="登录时间" min-width="200">
            <template #default="scope">{{
              getYMD("-", scope.row.createTime)
            }}</template>
          </el-table-column>
          <el-table-column label="创建时间" min-width="200">
            <template #default="scope">{{
              getYMD("-", scope.row.createTime)
            }}</template>
          </el-table-column>
          <el-table-column label="状态" min-width="120">
            <template #default="scope">{{
              scope.row.status == 0 ? "可用" : "不可用"
            }}</template>
          </el-table-column>
          <el-table-column label="角色权限名称" min-width="200" show-overflow-tooltip>
            <template #default="scope">
              <div class="ro_name">
                <span v-for="(v, i) in scope.row.roleName" :key="i">{{
                  v
                }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="scope">
              <el-button @click="editUser(scope.row)" size="small" type="primary" :icon="Edit" />
              <el-popconfirm title="你确定删除吗?" @confirm="deleUser(scope.row)">
                <template #reference>
                  <el-button type="danger" size="small" :icon="Delete" v-if="scope.row.account != 'admin'" />
                </template>
              </el-popconfirm>
              <el-button @click="editUserInfo(scope.row)" v-if="scope.row.account != 'admin'">编辑用户信息</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
      <el-dialog v-model="addShow" title="添加账号" class="add">
        <el-form ref="addFormRef" :model="addForm" status-icon class="demo-ruleForm" :rules="rules" label-width="100px">
          <el-form-item label="账号" prop="account">
            <el-input v-model="addForm.account" autocomplete="off"> </el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="addForm.password" autocomplete="off"> </el-input>
          </el-form-item>
          <el-form-item label="名称" prop="userName">
            <el-input v-model="addForm.userName" autocomplete="off"> </el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="addForm.mobile" autocomplete="off"> </el-input>
          </el-form-item>
          <el-form-item label="角色名" prop="roleIds">
            <el-select class="dia-ipt" multiple v-model="addForm.roleIds" placeholder="请选择角色">
              <el-option v-for="v in option" :key="v.id" :label="v.roleName" :value="v.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addUser(addFormRef)">添加</el-button>
            <el-button @click="addreset(addFormRef)">重置</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog v-model="editRoleShow" title="修改管理员的用户权限" class="editRole">
        <el-form ref="editRoleRef" :model="editRoleForm" status-icon class="demo-ruleForm" label-width="100px">
          <el-form-item label="角色名" prop="roleIds">
            <el-select class="dia-ipt" multiple v-model="editRoleForm.roleIds" placeholder="请选择角色">
              <el-option v-for="v in option" :key="v.id" :label="v.roleName" :value="v.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="账号" prop="account">
            <el-input v-model="editRoleForm.account" autocomplete="off" disabled>
            </el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editRoleShow = false">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog v-model="editInfoShow" title="修改后台用户信息" class="editRole">
        <el-form ref="editInfoRef" :model="editInfoForm" status-icon class="demo-ruleForm" label-width="100px">
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="editInfoForm.userName" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="账号" prop="account">
            <el-input v-model="editInfoForm.account" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="editInfoForm.password" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="editInfoForm.mobile" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="用户状态" prop="status">
            <el-select class="dia-ipt" v-model="editInfoForm.status" placeholder="请选择状态">
              <el-option label="可用" :value="0" />
              <el-option label="不可用" :value="1" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="editInfoShow = false">取消</el-button>
            <el-button type="primary" @click="subMitInfo">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import pageHook from "@/hooks/pageHook";
import {
  queryRoler_api,
  queryAminUser_api,
  addAminUser_api,
  modUserAuth_api,
  delAdminUser_api,
  modifyUser_api
} from "@/api/user";
import { Delete, Edit, Plus } from "@element-plus/icons-vue";
import type { FormInstance, FormRules } from "element-plus";
import { getYMD } from "@/utils/date";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(getqueryUser);
let userData = ref([]); //表格数据
let addShow = ref(false);
let editRoleShow = ref(false);
let editInfoShow = ref(false);
const queryForm = reactive({
  quMobile: null,
  quId: null,
  quName: null,
});

const addFormRef = ref<FormInstance>();
const addForm = reactive<userManage>({
  account: "",
  password: "",
  userName: "",
  mobile: "",
  roleIds: [],
});
let option = ref<any>([]);
const editRoleForm = reactive<userManage>({
  roleIds: [],
  account: "",
});

const editInfoForm = reactive<any>({
  account: null,
  password: null,
  userName: null,
  mobile: null,
  status: null,
  id:null
})

const rules = reactive<FormRules>({
  account: [{ required: true, message: "不能为空", trigger: "blur" }],
  password: [{ required: true, message: "不能为空", trigger: "blur" }],
  userName: [{ required: true, message: "不能为空", trigger: "blur" }],
  roleIds: [{ required: true, message: "不能为空", trigger: "blur" }],
});
// 获取数据
async function getqueryUser() {
  let res = await queryAminUser_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  userData.value = data;
  totalNum.value = total;
}
getqueryUser();
// 查询角色权限
const getRoleId = async () => {
  let res = await queryRoler_api({ pageNum: 1, pageSize: 50 });
  option.value = res.data.data;
};
getRoleId();
// 添加
const open = () => {
  addShow.value = true;
};

const addUser = (addFormRef: FormInstance | undefined) => {
  if (!addFormRef) return;
  addFormRef.validate(async (valid: any) => {
    if (valid) {
      await addAminUser_api({ ...addForm });
      addShow.value = false;
      addreset(addFormRef)
      getqueryUser();
    } else {
      return false;
    }
  });
};
// 编辑权限
const editUser = (row: any) => {
  editRoleForm.account = row.account;
  editRoleShow.value = true;
};
const editSubMit = async () => {
  await modUserAuth_api({ ...editRoleForm });
  // console.log(res);
  editRoleShow.value = false;
  getqueryUser();
};
// 删除
const deleUser = async (row: any) => {
  await delAdminUser_api({ account: row.account });
  getqueryUser();
};

// 编辑用户信息
const editUserInfo = (row:any) => {
  // console.log(row);
  
  let {account, mobile, username, status, id} = row
  editInfoForm.account = account
  editInfoForm.mobile = mobile
  editInfoForm.userName = username
  editInfoForm.status = status
  editInfoForm.id = id
  editInfoShow.value = true;
}

const subMitInfo = async () => {
  await modifyUser_api({...editInfoForm})
  getqueryUser();
  editInfoShow.value = false;
}

// 查询
const querySubmit = async () => {
  let res = await queryAminUser_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    id: queryForm.quId,
    account: queryForm.quName,
    mobile: queryForm.quMobile,
  });
  let { data, total } = res.data;
  userData.value = data;
  totalNum.value = total;
};

// 重置
const addreset = (addFormRef: FormInstance | undefined) => {
  if (!addFormRef) return;
  addFormRef.resetFields();
};
const onreset = () => {
  queryForm.quId = null;
  queryForm.quMobile = null;
  queryForm.quName = null;
  getqueryUser();
};
</script>

<style lang="scss" scoped>
.backgrounduser {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .card-header {
      display: flex;
      align-items: center;

      .el-button {
        margin-left: 10px;
      }

      .query-form-inline {
        // display: flex;
        align-items: center;
        margin-left: 10px;
        width: 100%;

        .el-input {
          width: 100%;
          height: 30px;
        }
      }
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .ro_name {
      span {
        display: inline-block;
        padding: 2px 3px;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin-right: 5px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    :deep(.el-dialog) {
      width: 35%;
    }

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
