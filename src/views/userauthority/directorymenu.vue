<template>
  <div class="directorymenu">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button @click="addResource" :icon="Plus">添加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="resourceMenu" style="width: 100%; height: 100%" row-key="id"
                  @selection-change="handleSelectionChange">
        <el-table-column prop="id" label="ID" min-width="100"></el-table-column>
        <el-table-column prop="name" label="名称" min-width="220"></el-table-column>
        <el-table-column prop="sn" label="sn(权重)" min-width="220"></el-table-column>
        <el-table-column prop="uriName" label="uri名(目录名/菜单名)" min-width="220"></el-table-column>
        <el-table-column prop="permission" label="描述" min-width="200"></el-table-column>
        <el-table-column label="图标" min-width="100">
          <template #default="scope">{{ scope.row.icon }}</template>
        </el-table-column>
        <el-table-column prop="url" label="URL" min-width="200"></el-table-column>
        <el-table-column label="类型" min-width="100">
          <template #default="scope">
            <span v-if="scope.row.type === 0">目录</span>
            <span v-else-if="scope.row.type === 1">菜单</span>
            <span v-else-if="scope.row.type === 2">按钮</span>
            <span v-else>未知类型</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editResource(scope.row)" size="small" type="primary" :icon="Edit"/>
            <el-popconfirm title="你确定删除吗?" @confirm="deleResource(scope.row)">
              <template #reference>
                <el-button size="small" type="danger" :icon="Delete"/>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20, 30]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"/>
      <el-dialog v-model="ShowMenu" title="添加权限目录">
        <el-form ref="menuFormRef" :model="MenuForm" status-icon class="demo-ruleForm" label-width="100px">
          <el-form-item label="名称" prop="name">
            <el-input v-model="MenuForm.name" autocomplete="off" placeholder="资源名称"/>
          </el-form-item>
          <el-form-item label="Url" prop="url">
            <el-input v-model="MenuForm.url" autocomplete="off" placeholder="资源路径"/>
          </el-form-item>
          <el-form-item label="SN(权重)" prop="sn">
            <el-input v-model="MenuForm.sn" autocomplete="off" placeholder="SN(权重)"/>
          </el-form-item>
          <el-form-item label="判断权限" prop="uriName">
            <el-input v-model="MenuForm.uriName" autocomplete="off" placeholder="用于判断权限"/>
          </el-form-item>
          <el-form-item label="描述" prop="permission">
            <el-input v-model="MenuForm.permission" autocomplete="off" placeholder="资源描述"/>
          </el-form-item>
          <el-form-item label="图标" prop="icon">
            <el-input v-model="MenuForm.icon" autocomplete="off" placeholder="资源图标"/>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select class="dia-ipt" v-model="MenuForm.type" placeholder="请选择类型">
              <el-option label="目录" value="0"/>
              <el-option label="菜单" value="1"/>
              <el-option label="按钮" value="2"/>
            </el-select>
          </el-form-item>
          <el-form-item label="父类ID" prop="parentId">
            <el-input v-model="MenuForm.parentId" autocomplete="off" placeholder="类型为目录填0"/>
          </el-form-item>
          <el-form-item label="表ID" prop="id" v-show="showMenuTab == false">
            <el-input v-model="MenuForm.id" autocomplete="off"/>
          </el-form-item>
          <el-form-item label="组件" prop="component">
            <el-input v-model="MenuForm.component" autocomplete="off" placeholder="组件"/>
          </el-form-item>
          <el-form-item label="重定向路径" prop="redirectionUrl">
            <el-input v-model="MenuForm.redirectionUrl" autocomplete="off" placeholder="重定向路径"/>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="ShowMenu = false">取消</el-button>
            <el-button type="primary" @click="menuSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import pageHook from "@/hooks/pageHook";
import {Delete, Edit, Plus} from "@element-plus/icons-vue";
import {
  resource_api,
  addAuth_api,
  modifyAuth_api,
  delAuth_api,
} from "@/api/user";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(getResource);

let resourceMenu = ref([]); //表格
let ShowMenu = ref(false);
let showMenuTab = ref(true);
let MenuForm = reactive<roleMenu>({
  // 添加
  name: null,
  url: null,
  sn: null,
  uriName: null,
  permission: null,
  icon: null,
  type: null,
  parentId: null,
  component: null,
  redirectionUrl: null,
  id: null,
});
let psize = ref(20)
pageSize.value = psize.value

async function getResource() {
  let res = await resource_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let {data, total} = res.data;
  data.forEach((v: any) => {
    if (v.menuList && v.menuList.length > 0) {
      v.children = v.menuList;
    }
    delete v.menuList;
  });
  resourceMenu.value = data;
  totalNum.value = total;
  // console.log(res);
}

getResource();
// 修改
const editResource = (row: any) => {
  showMenuTab.value = false;
  MenuForm = row;
  ShowMenu.value = true;
};
// 添加
const addResource = () => {
  showMenuTab.value = true;
  MenuForm = reactive<roleMenu>({
    // 添加
    name: null,
    url: null,
    sn: null,
    uriName: null,
    permission: null,
    icon: null,
    type: null,
    parentId: null,
    component: null,
    redirectionUrl: null,
    id: null,
  });
  ShowMenu.value = true;
};
const menuSubMit = async () => {
  if (showMenuTab.value == true) {
    let {name, url, type} = MenuForm;
    if (name != "" && url != "" && type != "") {
      await addAuth_api({...MenuForm});
      getResource();
      ShowMenu.value = false;
    }
  } else {
    await modifyAuth_api({...MenuForm});
    // console.log(res);
    getResource();
    ShowMenu.value = false;
  }
};
//删除
const deleResource = async (row: any) => {
  await delAuth_api({id: row.id});
  getResource();
};
const handleSelectionChange = () => {
};
</script>

<style lang="scss" scoped>
.directorymenu {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    :deep(.el-dialog) {
      width: 35%;
    }

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
