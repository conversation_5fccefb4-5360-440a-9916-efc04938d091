import { reactive } from "vue";

// 自己定义表格头部的数据
export const femaleSumPropColumn = reactive({
  statisticsDate: {
    prop: "statisticsDate",
    title: "日期",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  videoCardSumCnt: {
    prop: "videoCardSumCnt",
    title: "收到的视频卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  videoCardSumIncome: {
    prop: "videoCardSumIncome",
    title: "视频卡收益积分",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  }
  // ,
  // msgCardSumCnt: {
  //   prop: "msgCardSumCnt",
  //   title: "收到消息卡数量",
  //   minWidth: "100",
  //   show: true,
  //   type:null,
  //   fixed: "left",
  // },
  // msgCardSumIncome: {
  //   prop: "msgCardSumIncome",
  //   title: "消息卡收益积分",
  //   minWidth: "100",
  //   show: true,
  //   type:null,
  //   fixed: "left",
  // }
})