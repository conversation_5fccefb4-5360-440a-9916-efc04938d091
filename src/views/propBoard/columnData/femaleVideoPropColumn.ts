import { reactive } from "vue";

// 自己定义表格头部的数据
export const femaleVideoPropColumn = reactive({
  userId: {
    prop: "userId",
    title: "用户ID",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  // nickname: {
  //   prop: "nickname",
  //   title: "昵称",
  //   minWidth: "100",
  //   show: true,
  //   type:null,
  //   fixed: "left",
  // },
  receiveCardNums: {
    prop: "receiveCardNums",
    title: "收到的视频卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  videoCardIncome: {
    prop: "videoCardIncome",
    title: "视频卡收益积分",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  }
})