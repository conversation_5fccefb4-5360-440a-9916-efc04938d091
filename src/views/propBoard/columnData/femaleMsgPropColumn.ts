import { reactive } from "vue";

// 自己定义表格头部的数据
export const femaleMsgPropColumn = reactive({
  userId: {
    prop: "userId",
    title: "用户ID",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  // nickname: {
  //   prop: "nickname",
  //   title: "昵称",
  //   minWidth: "100",
  //   show: true,
  //   type:null,
  //   fixed: "left",
  // },
  receiveCardNums: {
    prop: "receiveCardNums",
    title: "收到的消息卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  msgCardIncome: {
    prop: "msgCardIncome",
    title: "消息卡收益积分",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
})