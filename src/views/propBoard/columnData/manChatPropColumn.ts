import { reactive } from "vue";

// 自己定义表格头部的数据
export const manChatPropColumn = reactive({
  userId: {
    prop: "userId",
    title: "用户ID",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  chatupCardSumCnt: {
    prop: "chatupCardSumCnt",
    title: "搭讪卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  useChatupCardSumCnt: {
    prop: "useChatupCardSumCnt",
    title: "使用搭讪卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  residueChatupSumCnt: {
    prop: "residueChatupSumCnt",
    title: "剩余搭讪卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },

})