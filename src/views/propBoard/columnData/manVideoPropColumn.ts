import { reactive } from "vue";

// 自己定义表格头部的数据
export const manVideoPropColumn = reactive({
  userId: {
    prop: "userId",
    title: "用户ID",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  videoCardSumCnt: {
    prop: "videoCardSumCnt",
    title: "视频卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  useVideoCardSumCnt: {
    prop: "useVideoCardSumCnt",
    title: "使用视频卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  residueVideoCardSumCnt: {
    prop: "residueVideoCardSumCnt",
    title: "剩余视频卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  }
})