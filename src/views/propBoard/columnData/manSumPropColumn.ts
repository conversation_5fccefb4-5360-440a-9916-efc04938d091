import { reactive } from "vue";

// 自己定义表格头部的数据
export const manSumPropColumn = reactive({
  unlockDate: {
    prop: "statisticsDate",
    title: "日期",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  videoCardSumCnt: {
    prop: "videoCardSumCnt",
    title: "视频卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  useVideoCardSumCnt: {
    prop: "useVideoCardSumCnt",
    title: "使用视频卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  // residueVideoCardSumCnt: {
  //   prop: "residueVideoCardSumCnt",
  //   title: "剩余视频卡数量",
  //   minWidth: "100",
  //   show: true,
  //   type:null,
  //   fixed: "left",
  // },
  chatupCardSumCnt: {
    prop: "chatupCardSumCnt",
    title: "搭讪卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  useChatupCardSumCnt: {
    prop: "useChatupCardSumCnt",
    title: "使用搭讪卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  msgCardSumCnt: {
    prop: "msgCardSumCnt",
    title: "消息卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  useMsgCardSumCnt: {
    prop: "useMsgCardSumCnt",
    title: "使用消息卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
})