import { reactive } from "vue";

// 自己定义表格头部的数据
export const manMsgPropColumn = reactive({
  userId: {
    prop: "userId",
    title: "用户ID",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  msgCardSumCnt: {
    prop: "msgCardSumCnt",
    title: "消息卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  useMsgCardSumCnt: {
    prop: "useMsgCardSumCnt",
    title: "使用消息卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  residueChatupSumCnt: {
    prop: "residueChatupSumCnt",
    title: "剩余消息卡数量",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  }
})