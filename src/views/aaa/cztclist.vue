<template>
  <div class="face">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="套餐状态">
              <el-select v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="''"/>
                <el-option label="显示" value="valid"/>
                <el-option label="隐藏" value="invalid"/>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset">重置</el-button>
              <el-button type="success" @click="handleAdd">添加套餐</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="faceRef" :data="faceData" style="width: 100%; height: 100%">
          <el-table-column label="ID" width="80" fixed>
            <template #default="scope">{{ scope.row.id }}</template>
          </el-table-column>
          <el-table-column label="套餐名称" min-width="140">
            <template #default="scope">{{ scope.row.content }}</template>
          </el-table-column>
          <el-table-column label="苹果ID" min-width="120">
            <template #default="scope">{{ scope.row.productid }}</template>
          </el-table-column>
          <el-table-column label="设备类型" min-width="100">
            <template #default="scope">
              <el-tag :type="!scope.row.productid ? 'success' : 'warning'">
                {{ !scope.row.productid ? '安卓' : '苹果' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="支付金额" min-width="100">
            <template #default="scope">{{ scope.row.price }}</template>
          </el-table-column>
          <el-table-column label="所得金币" min-width="100">
            <template #default="scope">{{ scope.row.golds }}</template>
          </el-table-column>
          <el-table-column label="赠送金币" min-width="100">
            <template #default="scope">{{ scope.row.giveGold }}</template>
          </el-table-column>
          <el-table-column label="赠送视频" min-width="100">
            <template #default="scope">{{ scope.row.videoSeconds }}</template>
          </el-table-column>
          <el-table-column label="赠送消息卡" min-width="100">
            <template #default="scope">{{ scope.row.chatNums }}</template>
          </el-table-column>
          <el-table-column label="赠送多少消息卡" min-width="120">
            <template #default="scope">{{ scope.row.textCount }}</template>
          </el-table-column>
          <el-table-column label="赠送多少视频卡" min-width="120">
            <template #default="scope">{{ scope.row.videoCount }}</template>
          </el-table-column>
          <el-table-column label="实际到手金币" min-width="100">
            <template #default="scope">{{ scope.row.rechargeGold }}</template>
          </el-table-column>
          <el-table-column label="排序" min-width="80">
            <template #default="scope">{{ scope.row.sort }}</template>
          </el-table-column>
          <el-table-column label="套餐类型" min-width="180">
            <template #default="scope">
              <el-tag v-if="scope.row.firstChargePackage" type="success">首充套餐</el-tag>
              <el-tag v-if="scope.row.newUserPackage" type="warning">新人套餐</el-tag>
              <el-tag v-if="scope.row.quickPaymentPackage" type="info">快捷支付套餐</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'valid' ? 'success' : 'danger'">
                {{ scope.row.status === 'valid' ? '显示' : '隐藏' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" :title="isEdit ? '编辑套餐' : '添加套餐'" width="50%">
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="套餐名称">
          <el-input v-model="editForm.content" placeholder="请输入套餐名称"/>
        </el-form-item>
        <el-form-item label="设备类型">
          <el-radio-group v-model="editForm.type" @change="handleDeviceTypeChange">
            <el-radio :label="1">安卓</el-radio>
            <el-radio :label="2">苹果</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="苹果ID" v-if="editForm.type === 2">
          <el-input v-model="editForm.productid" placeholder="请输入苹果ID"/>
        </el-form-item>
        <el-form-item label="支付金额">
          <el-input-number v-model="editForm.price" :min="0" :precision="2"/>
        </el-form-item>
        <el-form-item label="所得金币">
          <el-input-number v-model="editForm.golds" :min="0"/>
        </el-form-item>
        <el-form-item label="赠送金币">
          <el-input-number v-model="editForm.giveGold" :min="0"/>
        </el-form-item>
        <el-form-item label="视频最多可通话">
          <el-input-number v-model="editForm.videoSeconds" :min="0"/>
        </el-form-item>
        <el-form-item label="谐私聊最多可消息">
          <el-input-number v-model="editForm.chatNums" :min="0"/>
        </el-form-item>
        <el-form-item label="赠送多少消息卡">
          <el-input-number v-model="editForm.textCount" :min="0"/>
        </el-form-item>
        <el-form-item label="赠送多少视频卡">
          <el-input-number v-model="editForm.videoCount" :min="0"/>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="editForm.sort" :min="0"/>
        </el-form-item>
        <el-form-item label="快捷支付次数">
          <el-input-number v-model="editForm.quickPayCount" :min="0"/>
        </el-form-item>
        <el-form-item label="套餐类型">
          <el-checkbox v-model="editForm.firstChargePackage">首充套餐</el-checkbox>
          <el-checkbox v-model="editForm.newUserPackage">新人套餐</el-checkbox>
          <el-checkbox v-model="editForm.quickPaymentPackage">快捷支付套餐</el-checkbox>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editForm.status">
            <el-option label="显示" value="valid"/>
            <el-option label="隐藏" value="invalid"/>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEdit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import pageHook from "@/hooks/pageHook";
// import {getYMDHMS} from "@/utils/date"
import { golds_package_api, modify_package_api, add_package_api, del_package_api } from "@/api/aaa";
import { ElMessage, ElMessageBox } from 'element-plus'

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} = pageHook(
    querySubmit
);

let faceData = ref([]); // 表格数据

const queryForm = reactive<any>({
  status: '',
});

// 编辑相关
const editDialogVisible = ref(false);
const isEdit = ref(false);
const editForm = reactive<any>({
  id: '',
  content: '',
  productid: '',
  type: 1,
  price: 0,
  golds: 0,
  giveGold: 0,
  videoSeconds: 0,
  chatNums: 0,
  textCount: 0, // 赠送多少消息卡
  videoCount: 0, // 赠送多少视频卡
  sort: 0,
  quickPayCount: 0,
  firstChargePackage: false,
  newUserPackage: false,
  quickPaymentPackage: false,
  status: 'valid'
});

// 处理设备类型变更
const handleDeviceTypeChange = (val: string | number | boolean) => {
  if (val === 1 || val === '1') {
    editForm.productid = '';
  }
};

// 重置表单
const resetForm = () => {
  editForm.id = '';
  editForm.content = '';
  editForm.productid = '';
  editForm.type = 1;
  editForm.price = 0;
  editForm.golds = 0;
  editForm.giveGold = 0;
  editForm.videoSeconds = 0;
  editForm.chatNums = 0;
  editForm.textCount = 0; // 赠送多少消息卡
  editForm.videoCount = 0; // 赠送多少视频卡
  editForm.sort = 0;
  editForm.quickPayCount = 0;
  editForm.firstChargePackage = false;
  editForm.newUserPackage = false;
  editForm.quickPaymentPackage = false;
  editForm.status = 'valid';
};

// 处理添加
const handleAdd = () => {
  isEdit.value = false;
  resetForm();
  editDialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: any) => {
  isEdit.value = true;
  Object.assign(editForm, row);
  editDialogVisible.value = true;
};

// 确认编辑或添加
const confirmEdit = async () => {
  try {
    if (isEdit.value) {
      await modify_package_api(editForm);
      ElMessage.success('修改成功');
    } else {
      await add_package_api(editForm);
      ElMessage.success('添加成功');
    }
    editDialogVisible.value = false;
    querySubmit();
  } catch (error) {
    ElMessage.error(isEdit.value ? '修改失败' : '添加失败');
  }
};

// 处理删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    '确定要删除该套餐吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await del_package_api({ id: row.id });
      ElMessage.success('删除成功');
      querySubmit();
    } catch (error) {
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除操作
  });
};

// 请求列表数据
async function getfaceList() {
  let res = await golds_package_api({
    pageSize: pageSize.value || 20,
    page: currentPage.value || 1,
  });

  let {data, total} = res.data;
  faceData.value = data;
  totalNum.value = total;
}

getfaceList();

// 查询
async function querySubmit() {
  let {status} = queryForm;
  let res = await golds_package_api({
    status,
    pageSize: pageSize.value || 20,
    page: currentPage.value || 1,
  });
  let {data, total} = res.data;
  faceData.value = data;
  totalNum.value = total;
}

// 重置
const onreset = () => {
  queryForm.status = '';
  getfaceList();
};
</script>

<style scoped lang="scss">
.face {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .card-header {
      display: flex;
      align-items: center;
      width: 100%;

      .el-form {
        width: 100%;
      }
    }

    .none {
      display: none;
    }

    .image-slot {
      font-size: 30px;
    }

    .image-slot .el-icon {
      font-size: 30px;
    }

    .el-image {
      width: 100%;
    }
  }
}
</style>
