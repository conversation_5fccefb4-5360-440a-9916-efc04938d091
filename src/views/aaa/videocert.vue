<template>
  <div class="videocert">
    <!--    //视频认证-->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" :skipFirstSelect="true"
                          @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="认证状态" prop="type">
              <el-select class="header-select" v-model="queryForm.type" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="认证审核中" :value="1"/>
                <el-option label="认证成功" :value="2"/>
                <el-option label="认证失败" :value="3"/>
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryVideoAuth">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="VideoAuthTab" style="width: 100%; height: 100%">
          <el-table-column prop="id" label="ID" min-width="60" fixed></el-table-column>
          <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
          <el-table-column prop="nickname" label="昵称" min-width="120"></el-table-column>
          <el-table-column prop="avatar" label="用户头像" min-width="100">
            <template #default="scope">
              <el-image style="width: 70px; height: 70px" :src="scope.row.avatar" :zoom-rate="1.2"
                        :preview-src-list="[scope.row.avatar]" :initial-index="0" fit="cover" :preview-teleported="true"
                        :hide-on-click-modal="true"/>
            </template>
          </el-table-column>
          <el-table-column prop="url" label="视频地址" min-width="150">
            <template #default="scope">
              <video v-if="scope.row.url" style="width: 120px; height: 80px" controls>
                <source :src="scope.row.url" type="video/mp4">
                您的浏览器不支持视频播放
              </video>
              <span v-else>无视频</span>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="认证状态" min-width="120">
            <template #default="scope">{{ scope.row.typeText }}</template>
          </el-table-column>
          <el-table-column prop="createTime" label="审核时间" min-width="160"></el-table-column>
          <el-table-column prop="lastOperator" label="操作人" min-width="120"></el-table-column>
          <el-table-column prop="content" label="失败原因" min-width="150"></el-table-column>
          <el-table-column fixed="right" label="操作" width="140">
            <template #default="scope">
              <el-button v-if="scope.row.type === 1" @click="handleAudit({ row: scope.row, type: 2 })"
                         size="small" type="primary" style="color:#ffffff;">
                通过
              </el-button>
              <el-button v-if="scope.row.type === 1" @click="showRejectDialog(scope.row)"
                         size="small" type="danger" style="color:#ffffff;">
                拒绝
              </el-button>
              <el-button v-if="scope.row.type === 2" @click="showCancelDialog(scope.row)"
                         size="small" type="warning">
                取消认证
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>

    <!-- 拒绝原因对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="拒绝原因" width="500px">
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝原因" required>
          <el-input
            v-model="rejectForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入拒绝原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReject">确认拒绝</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 取消认证确认对话框 -->
    <el-dialog v-model="cancelDialogVisible" title="取消认证确认" width="400px">
      <div style="text-align: center; padding: 20px 0;">
        <el-icon style="font-size: 48px; color: #f56c6c; margin-bottom: 16px;">
          <WarningFilled />
        </el-icon>
        <p style="font-size: 16px; margin-bottom: 8px;">确定要取消该用户的视频认证吗？</p>
        <p style="color: #909399; font-size: 14px;">取消后用户需要重新进行视频认证</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialogVisible = false">取消</el-button>
          <el-button type="warning" @click="confirmCancel">确认取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import type {FormInstance} from 'element-plus'
import {ElMessage} from 'element-plus'
import {WarningFilled} from '@element-plus/icons-vue'
import pageHook from "@/hooks/pageHook";
import {query_ensure_video_api, update_ensure_video_api} from "@/api/authentication"

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(queryVideoAuth);
const queryFormRef = ref<FormInstance>()
let opNull = ref<any>(null)
let VideoAuthTab = ref([]);
let queryForm = reactive<any>({
  userId: null,
  type: 1, // 默认查询认证审核中的数据
  applicationId: ''
})

// 拒绝对话框相关
let rejectDialogVisible = ref(false);
let currentRejectRow = ref<any>(null);
let rejectForm = reactive({
  content: ''
})

// 取消认证对话框相关
let cancelDialogVisible = ref(false);
let currentCancelRow = ref<any>(null);

// 状态文本映射
const getTypeText = (type: number) => {
  switch (type) {
    case 1:
      return "认证审核中";
    case 2:
      return "认证成功";
    case 3:
      return "认证失败";
    default:
      return "未知状态";
  }
}

async function queryVideoAuthData() {
  let res = await query_ensure_video_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    type: queryForm.type,
    userId: queryForm.userId,
    applicationId: queryForm.applicationId,
  })
  let {data, total} = res.data

  // 处理数据，添加状态文本
  data.forEach((v: any) => {
    v.typeText = getTypeText(v.type);
  });

  VideoAuthTab.value = data
  totalNum.value = total
}

queryVideoAuthData()

// 查询
async function queryVideoAuth() {
  let res = await query_ensure_video_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let {data, total} = res.data

  // 处理数据，添加状态文本
  data.forEach((v: any) => {
    v.typeText = getTypeText(v.type);
  });

  VideoAuthTab.value = data
  totalNum.value = total
}

// 审核操作
const handleAudit = async ({row, type}: { row: any; type: number }) => {
  try {
    await update_ensure_video_api({
      id: row.id.toString(), // 转换为字符串
      desc: type === 3 ? '审核不通过' : '', // 失败原因
      type: type,
      userId: row.userId // 用户ID
    });

    // 根据操作类型显示不同的成功提示
    if (type === 2) {
      ElMessage.success('认证通过成功');
    } else if (type === 3) {
      ElMessage.success('取消认证成功');
    }

    queryVideoAuth();
  } catch (error) {
    console.error('审核操作失败:', error);
    ElMessage.error('审核操作失败');
  }
}

// 显示拒绝对话框
const showRejectDialog = (row: any) => {
  currentRejectRow.value = row;
  rejectForm.content = '';
  rejectDialogVisible.value = true;
}

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.content.trim()) {
    ElMessage.warning('请输入拒绝原因');
    return;
  }

  try {
    await update_ensure_video_api({
      id: currentRejectRow.value.id.toString(), // 转换为字符串
      desc: rejectForm.content, // 失败原因
      type: 3, // 认证失败
      userId: currentRejectRow.value.userId // 用户ID
    });
    rejectDialogVisible.value = false;
    ElMessage.success('审核操作成功');
    queryVideoAuth();
  } catch (error) {
    console.error('拒绝操作失败:', error);
    ElMessage.error('审核操作失败');
  }
}

// 显示取消认证对话框
const showCancelDialog = (row: any) => {
  currentCancelRow.value = row;
  cancelDialogVisible.value = true;
}

// 确认取消认证
const confirmCancel = async () => {
  try {
    await update_ensure_video_api({
      id: currentCancelRow.value.id.toString(), // 转换为字符串
      desc: '取消认证', // 失败原因
      type: 3, // 认证失败
      userId: currentCancelRow.value.userId // 用户ID
    });
    cancelDialogVisible.value = false;
    ElMessage.success('取消认证成功');
    queryVideoAuth();
  } catch (error) {
    console.error('取消认证失败:', error);
    ElMessage.error('取消认证失败');
  }
}

const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  queryVideoAuth();
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  queryForm.type = 1
  formEl.resetFields()
  queryVideoAuth()
}
</script>

<style lang="scss" scoped>
.videocert {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .card-header {
      .el-form-item {
        width: 22%;
      }

      .el-select {
        width: 100%;
      }
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }
}
</style>