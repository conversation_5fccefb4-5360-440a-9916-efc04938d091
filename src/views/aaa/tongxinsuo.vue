<template>
  <div class="face">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
<!--            <el-form-item label="女用户ID" prop="targetUserId">-->
<!--              <el-input v-model="queryForm.targetUserId"/>-->
<!--            </el-form-item>-->
            <el-form-item label="订单状态">
              <el-select v-model="queryForm.type" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="待确认" :value="0"/>
                <el-option label="已确认" :value="1"/>
                <el-option label="已退款" :value="2"/>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="时间选择">
              <el-date-picker v-model="queryForm.date" :clearable="false" type="daterange" unlink-panels
                              value-format="x"
                              format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :shortcuts="shortcuts" size="default" @change="timeChange"/>
            </el-form-item> -->
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="faceRef" :data="faceData" style="width: 100%; height: 100%">
          <el-table-column label="ID" width="80" fixed>
            <template #default="scope">{{ scope.row.id }}</template>
          </el-table-column>
          <el-table-column label="用户ID" width="120">
            <template #default="scope">{{ scope.row.userId }}</template>
          </el-table-column>
          <el-table-column label="女用户ID" min-width="120">
            <template #default="scope">{{ scope.row.targetUserId }}</template>
          </el-table-column>
          <el-table-column label="联系方式" min-width="120">
            <template #default="scope">{{ scope.row.contact }}</template>
          </el-table-column>
          <el-table-column label="支付金额" min-width="120">
            <template #default="scope">{{ scope.row.payAmount }}</template>
          </el-table-column>
          <el-table-column label="验证状态" min-width="120">
            <template #default="scope">
              <span>{{ scope.row.verifyContact ? '已验证' : '未验证' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="类型" min-width="120">
            <template #default="scope">
              <span v-if="scope.row.type === 0" style="color: #e6a23c;">待确认</span>
              <span v-if="scope.row.type === 1" style="color: #67c23a;">已确认</span>
              <span v-if="scope.row.type === 2" style="color: #f56c6c;">已退款</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" min-width="180">
            <template #default="scope">{{ getYMDHMS('-', ':', scope.row.gmtCreated) }}</template>
          </el-table-column>
          <el-table-column label="解锁时间" min-width="180">
            <template #default="scope">{{ getYMDHMS('-', ':', scope.row.unlockTime) }}</template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="120">
            <template #default="scope">
              <el-button
                type="danger"
                size="small"
                :disabled="scope.row.type !== 1 || !scope.row.verifyContact || scope.row.type === 2"
                @click="handleRefund(scope.row)"
              >
                退款
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import pageHook from "@/hooks/pageHook";
import {getYMDHMS} from "@/utils/date"
// import {orders_member_page} from "@/api/authentication";
import { tongxinsuo_list_api, tongxinsuo_tuikuan_api } from "@/api/aaa";
import { ElMessageBox, ElMessage } from 'element-plus/es'

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook((params: any) => {
      return tongxinsuo_list_api({
        ...params,
        size: params.pageSize,
        page: params.pageNum
      });
    });

let faceData = ref([]); // 表格数据
let opNull = ref<any>(null);

const queryForm = reactive<any>({
  userId: null,
  targetUserId: null,
  date: [],
  type: null,
  startTime: null,
  endTime: null,
  applicationId: null,
});

// const shortcuts = [
//   {
//     text: "最近一周",
//     value: () => {
//       const end = new Date();
//       const start = new Date();
//       start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
//       return [start, end];
//     },
//   },
//   {
//     text: "最近一个月",
//     value: () => {
//       const end = new Date();
//       const start = new Date();
//       start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
//       return [start, end];
//     },
//   },
//   {
//     text: "最近三个月",
//     value: () => {
//       const end = new Date();
//       const start = new Date();
//       start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
//       return [start, end];
//     },
//   },
// ];

// 请求列表数据
async function getfaceList() {
  let res = await tongxinsuo_list_api({
    size: 20,
    page: 1,
    applicationId: null,
    status: null
  });

  let {data, total} = res.data;
  console.log('Response data:', data); // 调试用
  faceData.value = data;
  totalNum = total;
}

getfaceList();

// 查询
// const timeChange = (data: any) => {
//   queryForm.startTime = data[0];
//   queryForm.endTime = data[1];
// };

// 更新查询函数
async function querySubmit() {
  let {userId, targetUserId, type, startTime, endTime} = queryForm;
  let res = await tongxinsuo_list_api({
    userId,
    targetUserId,
    verifyContact: type,
    startTime,
    endTime,
    size: pageSize.value || 20,
    page: currentPage.value || 1,
  });
  let {data, total} = res.data;
  console.log('Query response data:', data); // 调试用
  faceData.value = data;
  totalNum = total;
}

// 更新重置函数
const onreset = () => {
  queryForm.userId = null;
  queryForm.targetUserId = null;
  queryForm.date = [];
  queryForm.type = null;
  queryForm.startTime = null;
  queryForm.endTime = null;
  getfaceList();
};

// 修改退款处理函数
const handleRefund = (row: any) => {
  // 增加条件判断
  if (row.type !== 1 || !row.verifyContact) {
    ElMessage({
      type: 'warning',
      message: '只有已确认且已验证的订单才能退款',
    });
    return;
  }

  ElMessageBox.confirm(
    `确认要对ID为 ${row.id} 的订单进行退款操作吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        await tongxinsuo_tuikuan_api({
          id: row.id,
          bindUserId: row.targetUserId,
          userId: row.userId,
          verifyContact: row.verifyContact
        });
        ElMessage({
          type: 'success',
          message: '退款操作成功',
        });
        querySubmit();
      } catch (error) {
        ElMessage({
          type: 'error',
          message: '退款操作失败',
        });
      }
    })
    .catch(() => {
      // 取消操作
    });
};
</script>

<style scoped lang="scss">
.face {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .card-header {
      display: flex;
      align-items: center;
      width: 100%;

      .el-form {
        width: 100%;
      }
    }

    .none {
      display: none;
    }

    .image-slot {
      font-size: 30px;
    }

    .image-slot .el-icon {
      font-size: 30px;
    }

    .el-image {
      width: 100%;
    }

    :deep(.el-dialog) {
      width: 35%;
    }

    :deep(.dia-ipt) {
      width: 215px;
    }
  }
}
</style>
