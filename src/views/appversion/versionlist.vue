<template>
  <div class="versionlist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <majaSelect :applicationId="applicationId" 
                     :skipFirstSelect="false"
                     :disabled="tabShow"
                     @changeSelect="handleChnageAppID"
                     style="margin-right: 20px;"/>
          <el-button :icon="Plus" @click="addVersion">增加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="listData" style="width: 100%; height: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" min-width="60"></el-table-column>
        <el-table-column prop="clientType" label="安卓&ios" min-width="100">
          <template #default="scope">
            {{ scope.row.clientType == 1 ? "安卓" : "ios" }}
          </template>
        </el-table-column>
        <el-table-column prop="title" label="	弹窗更新标题" min-width="120"></el-table-column>
        <el-table-column prop="content" label="	更新内容" min-width="120"></el-table-column>
        <el-table-column prop="forceUpdate" label="强制更新" min-width="100">
          <template #default="scope">
            {{ scope.row.forceUpdate ? "是" : "否" }}
          </template>
        </el-table-column>
        <el-table-column prop="downloadUrl" label="下载地址" min-width="220"></el-table-column>
        <el-table-column prop="versionCode" label="版本号" min-width="100"></el-table-column>
        <el-table-column prop="versionName" label="版本名称" min-width="120"></el-table-column>
        <el-table-column prop="description" label="应用商店更新说明" min-width="150"></el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button @click="editVersion(scope.row)" size="small" type="primary" :icon="Edit"/>
            <el-popconfirm title="是否确认?" @confirm="delVersion(scope.row)">
              <template #reference>
                <el-button size="small" type="danger" :icon="Delete"/>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>

      <el-dialog v-model="isShow" :title="tabShow ? '编辑版本' : '增加版本'" width="35%">
        <el-form ref="listFormRef" :model="listForm" status-icon class="demo-ruleForm" label-width="100px">
          <el-form-item label="ID" prop="id" label-width="100" v-if="tabShow">
            <el-input v-model="listForm.id" disabled/>
          </el-form-item>
          <el-form-item label="安卓&ios" prop="clientType" label-width="100">
            <el-select v-model="listForm.clientType" placeholder="请选择">
              <el-option label="安卓" :value="1"/>
              <el-option label="ios" :value="2"/>
            </el-select>
          </el-form-item>
          <el-form-item label="马甲包" prop="applicationId">
            <majaSelect :applicationId="listForm.applicationId" 
                       :skipFirstSelect="true"
                       :disabled="tabShow"
                       @changeSelect="(e) => listForm.applicationId = e"/>
          </el-form-item>
          <el-form-item label="弹窗更新标题" prop="title" label-width="100">
            <el-input v-model="listForm.title"/>
          </el-form-item>
          <el-form-item label="更新内容" prop="content" label-width="100">
            <el-input v-model="listForm.content" type="textarea"/>
          </el-form-item>
          <el-form-item label="强制更新" prop="forceUpdate" label-width="100">
            <el-select v-model="listForm.forceUpdate" placeholder="请选择">
              <el-option label="是" :value="true"/>
              <el-option label="否" :value="false"/>
            </el-select>
          </el-form-item>
          <el-form-item label="下载地址" prop="downloadUrl" label-width="100">
            <el-input v-model="listForm.downloadUrl"/>
          </el-form-item>
          <el-form-item label="版本号" prop="versionCode" label-width="100">
            <el-input v-model="listForm.versionCode"/>
          </el-form-item>
          <el-form-item label="版本名称" prop="versionName" label-width="100">
            <el-input v-model="listForm.versionName"/>
          </el-form-item>
          <el-form-item label="更新说明" prop="description" label-width="100">
            <el-input v-model="listForm.description" placeholder="应用商店更新说明" type="textarea"/>
          </el-form-item>
          <el-form-item label="备注" prop="remark" label-width="100">
            <el-input v-model="listForm.remark" type="textarea"/>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isShow = false">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import {Edit, Delete, Plus} from "@element-plus/icons-vue";
import {queryVersion_api, delVersion_api, modifyVersion_api, addVersion_api} from "@/api/appversion"
import pageHook from "@/hooks/pageHook";
import majaSelect from "@/components/majaSelect.vue";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} = pageHook(queryVersion);
let listData = ref([])
let isShow = ref(false)
let tabShow = ref(false)
let loading = ref(false)
let listForm = reactive<any>({
  id: null,
  clientType: null,
  title: null,
  content: null,
  forceUpdate: null,
  downloadUrl: null,
  versionCode: null,
  versionName: null,
  description: null,
  remark: null,
  applicationId: null,
})
let applicationId = ref('com.dongxin.app')

//查询
async function queryVersion() {
  loading.value = true;
  let res = await queryVersion_api({
    pageSize: pageSize.value,
    applicationId: applicationId.value,
    pageNum: currentPage.value
  })
  let {data, total} = res.data
  loading.value = false;
  listData.value = data
  totalNum.value = total;
}

queryVersion()

const handleChnageAppID = (e: string) => {
  applicationId.value = e;
  queryVersion();
}
//编辑 增加
const editVersion = (row: any) => {
  listForm.id = row.id
  listForm.clientType = row.clientType
  listForm.title = row.title
  listForm.content = row.content
  listForm.forceUpdate = row.forceUpdate
  listForm.downloadUrl = row.downloadUrl
  listForm.versionCode = row.versionCode
  listForm.versionName = row.versionName
  listForm.description = row.description
  listForm.remark = row.remark
  listForm.applicationId = row.applicationId;
  isShow.value = true
  tabShow.value = true
}

const addVersion = () => {
  listForm.id = null
  listForm.clientType = null
  listForm.title = null
  listForm.content = null
  listForm.forceUpdate = null
  listForm.downloadUrl = null
  listForm.versionCode = null
  listForm.versionName = null
  listForm.description = null
  listForm.remark = null
  isShow.value = true
  tabShow.value = false
}

const editSubMit = async () => {
  if (tabShow.value) {
    await modifyVersion_api({...listForm})
    isShow.value = false
    queryVersion()
  } else {
    await addVersion_api({...listForm})
    isShow.value = false
    queryVersion()
  }
}

//删除
const delVersion = async (row: any) => {
  await delVersion_api({id: row.id})
  queryVersion()
}
</script>

<style lang="scss" scoped>
.versionlist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    min-width: 450px;
  }

  .el-select {
    width: 100%;
  }
}
</style>