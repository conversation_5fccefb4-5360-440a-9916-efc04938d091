<template>
  <div class="channelmanage">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-button :icon="Plus" @click="addChannel">增加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="listData" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" min-width="60"></el-table-column>
        <el-table-column prop="title" label="一级分类" min-width="120"></el-table-column>
        <el-table-column prop="titleTwo" label="二级分类" min-width="120"></el-table-column>
        <el-table-column prop="channelUrl" label="渠道Url" min-width="120"></el-table-column>
        <el-table-column prop="channelName" label="渠道名" min-width="120"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="120">
          <template #default="scope">{{ getYMDHMS("-", ":", scope.row.createTime) }}</template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button @click="editChannel(scope.row)" size="small" type="primary" :icon="Edit" />
            <el-button @click="delChannel(scope.row)" size="small" type="danger" :icon="Delete" />
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />

      <el-dialog v-model="isShow" :title="tabShow ? '编辑渠道' : '增加渠道'" width="35%" @close="disclose">
        <el-form ref="listFormRef" :model="listForm" status-icon class="demo-ruleForm" label-width="100px">
          <el-form-item label="ID" prop="id" label-width="100" v-if="tabShow">
            <el-input v-model="listForm.id" disabled />
          </el-form-item>
          <el-form-item label="一级分类" prop="title" label-width="100">
            <el-input v-model="listForm.title" />
          </el-form-item>
          <el-form-item label="二级分类" prop="titleTwo" label-width="100">
            <el-input v-model="listForm.titleTwo" />
          </el-form-item>
          <el-form-item label="渠道" prop="channelUrl" label-width="100">
            <el-input v-model="listForm.channelUrl" />
          </el-form-item>
          <el-form-item label="渠道名" prop="channelName" label-width="100">
            <el-input v-model="listForm.channelName" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isShow = false">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Edit, Delete, Plus } from "@element-plus/icons-vue";
import { getYMDHMS } from "@/utils/date"
import { queryUserChannel_api, modifyUserChannel_api, saveUserChannel_api, delUserChannel_api } from "@/api/appversion"
import pageHook from "@/hooks/pageHook";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(queryChannel);
const listFormRef = ref(null)
let listData = ref([])
let isShow = ref(false)
let tabShow = ref(false)
let listForm = reactive<any>({
  id: null,
  channelUrl: null,
  channelName: null,
  title: null,
  titleTwo: null,
})

//查询
async function queryChannel() {
  let res = await queryUserChannel_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let { data, total } = res.data
  listData.value = data
  totalNum.value = total;
}
queryChannel()

//编辑 增加
const editChannel = (row: any) => {
  listForm.id = row.id
  listForm.channelUrl = row.channelUrl
  listForm.channelName = row.channelName
  listForm.title = row.title
  listForm.titleTwo = row.titleTwo
  isShow.value = true
  tabShow.value = true
}

const addChannel = () => {
  isShow.value = true
  tabShow.value = false
}

const editSubMit = async () => {
  if (tabShow.value) {
    await modifyUserChannel_api({ ...listForm })
    isShow.value = false
    onreset(listFormRef.value)
    queryChannel()
  } else {
    await saveUserChannel_api({ ...listForm })
    isShow.value = false
    onreset(listFormRef.value)
    queryChannel()
  }
}

//删除
const delChannel = async (row: any) => {
  await delUserChannel_api({id:row.id})
  queryChannel()
}

//重置
function onreset(formEl: any | undefined) {
  if (!formEl) return
  formEl.resetFields()
}

const disclose = () => {
  onreset(listFormRef.value)
}
</script>

<style lang="scss" scoped>
.channelmanage {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    min-width: 450px;
  }

  .el-select {
    width: 100%;
  }
}
</style>