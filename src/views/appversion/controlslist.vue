<template>
  <div class="controlslist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
              <majaSelect :applicationId="applicationId" @changeSelect="changeSelect"/>
          <el-button :icon="Plus" @click="addControls">增加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="listData" style="width: 100%; height: 100%">
        <el-table-column prop="marketName" label="市场名称" min-width="120"></el-table-column>
        <el-table-column prop="code" label="版本号" min-width="120"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button @click="delControls(scope.row)" size="small" type="danger" :icon="Delete" />
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />

      <el-dialog v-model="isShow" title="增加审核包" width="35%" @close="disclose">
        <el-form ref="listFormRef" :model="listForm" status-icon class="demo-ruleForm" label-width="100px">
          <el-form-item label="马甲包">
              <majaSelect :applicationId="listForm.applicationId" @changeSelect="changeSelect1"/>
            </el-form-item>
          <el-form-item label="市场名称" prop="marketName" label-width="100">
            <el-input v-model="listForm.marketName" />
          </el-form-item>
          <el-form-item label="版本号" prop="code" label-width="100">
            <el-input v-model="listForm.code" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isShow = false">取消</el-button>
            <el-button type="primary" @click="editSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Delete, Plus } from "@element-plus/icons-vue";
import { queryAuditPackage_api, delAuditPackage_api, addAuditPackage_api } from "@/api/appversion"
import pageHook from "@/hooks/pageHook";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(queryControls);
const listFormRef = ref(null)
let listData = ref([])
let applicationId =ref('com.dongxin.app')
let isShow = ref(false)
let listForm = reactive<any>({
  marketName: null,
  code: null,
  applicationId:'com.dongxin.app'
})

//查询
async function queryControls() {
  let res = await queryAuditPackage_api({
    applicationId:applicationId.value,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })

  let { data, total } = res.data
  listData.value = data
  totalNum.value = total;
}
queryControls()

// 增加
const addControls = () => {
  isShow.value = true
}

const editSubMit = async () => {
  await addAuditPackage_api({ ...listForm })
  isShow.value = false
  onreset(listFormRef.value)
  queryControls()
}

//删除
const delControls = async (row: any) => {
  console.log(row);
  await delAuditPackage_api({ ...row })
  queryControls()
}

//重置
function onreset(formEl: any | undefined) {
  if (!formEl) return
  formEl.resetFields()
}

const disclose = () => {
  onreset(listFormRef.value)
}

const changeSelect=(e:string)=>{
  applicationId.value=e;
  queryControls();
}
const changeSelect1=(e:string)=>{
  listForm.applicationId=e;
}
</script>

<style lang="scss" scoped>
.controlslist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    min-width: 450px;
  }

  .el-select {
    width: 100%;
  }
}
</style>