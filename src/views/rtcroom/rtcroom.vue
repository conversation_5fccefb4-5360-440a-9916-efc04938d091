<template>
  <div class="rtc-room">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="对方用户ID" prop="toUserId">
              <el-input v-model="queryForm.toUserId" />
            </el-form-item>
            <el-form-item label="房间ID" prop="roomId">
              <el-input v-model="queryForm.roomId" />
            </el-form-item>
            <el-form-item label="通话类型" prop="type">
              <el-select class="header-select" v-model="queryForm.type" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="语音" :value="1" />
                <el-option label="视频" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="通话状态" prop="callStatus">
              <el-select class="header-select" v-model="queryForm.callStatus" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="创建房间" :value="1" />
                <el-option label="对方已接通" :value="2" />
                <el-option label="取消通话" :value="3" />
                <el-option label="对方拒绝" :value="4" />
                <el-option label="通话结束" :value="5" />
                <el-option label="所有结束的通话" :value="6" />
              </el-select>
            </el-form-item>
            <el-form-item label="房间状态" prop="roomStatus">
              <el-select class="header-select" v-model="queryForm.roomStatus" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="开" :value="1" />
                <el-option label="关" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="通话时长" prop="duration">
              <el-input-number v-model="queryForm.duration" style="width: 200px;"   :controls="false"/>
            </el-form-item>
            <el-form-item label="时间选择" prop="date">
              <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :shortcuts="shortcuts" size="default" @change="timeChange" />
            </el-form-item>


            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryRtcRoom">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="rtcRoomTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" min-width="80" fixed></el-table-column>
        <el-table-column prop="userId" label="用户ID" min-width="100"></el-table-column>
        <el-table-column prop="toUserId" label="对方用户ID" min-width="100"></el-table-column>
        <el-table-column prop="roomId" label="房间ID" min-width="120"></el-table-column>
        <el-table-column prop="roomStatus" label="通话类型" min-width="100">
          <template #default="scope">
            {{ scope.row.type == 1 ? "语音" : scope.row.type == 2 ? "视频" : "" }}
          </template>
        </el-table-column>
        <el-table-column prop="roomStatus" label="房间状态" min-width="100">
          <template #default="scope">
            {{ scope.row.roomStatus == 1 ? "开" : scope.row.roomStatus == 2 ? "关" : "" }}
          </template>
        </el-table-column>
        <el-table-column prop="createRoomTime" label="房间创建时间" min-width="180">
          <template #default="scope">
            {{ getYMDHMS("-", ":", scope.row.createRoomTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="callStatus" label="通话状态" min-width="120"></el-table-column>
        <el-table-column prop="duration" label="通话时长" min-width="120"></el-table-column>
        <el-table-column prop="startTime" label="通话开始时间" min-width="180">
          <template #default="scope">
            <span v-show="scope.row.startTime">{{ getYMDHMS("-", ":", scope.row.startTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="通话结束时间" min-width="180">
          <template #default="scope">
            <span v-show="scope.row.endTime">{{ getYMDHMS("-", ":", scope.row.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="hangUpUserId" label="挂断方ID" min-width="180">
        </el-table-column>
        <el-table-column prop="hangUpTime" label="挂断时间" min-width="180">
        </el-table-column>
        <el-table-column prop="userCloseReason" label="主动方流关闭原因" min-width="180">
          <template #default="{row}">
         {{ row.userCloseReason===1?'用户心跳超时关闭':row.userCloseReason===2?'用户重复登录同一个房间，关闭上次登录会话的流':
         row.userCloseReason===3?'服务端接口 kickout 用户，关闭用户创建的流':row.userCloseReason===4?'tcp掉线关闭流':row.userCloseReason===5?'房间被清除关闭流'
         :row.userCloseReason===100?'服务端接口关闭流':row.userCloseReason===0?'正常关闭':'' }}
          </template>
        </el-table-column>
        <el-table-column prop="toUserCloseReason" label="接听方流关闭原因" min-width="180">
          <template #default="{row}">
         {{ row.toUserCloseReason===1?'用户心跳超时关闭':row.toUserCloseReason===2?'用户重复登录同一个房间，关闭上次登录会话的流':
         row.toUserCloseReason===3?'服务端接口 kickout 用户，关闭用户创建的流':row.toUserCloseReason===4?'tcp掉线关闭流':row.toUserCloseReason===5?'房间被清除关闭流'
         :row.toUserCloseReason===100?'服务端接口关闭流':row.toUserCloseReason===0?'正常关闭':'' }}
          </template>
        </el-table-column>
        <el-table-column prop="womanHangUpReason" label="女方挂断原因" min-width="180">
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="260">
          <template #default="scope">
            <el-popconfirm title="是否关闭房间?" @confirm="closeRoom(scope.row, 1)">
              <template #reference>
                <el-button size="small" type="danger" v-show="scope.row.roomStatus != 2">关闭房间</el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm title="是否确认警告?" @confirm="warningRoom(scope.row, 2)">
              <template #reference>
                <el-button size="small" type="warning" v-show="scope.row.roomStatus != 2">警告</el-button>
              </template>
            </el-popconfirm>
            <el-button size="small" type="primary" @click="closePushflow(scope.row, 3)"  v-show="scope.row.roomStatus != 2">关闭推流</el-button>
            <el-button size="small" :type="showId===scope.row.id?'primary':''" @click="showVideo(scope.row)"  v-show="scope.row.callStatus === '通话结束'&&scope.row.show">查看回放</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      <el-dialog v-model="closePushflowShow" title="关闭推流" >
        <el-select class="header-select" v-model="closeStreamUserId" placeholder="请选择">
          <el-option label="用户ID" :value="closeStreamUser.userId" />
          <el-option label="对方用户ID" :value="closeStreamUser.toUserId" />
        </el-select>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closePushflowShow = false">取消</el-button>
            <el-button type="primary" @click="closePushflowSubMit">确认</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog
        draggable
        v-model="isShow"  
        class="videoPlayer"
        destroy-on-close
        width="70%"
        center
        title="查看回放"
        @close="handleClose"
        >
      <video class="video"  autoPlay :src="showVideoUrl" controls />
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from 'element-plus'
import pageHook from "@/hooks/pageHook";
import { getYMDHMS } from "@/utils/date";
import { queryRtcRoom_api, modifyUser_api } from "@/api/room"
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(queryRtcRoom);
const queryFormRef = ref<FormInstance>()
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;

let date = ref<[Date, Date]>([
  new Date(past7daysStart),
  new Date(todayEnd),
])
let showId=ref<any>();
let opNull = ref<any>(null)
let rtcRoomTab = ref([]);
let queryForm = reactive<any>({
  id: null,
  type: null,
  userId: null,
  toUserId: null,
  roomId: null,
  callStatus: null,
  roomStatus: null,
  startTime: past7daysStart,
  endTime: todayEnd,
  duration:null,
  applicationId:'com.dongxin.app'
})
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
let closeStreamUserId = ref()
let closeStreamUser = reactive<any>({
  id: null,
  type: null,
  userId: null,
  toUserId: null,
})
let isShow=ref(false);
let showVideoUrl=ref<any>(null)
let closePushflowShow = ref(false)

async function getRtcRoom() {
  let res = await queryRtcRoom_api({
    startTime: past7daysStart,
    endTime: todayEnd,
    applicationId:queryForm.applicationId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let { data, total } = res.data
  data.forEach((v: any) => {
     v.show=new Date().getTime()-new Date(v.createRoomTime).getTime()>60000&&v.cloudRecordUrl;
    if (v.callStatus == 1) {
      v.callStatus = "创建房间"
    } else if (v.callStatus == 2) {
      v.callStatus = "对方已接通"
    } else if (v.callStatus == 3) {
      v.callStatus = "取消通话"
    } else if (v.callStatus == 4) {
      v.callStatus = "对方拒绝"
    } else if (v.callStatus == 5) {
      v.callStatus = "通话结束"
    }
  })
  rtcRoomTab.value = data
  totalNum.value = total
}
getRtcRoom()

//关闭房间
const closeRoom = async (row: any, type: any) => {
  await modifyUser_api({ id: row.id, type })
  queryRtcRoom();
}
//警告
const warningRoom = async (row: any, type: any) => {
  await modifyUser_api({ id: row.id, type })
  queryRtcRoom();
}
//关闭推流
const closePushflow = (row: any, type: any) => {
  closeStreamUser.id = row.id
  closeStreamUser.type = type
  closeStreamUserId.value = row.userId
  closeStreamUser.userId = row.userId
  closeStreamUser.toUserId = row.toUserId
  closePushflowShow.value = true
}

const closePushflowSubMit = async () => {
  await modifyUser_api({ id: closeStreamUser.id, type:closeStreamUser.type,closeStreamUserId:closeStreamUserId.value })
  queryRtcRoom();
  closePushflowShow.value = false
}

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function queryRtcRoom() {
  let res = await queryRtcRoom_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let { data, total } = res.data
  data.forEach((v: any) => {
    v.show=new Date().getTime()-new Date(v.createRoomTime).getTime()>60000&&v.cloudRecordUrl;
    if (v.callStatus == 1) {
      v.callStatus = "创建房间"
    } else if (v.callStatus == 2) {
      v.callStatus = "对方已接通"
    } else if (v.callStatus == 3) {
      v.callStatus = "取消通话"
    } else if (v.callStatus == 4) {
      v.callStatus = "对方拒绝"
    } else if (v.callStatus == 5) {
      v.callStatus = "通话结束"
    }
  })
  rtcRoomTab.value = data
  totalNum.value = total
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = [
    new Date(past7daysStart),
    new Date(todayEnd),
  ]
  queryForm.startTime = past7daysStart
  queryForm.endTime = todayEnd
  formEl.resetFields()
  queryRtcRoom()
}

const showVideo=(e:any)=>{
  showId.value=e.id;
  showVideoUrl.value=e.cloudRecordUrl;
  isShow.value=true;
}

const handleClose=()=>{
  showVideoUrl.value=null;
}
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  queryRtcRoom();
}
</script>

<style lang="scss" scoped>
.video{
  width: 100%;
  height: 100%;
  padding: 0;
}
.rtc-room {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    .el-select {
      width: 100%;
    }
    :deep(.el-input-number .el-input__inner) {
      text-align: left;
    }
  }
}
</style>