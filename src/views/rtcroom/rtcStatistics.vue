<template>
  <div class="rtcStatistics">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="queryForm"
            class="demo-form-inline"
          >
          <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item prop="gender" label="性别">
              <el-select
                v-model="queryForm.gender"
                placeholder="请选择"
                style="width: 200px"
              >
                <el-option label="男" :value="1" />
                <el-option label="女" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择" prop="id">
              <el-date-picker
                v-model="date"
                :clearable="false"
                type="daterange"
                unlink-panels
                value-format="x"
                format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                size="default"
                @change="timeChange"
              />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button class="ml" type="primary" @click="handleExportExcel">
                <el-icon style="vertical-align: middle">
                  <Download />
                </el-icon>
                <span style="vertical-align: middle">导出Excel</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="rtcTable" style="width: 100%; height: 100%">
        <el-table-column prop="date" label="时间" min-width="150" fixed></el-table-column>
        <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
        <el-table-column prop="callCnt" label="用户主动拨打次数" min-width="150">
          <template #default="{ row }">
            {{ row.callCnt ? row.callCnt : "0" }}
          </template>
        </el-table-column>
        <el-table-column prop="collectCnt" label="接通次数" min-width="100">
          <template #default="{ row }">
            {{ row.collectCnt ? row.collectCnt : "0" }}
          </template>
        </el-table-column>
        <el-table-column prop="collectRate" label="接通率" min-width="150">
          <template #default="{ row }">
            {{ row.collectRate ? row.collectRate : "0%" }}
          </template>
        </el-table-column>
        <el-table-column prop="hangUpCnt" label="用户主动挂断的次数" min-width="150">
          <template #default="{ row }">
            {{ row.hangUpCnt ? row.hangUpCnt : "0" }}
          </template>
        </el-table-column>
        <el-table-column prop="rtcCnt" label="接通的通话次数" min-width="120">
          <template #default="{ row }">
            {{ row.rtcCnt ? row.rtcCnt : "0" }}
          </template>
        </el-table-column>
        <el-table-column prop="hangUpRate" label="挂断率" min-width="120">
          <template #default="{ row }">
            {{ row.hangUpRate ? row.hangUpRate : "0%" }}
          </template>
        </el-table-column>
        <el-table-column prop="calledCnt" label="用户被拨打次数" min-width="120">
          <template #default="{ row }">
            {{ row.calledCnt ? row.calledCnt : "0" }}
          </template>
        </el-table-column>
        <el-table-column prop="receptionCnt" label="接听次数" min-width="120">
          <template #default="{ row }">
            {{ row.receptionCnt ? row.receptionCnt : "0" }}
          </template>
        </el-table-column>
        <el-table-column prop="receptionRate" label="接听率" min-width="120">
          <template #default="{ row }">
            {{ row.receptionRate ? row.receptionRate : "0%" }}
          </template>
        </el-table-column>
        <el-table-column prop="rtcDuration" label="通话时长(秒)" min-width="120">
        </el-table-column>
      </el-table>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 50, 100, 150]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import pageHook from "@/hooks/pageHook";
import { getYMD } from "@/utils/date";
import { statistics_api } from "@/api/index";
import { aoaToSheetXlsx } from "@/utils/excel";
import { flattenArray } from "@/utils/list";
import { concurrencyRequest } from "@/utils/concurrencyRequest";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  query
);
const queryFormRef = ref<FormInstance>();
let toData = new Date(new Date().toLocaleDateString()).getTime();
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
let rtcTable = ref([]);
let listLoading = ref<boolean>(false);
let queryForm = reactive<any>({
  userId: null,
  gender: 2,
  begin: getYMD("-", past7daysStart),
  end: getYMD("-", toData),
  applicationId:'com.dongxin.app'
});
let date = ref<[Date, Date]>([new Date(past7daysStart), new Date(toData)]);

const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

const timeChange = (data: any) => {
  queryForm.begin = getYMD("-", data[0]);
  queryForm.end = getYMD("-", data[1]);
};

// 查询
function query() {
  statistics_api({
    ...queryForm,
    size: pageSize.value,
    page: currentPage.value,
  }).then((res: any) => {
    let { data, total } = res.data;
    rtcTable.value = data;
    totalNum.value = total;
  });
}
query();

const onreset = (formEl: FormInstance | undefined) => {
  date.value = [new Date(past7daysStart), new Date(toData)];
  if (!formEl) return;
  formEl.resetFields();
  query();
};

async function getKeys() {
  let queue: any = [];
  let page = Math.ceil(totalNum.value / 10000);
  for (let i = 1; i <= page; i++) {
    let urls = {
      data: { ...queryForm, page: i, size: 10000 },
      url: "/admin/rtc/rtc_manage/statistics",
    };

    queue.push(urls);
  }
  let res = await concurrencyRequest(queue, 2);
  return flattenArray(res);
}

// 导出Excel表格
const handleExportExcel = async () => {
  let res = await getKeys();
  let data = res.map((item: any, _index: any) => {
    let {
      date,
      userId,
      callCnt,
      collectCnt,
      collectRate,
      hangUpCnt,
      rtcCnt,
      hangUpRate,
      calledCnt,
      receptionCnt,
      receptionRate,
      rtcDuration,
    } = item;
    return [
      date,
      userId,
      callCnt,
      collectCnt,
      collectRate,
      hangUpCnt,
      rtcCnt,
      hangUpRate,
      calledCnt,
      receptionCnt,
      receptionRate,
      rtcDuration,
    ];
  });

  listLoading.value = true;

  //标题数组

  let header: any = [
    "时间",
    "用户ID",
    "用户主动拨打次数",
    "接通次数",
    "接通率",
    "用户主动挂断的次数",
    "接通的通话次数",
    "挂断率",
    "用户被拨打次数",
    "接听次数",
    "接听率",
    "通话时长",
  ];

  aoaToSheetXlsx({
    data,
    header,
    filename: `音视频通话统计.xlsx`,
  });


  listLoading.value = false;
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}
</script>

<style lang="scss" scoped>
.rtcStatistics {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }
  .el-pagination {
    margin-top: auto;
    margin-bottom: 0;
    background-color: #fff;
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
    z-index: 10;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>
