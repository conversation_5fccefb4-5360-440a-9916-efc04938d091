<template>
  <div class="goldsstatistics">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
              <el-form-item label="选择日期">
                <el-date-picker v-model="date" value-format="x" :clearable="false" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="shortcuts" size="default"
                @change="timeChange" />
              </el-form-item>
              <el-form-item class="qubtn">
                <el-button type="primary" @click="query">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="goldsTab" style="width: 100%; height: 100%">
        <el-table-column prop="date" label="日期" min-width="120" fixed></el-table-column>
        <el-table-column prop="yesterdayBalance" label="昨日总余额" min-width="120"></el-table-column>
        <!-- <el-table-column prop="addAmount" label="产出" min-width="120"></el-table-column> -->
        <el-table-column prop="addAmountPay" label="产出（付费金币）" min-width="120"></el-table-column>
        <el-table-column prop="addAmountFree" label="产出（赠送金币）" min-width="120"></el-table-column>
        <!-- <el-table-column prop="deductionAmount" label="扣减" min-width="120"></el-table-column>  -->
        <el-table-column prop="deductionAmountPay" label="消耗（付费金币）" min-width="120"></el-table-column>
        <el-table-column prop="deductionAmountFree" label="消耗（赠送金币）" min-width="120"></el-table-column>
        <el-table-column prop="todayBalance" label="今天总余额" min-width="120"></el-table-column>
        <el-table-column prop="hasAbnormal" label="是否异常" min-width="120">
          <template #default="scope">
            <span :class="scope.row.hasAbnormal ? 'hasErrMark-on' : ''">{{ scope.row.hasAbnormal ? "是" : "否" }}</span>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from 'element-plus'
import pageHook from "@/hooks/pageHook";
import { queryPlatformGolds_api } from "@/api/flowingwater"
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(queryPlatformGolds);

let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
let date = ref<[Date, Date]>([
  new Date(past7daysStart),
  new Date(todayEnd),
])
const queryFormRef = ref<FormInstance>()
let queryForm = reactive<any>({
  startTime: null,
  endTime: null,
  applicationId:'com.dongxin.app'
})
let goldsTab = ref<any>([])

const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
}

async function queryPlatformGolds() {
  let res = await queryPlatformGolds_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let { data, total } = res.data
  goldsTab.value = data
  totalNum.value = total
}
queryPlatformGolds()

const query = () => {
  queryPlatformGolds()
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  queryForm.startTime = null;
  queryForm.endTime = null;
  date.value = [
    new Date(past7daysStart),
    new Date(todayEnd),
  ]
  formEl.resetFields()
  queryPlatformGolds()
}
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}
</script>

<style lang="scss" scoped>
.goldsstatistics {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .hasErrMark-on {
    color: #f00;
  }
}
</style>