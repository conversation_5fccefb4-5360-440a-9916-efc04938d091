<template>
  <div class="recharge">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <!-- <el-form-item label="ID" prop="id">
              <el-input v-model="queryForm.id" />
            </el-form-item> -->
            <el-form-item label="商户订单号" prop="orderNum">
              <el-input v-model="queryForm.orderNum"/>
            </el-form-item>
            <el-form-item label="邀请人ID" prop="inviteUserId">
              <el-input v-model="queryForm.inviteUserId"/>
            </el-form-item>
            <el-form-item label="产品ID" prop="productId">
              <el-input v-model="queryForm.productId"/>
            </el-form-item>
            <el-form-item label="订单状态" prop="status">
              <el-select class="header-select" v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="未支付" value="unpay"/>
                <el-option label="已支付" value="payed"/>
              </el-select>
            </el-form-item>
            <el-form-item label="订单业务状态" prop="businessStatus">
              <el-select class="header-select" v-model="queryForm.businessStatus" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="未发货" value="not_shipped"/>
                <el-option label="已发货" value="shipped"/>
              </el-select>
            </el-form-item>
            <el-form-item label="充值类型" prop="orderType">
              <el-select class="header-select" v-model="queryForm.orderType" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="金币" value="golds"/>
                <el-option label="新人三日充值礼包" value="new_user_3day_package"/>
                <el-option label="新人半屏充值礼包" value="new_user_package"/>
                <el-option label="新人首次礼包充值" value="new_user_first_package"/>
                <el-option label="周卡" value="card_package"/>
                <el-option label="申请加入红娘群聊" value="join_group"/>
              </el-select>
            </el-form-item>
            <el-form-item label="支付方式" prop="paymentMethod">
              <el-select class="header-select" v-model="queryForm.paymentMethod" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="微信支付" value="wechat_app"/>
                <el-option label="支付宝支付" value="alipay_app"/>
              </el-select>
            </el-form-item>
            <el-form-item label="邀请人" prop="inviteUserType">
              <el-select class="header-select" v-model="queryForm.inviteUserType" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="有" value="2"/>
                <el-option label="无" value="1"/>
              </el-select>
            </el-form-item>
            <el-form-item label="渠道选择">
              <el-cascader
                  popper-class="cascaderRadio"
                  v-model="queryForm.data"
                  :options="options"
                  :props="{
                  expandTrigger: 'hover',
                  label: 'channelName',
                  children: 'childChannels',
                  value: 'channelCode',
                  checkStrictly: true,
                  multiple: true,
                }"
                  clearable
              >
                <template #default="{ node, data }">
                  <span
                      class="custom-node leaf"
                      v-if="node.isLeaf == 0"
                      @mouseenter="mouseenterLeaf(node)"
                  >{{ data.channelName }}</span
                  >
                  <span
                      v-else
                      class="custom-node noLeaf"
                      @mouseenter="mouseenterSubcat(node)"
                  >{{ data.channelName }}</span
                  >
                </template>
              </el-cascader>
            </el-form-item>
            <el-form-item label="时间选择" prop="date" class="item_time">
              <el-date-picker v-model="date" :clearable="false" type="daterange" range-separator="至"
                              start-placeholder="开始日期" end-placeholder="结束日期" value-format="x"
                              :shortcuts="shortcuts" size="default"
                              @change="timeChange"/>
            </el-form-item>
            <el-form-item label="充值性别" prop="gender">
              <el-select class="header-select" v-model="queryForm.gender" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="男" value="1"/>
                <el-option label="女" value="2"/>
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryRech">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button type="primary" :icon="Download" @click="handleExcel">导出Excel</el-button>
            </el-form-item>
          </el-form>
          <div><span>累计所得金币 : {{ goldTotal }}</span> <span>累计赠送金币: {{ give_gold_total }}</span> <span>累计充值: {{
              prices
            }}</span></div>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="rechargeTab" style="width: 100%; height: 100%" v-loading="Loading">
        <el-table-column prop="id" label="ID" min-width="80" fixed></el-table-column>
        <el-table-column prop="nickname" label="昵称" min-width="150">
          <template #default="scope">
            <div style="display: flex; align-items: center;">
              <span>{{ scope.row.nickname }}</span>
              <sup v-if="scope.row.channel === 'vivo_vivo_vivo'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">vivo</sup>
              <sup v-else-if="scope.row.channel === 'huawei_huawei_huawei'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">华为</sup>
              <sup v-else-if="scope.row.channel === 'official_official_official'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">官方</sup>
              <sup v-else-if="scope.row.channel === 'oppo_oppo_oppo'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">oppo</sup>
              <sup v-else-if="scope.row.channel === 'new_new_new'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New</sup>
              <sup v-else-if="scope.row.channel === 'new1_new1_new1'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New1</sup>
              <sup v-else-if="scope.row.channel === 'new2_new2_new2'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New2</sup>
              <sup v-else-if="scope.row.channel === 'dage_dage_dage'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">大哥</sup>
              <sup v-else-if="scope.row.channel === 'honor_honor_honor'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">荣耀</sup>
              <sup v-else-if="scope.row.channel === 'ying_ying_ying'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">YY宝</sup>
              <sup v-else-if="scope.row.channel === 'juliang_juliang_juliang'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">抖音</sup>
              <sup v-else-if="scope.row.channel === 'xiaomi_xiaomi_xiaomi'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">小米</sup>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="userId" label="用户ID" min-width="100"></el-table-column>
        <el-table-column prop="inviteUserId" label="邀请人ID" min-width="100"></el-table-column>
        <el-table-column prop="registerTime" label="注册时间" min-width="180">
          <template #default="scope">{{ getYMDHMS("-", ":", scope.row.registerTime) }}</template>
        </el-table-column>
        <el-table-column prop="price" label="价格" min-width="80"></el-table-column>
        <el-table-column prop="golds" label="付费金币" min-width="80"></el-table-column>
        <el-table-column prop="giveGold" label="赠送金币" min-width="100"></el-table-column>
        <el-table-column prop="status" label="订单状态" min-width="100">
          <template #default="scope">{{ scope.row.status == "unpay" ? "未支付" : "已支付" }}</template>
        </el-table-column>
        <el-table-column prop="businessStatus" label="业务状态" min-width="120">
          <template #default="scope">{{ scope.row.businessStatus == "shipped" ? "已发货" : "未发货" }}</template>
        </el-table-column>
        <el-table-column prop="orderType" label="充值类型" min-width="100">
          <template #default="scope">{{
              scope.row.orderType == "golds" ? "金币" : scope.row.orderType == "new_user_3day_package" ? '新人三日充值礼包' : scope.row.orderType == "new_user_package" ? '新人半屏充值礼包' : scope.row.orderType === 'card_package' ? '周卡' : scope.row.orderType === 'new_user_first_package' ? '新人首次礼包充值' : scope.row.orderType === 'join_group' ? '申请' : '申请加入红娘群聊'
            }}
          </template>
        </el-table-column>
        <el-table-column prop="productId" label="产品ID" min-width="100"></el-table-column>
        <el-table-column prop="orderNum" label="商户订单号" min-width="190"></el-table-column>
        <el-table-column prop="paymentMethod" label="支付方式" min-width="120">
          <template #default="scope">{{
              scope.row.paymentMethod == "wechat_app" ? "微信支付" : "支付宝支付"
            }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="150">
          <template #default="scope">{{ getYMDHMS("-", ":", scope.row.createTime) }}</template>
        </el-table-column>
        <el-table-column prop="payTime" label="支付时间" min-width="150">
          <template #default="scope">
            <span v-show="scope.row.payTime">{{ getYMDHMS("-", ":", scope.row.payTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {query_channel_list} from "@/api/datadetail";
import {ref, reactive, unref, onMounted} from "vue";
import type {FormInstance} from 'element-plus'
import pageHook from "@/hooks/pageHook";
import {getYMDHMS} from "@/utils/date"
import {Download} from "@element-plus/icons-vue";
import {queryRecharge_api, queryrechargeTotal_api} from "@/api/flowingwater"
import {ElMessage} from "element-plus";
import {aoaToSheetXlsx} from "@/utils/excel";
import {concurrencyRequest} from '@/utils/concurrencyRequest'
import {flattenArray} from '@/utils/list';

let options = ref<any>();
let goldTotal = ref(0);
let give_gold_total = ref(0);
let prices = ref(0);

//查询筛选列表
async function query_list() {
  let res = await query_channel_list();
  let {data} = res.data;
  options.value = data;
}


query_list();
let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(queryRech);
const queryFormRef = ref<FormInstance>()
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
let opNull = ref<any>(null)
let rechargeTab = ref([]);
let Loading = ref(false);
let queryForm = reactive<any>({
  id: null,
  orderNum: null,
  userId: null,
  status: null,
  bindTime: null,
  businessStatus: null,
  orderType: null,
  productId: null,
  paymentMethod: null,
  inviteUserType: null,
  startTime: past7daysStart,
  endTime: todayEnd,
  bindInviteUserId: null,
  registerTime: null,
  applicationId: 'com.dongxin.app',
  gender: null,
  data: [] // 渠道选择数据，支持多选
})

let date = ref<[Date, Date]>([
  new Date(past7daysStart),
  new Date(todayEnd),
])

function mouseenterSubcat(node: any) {
  let el_node = document.querySelectorAll(
      ".el-popper.el-cascader__dropdown.specialCascaderRadio .el-cascader-panel .el-cascader-menu"
  );
  if (el_node[node.level] && el_node.length > 0) {
    (el_node[node.level] as HTMLElement).style.display = "none";
    cascaderRecursion(el_node, node);
  }
}

function mouseenterLeaf(node: any) {
  let el_node = document.querySelectorAll(
      ".el-popper.el-cascader__dropdown.specialCascaderRadio .el-cascader-panel .el-cascader-menu"
  );
  if (el_node[node.level] && el_node.length > 0) {
    (el_node[node.level] as HTMLElement).style.display = "block";
  }
}

function cascaderRecursion(el_node: any, node: any) {
  function handle(i: any) {
    if (el_node[node.level + i]) {
      el_node[node.level + i].style.display = "none";
      i++;
      handle(i);
    } else {
      return;
    }
  }

  handle(1);
}

async function getKeys() {
  let queue: any = [];
  let pageNum = Math.ceil(totalNum.value / 10000);

  // 处理渠道数据，与查询函数保持一致
  let channelData: any = {};
  if (queryForm.data && queryForm.data.length > 0) {
    if (Array.isArray(queryForm.data[0])) {
      // 多选情况
      const channels = queryForm.data.map((item: any) => ({
        oneLevel: item[0] || undefined,
        twoLevel: item[1] || undefined,
        threeLevel: item[2] || undefined,
      }));
      const oneLevels = [...new Set(channels.map((c: any) => c.oneLevel).filter(Boolean))];
      const twoLevels = [...new Set(channels.map((c: any) => c.twoLevel).filter(Boolean))];
      const threeLevels = [...new Set(channels.map((c: any) => c.threeLevel).filter(Boolean))];

      channelData.oneLevel = oneLevels.length === 1 ? oneLevels[0] : oneLevels.length > 1 ? oneLevels.join(',') : undefined;
      channelData.twoLevel = twoLevels.length === 1 ? twoLevels[0] : twoLevels.length > 1 ? twoLevels.join(',') : undefined;
      channelData.threeLevel = threeLevels.length === 1 ? threeLevels[0] : threeLevels.length > 1 ? threeLevels.join(',') : undefined;
    } else {
      // 单选情况
      channelData.oneLevel = queryForm.data[0] || undefined;
      channelData.twoLevel = queryForm.data[1] || undefined;
      channelData.threeLevel = queryForm.data[2] || undefined;
    }
  }

  for (let i = 1; i <= pageNum; i++) {
    let urls = {
      data: {
        ...queryForm,
        ...channelData,
        pageNum: i,
        pageSize: 10000
      },
      url: '/admin/journal_account/recharge/query_recharge_execl'
    }

    queue.push(urls);
  }
  let res = await concurrencyRequest(queue, 2);
  return flattenArray(res);
}

// 导出Excel表格
const fileName = ref("充值管理");
const handleExcel = async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }
  //获取后端数据
  Loading.value = true;
  let res = await getKeys();
  let header = ['ID', '商户订单号', '用户ID', '渠道号', '邀请人id', '邀请人', '注册时间', '价格（元）', '付费金币', '赠送金币', '订单状态', '订单业务状态', '充值类型', '产品ID', '支付方式', '创建时间', '支付时间'];
  let listData = res;

  let datas = listData.map((item: any, _index: any) => {
    item.paymentMethod == "wechat_app" ? item.paymentMethod = "微信支付" : item.paymentMethod = "支付宝支付";
    item.orderType == "golds" ? item.orderType = "金币" : item.orderType = "";
    item.businessStatus == "shipped" ? item.businessStatus = "已发货" : item.businessStatus = "未发货"
    item.status == "unpay" ? item.status = "未支付" : item.status = "已支付"
    item.inviteUserType = (item.hasOwnProperty('inviteUserId') && item.inviteUserId !== "") || item.inviteUserId !== undefined ? "有" : "无";
    let {
      id,
      orderNum,
      userId,
      channel,
      inviteUserId,
      inviteUserType,
      registerTime,
      price,
      golds,
      giveGold,
      status,
      businessStatus,
      orderType,
      productId,
      paymentMethod,
      createTime,
      payTime
    } = item;
    return [id, orderNum, userId, channel, inviteUserId, inviteUserType, registerTime, price, golds, giveGold, status, businessStatus, orderType, productId, paymentMethod, createTime, payTime];
  });
  Loading.value = false;
  aoaToSheetXlsx({
    data: datas,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });
};
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

async function queryRecharge() {
  let res = await queryRecharge_api({
    startTime: past7daysStart,
    endTime: todayEnd,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId: queryForm.applicationId
  })
  let {data, total} = res.data
  rechargeTab.value = data
  totalNum.value = total
}

queryRecharge()


async function fetchTotalWithdrawal(queryData = {}) {
  const res = await queryrechargeTotal_api(queryData);
  const data = res.data;
  console.log(data);  // 打印返回的数据
  if (data.code === 200 && data.success) {
    goldTotal.value = data.data.goldTotal;
    give_gold_total.value = data.data.give_gold_total;
    prices.value = data.data.prices;
    console.log(goldTotal.value, give_gold_total.value, prices.value);  // 打印赋值后的变量
  }
}

onMounted(() => {
  fetchTotalWithdrawal();
});

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function queryRech() {
  let queryData = {
    ...queryForm,
    gender: queryForm.gender !== null ? Number(queryForm.gender) : null,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  };

  // 处理渠道选择数据 - 支持多选
  if (queryForm.data && queryForm.data.length > 0) {
    console.log('渠道选择数据:', queryForm.data); // 调试信息
    // 检查是否为多选模式（二维数组）
    if (Array.isArray(queryForm.data[0])) {
      console.log('多选模式'); // 调试信息
      // 多选情况：提取所有选中的渠道
      const channels = queryForm.data.map((item: any) => ({
        oneLevel: item[0] || undefined,
        twoLevel: item[1] || undefined,
        threeLevel: item[2] || undefined,
      }));
      console.log('解析的渠道:', channels); // 调试信息
      // 将多个渠道条件合并为单个查询条件
      // 提取所有不重复的级别值
      const oneLevels = [...new Set(channels.map((c: any) => c.oneLevel).filter(Boolean))];
      const twoLevels = [...new Set(channels.map((c: any) => c.twoLevel).filter(Boolean))];
      const threeLevels = [...new Set(channels.map((c: any) => c.threeLevel).filter(Boolean))];

      // 如果只有一个值，直接赋值；如果有多个值，转为逗号分隔的字符串
      queryData.oneLevel = oneLevels.length === 1 ? oneLevels[0] : oneLevels.length > 1 ? oneLevels.join(',') : undefined;
      queryData.twoLevel = twoLevels.length === 1 ? twoLevels[0] : twoLevels.length > 1 ? twoLevels.join(',') : undefined;
      queryData.threeLevel = threeLevels.length === 1 ? threeLevels[0] : threeLevels.length > 1 ? threeLevels.join(',') : undefined;
      console.log('最终查询参数:', { oneLevel: queryData.oneLevel, twoLevel: queryData.twoLevel, threeLevel: queryData.threeLevel }); // 调试信息
    } else {
      console.log('单选模式'); // 调试信息
      // 单选情况（向后兼容）
      queryData.oneLevel = queryForm.data[0] || undefined;
      queryData.twoLevel = queryForm.data[1] || undefined;
      queryData.threeLevel = queryForm.data[2] || undefined;
      console.log('单选查询参数:', { oneLevel: queryData.oneLevel, twoLevel: queryData.twoLevel, threeLevel: queryData.threeLevel }); // 调试信息
    }
  }

  let res = await queryRecharge_api(queryData);
  let {data, total} = res.data;
  rechargeTab.value = data;
  totalNum.value = total;
  fetchTotalWithdrawal(queryData);
}


const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = [
    new Date(past7daysStart),
    new Date(todayEnd),
  ]
  queryForm.startTime = past7daysStart
  queryForm.endTime = todayEnd
  rechargeTab.value = []
  formEl.resetFields()
  queryRecharge()
}
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  queryRech();
}
</script>

<style lang="scss" scoped>
.recharge {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .form-inline {
    :deep(.el-form-item__label) {
      width: 100px;
    }

    .el-form-item {
      width: 22%;

      .el-input {
        width: 215px;
      }

      // 优化级联选择器多选模式的显示
      .el-cascader {
        width: 215px;

        :deep(.el-cascader__tags) {
          max-height: 60px;
          overflow-y: auto;
        }
      }
    }

    .item_time {
      width: 28%;
    }
  }
}
</style>
