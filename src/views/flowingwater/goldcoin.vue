<template>
  <div class="goldcoin">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="流水操作类型" prop="operateType">
              <el-select class="header-select" v-model="queryForm.operateType" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="扣减" :value="0"/>
                <el-option label="增加" :value="1"/>
              </el-select>
            </el-form-item>
            <el-form-item label="为谁花的金币" prop="toUserId">
              <el-input v-model="queryForm.toUserId"/>
            </el-form-item>
            <el-form-item label="业务ID" prop="businessId">
              <el-input v-model="queryForm.businessId"/>
            </el-form-item>
            <el-form-item label="金币类型" prop="goldsType">
              <el-select class="header-select" v-model="queryForm.goldsType" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="赠送金币" :value="1"/>
                <el-option label="付费金币" :value="0"/>
              </el-select>
            </el-form-item>
            <el-form-item label="业务类型" prop="businessType">
              <el-select class="header-select" v-model="queryForm.businessType" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option v-for="v in goldsType" :key="v.code" :label="v.value" :value="v.key"/>
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择" prop="date">
              <el-date-picker v-model="date" value-format="x" :clearable="false" type="datetimerange"
                              range-separator="至"
                              start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="shortcuts"
                              size="default"
                              @change="timeChange"/>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryGold">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button class="ml" type="primary" @click="handleExportExcel">
                <el-icon style="vertical-align: middle">
                  <Download/>
                </el-icon>
                <span style="vertical-align: middle">导出Excel</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="goldcoinTab" style="width: 100%; height: 100%" v-loading="listLoading"
                  element-loading-text="正在努力生成Excel中，请耐心等待">
        <el-table-column prop="id" label="ID" min-width="100" fixed></el-table-column>
        <el-table-column prop="userId" label="用户ID" min-width="120">
          <template #default="scope">
            <div style="display: flex; align-items: center;">
              <span>{{ scope.row.userId }}</span>
              <sup v-if="scope.row.channel === 'vivo_vivo_vivo'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">vivo</sup>
              <sup v-else-if="scope.row.channel === 'huawei_huawei_huawei'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">华为</sup>
              <sup v-else-if="scope.row.channel === 'official_official_official'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">官方</sup>
              <sup v-else-if="scope.row.channel === 'oppo_oppo_oppo'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">oppo</sup>
              <sup v-else-if="scope.row.channel === 'new_new_new'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New</sup>
              <sup v-else-if="scope.row.channel === 'new1_new1_new1'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New1</sup>
              <sup v-else-if="scope.row.channel === 'new2_new2_new2'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">New2</sup>
              <sup v-else-if="scope.row.channel === 'dage_dage_dage'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">大哥</sup>
              <sup v-else-if="scope.row.channel === 'honor_honor_honor'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">荣耀</sup>
              <sup v-else-if="scope.row.channel === 'ying_ying_ying'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">YY宝</sup>
              <sup v-else-if="scope.row.channel === 'juliang_juliang_juliang'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">抖音</sup>
              <sup v-else-if="scope.row.channel === 'xiaomi_xiaomi_xiaomi'"
                   style="margin-left: 4px; font-size: 10px; color: #152b52;border: 1px solid #152b52;border-radius: 5px;">小米</sup>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="flowDesc" label="描述" min-width="240"></el-table-column>
        <el-table-column prop="time" label="创建时间" min-width="200">
          <template #default="scope">{{ TimestampToDate(scope.row.time) }}</template>
        </el-table-column>
        <el-table-column prop="operateType" label="流水操作类型" min-width="120">
          <template #default="scope">{{ scope.row.operateType == 0 ? "扣减" : "增加" }}</template>
        </el-table-column>
        <el-table-column prop="hasErrMark" label="是否异常" min-width="120">
          <template #default="scope">
            <span :class="scope.row.hasErrMark ? 'hasErrMark-on' : ''">{{
                scope.row.hasErrMark ? "是" : "否"
              }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="businessType" label="业务类型" min-width="150"></el-table-column>
        <el-table-column prop="beforeBalance" label="变动前金币数" min-width="200"></el-table-column>
        <el-table-column prop="amount" label="变动金币数" min-width="200"></el-table-column>
        <el-table-column prop="afterBalance" label="变动后金币数" min-width="200"></el-table-column>
        <el-table-column prop="toUserId" label="为谁花的金币" min-width="200"></el-table-column>
        <el-table-column prop="businessId" label="业务ID" min-width="200"></el-table-column>
        <el-table-column prop="goldsType" label="金币类型" min-width="200">
          <template #default="{row}">
            {{ row.goldsType == 1 ? "赠送金币" : "付费金币" }}
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, unref, reactive, onBeforeMount} from "vue";
import type {FormInstance} from 'element-plus'
import pageHook from "@/hooks/pageHook";
import {TimestampToDate} from "@/utils/date"
import {queryGolds_api, queryGoldsExecl_api} from "@/api/flowingwater"
import {queryBusinessType_api} from "@/api/wallet"
import {aoaToSheetXlsx} from "@/utils/excel";
import {ElMessage} from "element-plus";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(queryGold);

let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
let goldcoinTab = ref([]);
let date = ref<[Date, Date]>([
  new Date(past7daysStart),
  new Date(todayEnd),
])
let opNull = ref<any>(null)
let goldsType = ref<any>([]);
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
const queryFormRef = ref<FormInstance>()
let queryForm = reactive<any>({
  userId: null,
  operateType: null,
  businessType: null,
  startTime: past7daysStart,
  endTime: todayEnd,
  goldsType: null,
  businessId: null,
  toUserId: null,
  applicationId: 'com.dongxin.app'
})

async function queryGoldcoin() {
  let res = await queryGolds_api({
    startTime: past7daysStart,
    endTime: todayEnd,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId: queryForm.applicationId,
  })
  let {data, total} = res.data
  data.forEach((v: any) => {
    goldsType.value.forEach((item: any) => {
      if (v.businessType == item.code) {
        v.businessType = item.value
      }
    })
  })
  goldcoinTab.value = data
  totalNum.value = total
}

queryGoldcoin()
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

// 查询
async function queryGold() {
  let res = await queryGolds_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  })
  let {data, total} = res.data
  data.forEach((v: any) => {
    goldsType.value.forEach((item: any) => {
      if (v.businessType == item.code) {
        v.businessType = item.value
      }
    })
  })
  goldcoinTab.value = data
  totalNum.value = total
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = [
    new Date(past7daysStart),
    new Date(todayEnd),
  ]
  queryForm.startTime = null
  queryForm.endTime = null
  goldcoinTab.value = []
  formEl.resetFields()
  queryGoldcoin()
}

// 积分金币业务类型查询
const queryBusinessType = async () => {
  let res = await queryBusinessType_api()
  let {data} = res.data
  goldsType.value = data.goldsBusinessType
}
queryBusinessType()

// 表格相关
let timer: any = null
let listLoading = ref(false)
let listData = ref([])

// 导出Excel表格
const fileName = ref("金币流水");
const handleExportExcel = async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }

  listLoading.value = true;
  // //获取后端数据
  let res = await queryGoldsExecl_api({
    ...queryForm,
  })
  res.data.data.forEach((v: any) => {
    v.time = TimestampToDate(v.time)
    v.operateType == 0 ? v.operateType = "扣减" : v.operateType = "增加"
    v.hasErrMark ? v.hasErrMark = "是" : v.hasErrMark = "否"
  })

  listData.value = res.data.data

  let table = unref(listData.value); //取消数据响应
  let header = ["ID", "用户ID", "描述", "创建时间", "流水操作类型", "是否异常", "业务类型", "变动前金币数", "变动金币数", "变动后金币数", "充值金额"];
  let data = table.map((item: any, _index: any) => {
    let {
      id,
      userId,
      flowDesc,
      time,
      operateType,
      hasErrMark,
      businessType,
      beforeBalance,
      amount,
      afterBalance,
      price
    } = item;
    return [id, userId, flowDesc, time, operateType, hasErrMark, businessType, beforeBalance, amount, afterBalance, price];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });

  timer = setInterval(() => {
    listLoading.value = false;
  }, 1000)
};

onBeforeMount(() => {
  clearInterval(timer)
})
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  queryGold();
}
</script>

<style lang="scss" scoped>
.goldcoin {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .hasErrMark-on {
    color: #f00;
  }
}
</style>