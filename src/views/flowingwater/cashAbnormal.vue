<template>
  <div class="withdrawcash">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="el-form-header">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="审核状态" prop="status">
              <el-select class="header-select" v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="审核中" :value="0" />
                <el-option label="审核通过" :value="1" />
                <el-option label="审核失败" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryWithdrawcash">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>

            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="withdrawcashTab" style="width: 100%; height: 100%">
        <el-table-column prop="userId" label="用户ID" min-width="100"></el-table-column>
        <el-table-column prop="withdrawalTime" label="提现时间" min-width="100"></el-table-column>
        <el-table-column prop="price" label="提现金额" min-width="100"></el-table-column>
        <el-table-column prop="lastOperator" label="操作人" min-width="100"></el-table-column>
        <el-table-column prop="lastOperatorDate" label="最后审核时间" min-width="100"></el-table-column>
        <el-table-column prop="status" label="审核状态" min-width="120">
          <template #default="scope">{{ scope.row.status == 0 ? "审核中" :scope.row.status == 1 ? "审核通过": "审核失败" }}</template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="280">
          <template #default="scope"> 
            <el-button @click="editapply_pass(scope.row, 1)" size="small" type="primary"
            >审核通过</el-button>
            <el-button   @click="editapply_pass(scope.row, 2)" size="small" type="danger"
            >审核不通过</el-button>
         
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import pageHook from "@/hooks/pageHook";
import type { FormInstance } from 'element-plus'
import { getYMDHMS } from "@/utils/date"
import { withdrawal_illegal_list, modify_withdrawal_illegal } from "@/api/flowingwater"
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(queryWithdrawcash);
let withdrawcashTab = ref([])
const queryFormRef = ref<FormInstance>()

let queryForm = reactive<any>({
  userId: null,
  status:0,
  applicationId:'com.dongxin.app'
})
let opNull = ref<any>(null)


async function queryWithdrawcash() {
  let res = await withdrawal_illegal_list({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let { data, total } = res.data
  data.forEach((item:any) => {
    item.withdrawalTime=item.withdrawalTime?getYMDHMS('-',':',item.withdrawalTime):'';
    item.lastOperatorDate= item.lastOperatorDate?getYMDHMS('-',':',item.lastOperatorDate):'';
  });
  withdrawcashTab.value = data
  totalNum.value = total
}

// 操作

const editapply_pass=(row:any,status:number)=>{

  modify_withdrawal_illegal({userId:row.userId,status:status}).then((_res:any)=>{
    queryWithdrawcash();
  })
}


queryWithdrawcash();
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  queryWithdrawcash()
}
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  queryWithdrawcash();
}
</script>

<style lang="scss" scoped>
.withdrawcash {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    .el-form-header {
      width: 100%;
      overflow: hidden;
      box-sizing: border-box;

      :deep(.el-form-item__label) {
        width: 85px;
      }

      .el-form-item {
        width: 22%;

        .el-input {
          width: 215px;
        }
      }

      .item_time{
        width: 28%;
      }
    }
  }
}
</style>