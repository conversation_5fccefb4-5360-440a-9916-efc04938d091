<template>
  <div class="withdrawcash">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="el-form-header">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="收款帐号" prop="account">
              <el-input v-model="queryForm.account"/>
            </el-form-item>
            <el-form-item label="证件号" prop="idCard">
              <el-input v-model="queryForm.idCard"/>
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="queryForm.phone"/>
            </el-form-item>
            <el-form-item label="提现套餐ID" prop="packageId">
              <el-input v-model="queryForm.packageId"/>
            </el-form-item>
            <el-form-item label="ID" prop="id">
              <el-input v-model="queryForm.id"/>
            </el-form-item>
            <el-form-item label="提现平台" prop="platform">
              <el-select class="header-select" v-model="queryForm.platform" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="dalong" value="dalong"/>
              </el-select>
            </el-form-item>
            <el-form-item label="收款类型" prop="paymentType">
              <el-select class="header-select" v-model="queryForm.paymentType" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="支付宝" value="alipay"/>
                <el-option label="银行卡" value="bank"/>
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select class="header-select" v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="申请" value="apply"/>
                <el-option label="到账" value="received"/>
                <el-option label="驳回" value="reject"/>
                <!-- <el-option label="申请通过" value="apply_pass" /> -->
                <el-option label="申请打款中" value="apply_progress"/>
                <el-option label="到账失败" value="received_fail"/>
              </el-select>
            </el-form-item>
            <el-form-item label="申请时间" prop="date" class="item_time">
              <el-date-picker v-model="date" :clearable="true" type="daterange" value-format="x" range-separator="至"
                              start-placeholder="开始日期" end-placeholder="结束日期"
                              :shortcuts="shortcuts" size="default" @change="timeChange"/>
            </el-form-item>
            <el-form-item label="到账时间" prop="date" class="item_time">
              <el-date-picker v-model="date2" :clearable="true" type="daterange" value-format="x" range-separator="至"
                              start-placeholder="开始日期" end-placeholder="结束日期"
                              :shortcuts="shortcuts" size="default" @change="timeChange2"/>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="queryWithdrawcash">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button type="primary" :icon="Download" @click="handleExcel">导出Excel</el-button>
            </el-form-item>
          </el-form>

          <div><span>提现总金额 : {{ totalPrices }}</span> <span>提现总消耗积分: {{ totalPoints }}</span></div>


        </div>
      </template>
      <div class="table-container">
        <el-table :data="withdrawcashTab" style="width: 100%; height: 100%" v-loading="Loading">
          <el-table-column prop="id" label="ID" min-width="80" fixed></el-table-column>
          <el-table-column prop="userId" label="用户ID" min-width="100"></el-table-column>
          <el-table-column prop="name" label="收款人姓名" min-width="100"></el-table-column>
          <el-table-column prop="idCard" label="收款人证件号（身份证）" min-width="180"></el-table-column>
          <el-table-column prop="paymentType" label="提现收款类型" min-width="120">
            <template #default="scope">{{ scope.row.paymentType == "alipay" ? "支付宝" : "银行卡" }}</template>
          </el-table-column>
          <el-table-column prop="account" label="收款账号" min-width="180"></el-table-column>
          <el-table-column prop="status" label="状态" min-width="120">
            <template #default="scope">{{
                scope.row.status == "apply" ? "申请" : scope.row.status == "received" ? "到账"
                    : scope.row.status == "received_wait" ? "到账中" : scope.row.status == "apply_pass" ? "申请通过" : scope.row.status == "reject" ? "驳回" :
                        scope.row.status == "apply_progress" ? "申请打款中" : "到账失败"
              }}
            </template>
          </el-table-column>
          <el-table-column prop="packagePrice" label="提现套餐中的价格" min-width="180">
            <template #default="scope">{{ scope.row.packagePrice }}元</template>
          </el-table-column>
          <el-table-column prop="packagePoints" label="提现套餐中的所需积分" min-width="180"></el-table-column>
          <el-table-column prop="pointBalance" label="提现申请前积分余额" min-width="150"></el-table-column>
          <el-table-column prop="applyTime" label="申请时间" min-width="180">
            <template #default="scope">{{ getYMDHMS("-", ":", scope.row.applyTime) }}</template>
          </el-table-column>
          <el-table-column prop="receivedTime" label="到账时间" min-width="180">
            <template #default="scope">
              <span v-show="scope.row.receivedTime">{{ getYMDHMS("-", ":", scope.row.receivedTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="adminAccount" label="操作人" min-width="120"></el-table-column>
          <el-table-column prop="packageId" label="提现套餐ID" min-width="100"></el-table-column>
          <el-table-column prop="platform" label="提现平台" min-width="100"></el-table-column>
          <el-table-column prop="phone" label="收款人手机号" min-width="120"></el-table-column>
          <el-table-column prop="passTime" label="通过时间" min-width="180">
            <template #default="scope">
              <span v-show="scope.row.passTime">{{ getYMDHMS("-", ":", scope.row.passTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="rejectTime" label="驳回时间" min-width="180">
            <template #default="scope">
              <span v-show="scope.row.rejectTime">{{ getYMDHMS("-", ":", scope.row.rejectTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="失败原因" min-width="240"></el-table-column>
          <el-table-column prop="orderNo" label="订单号" min-width="200"></el-table-column>
          <el-table-column prop="tradeNo" label="（第三方）失败的原因" min-width="200"></el-table-column>
          <el-table-column fixed="right" label="操作" width="280">
            <template #default="scope">
              <div v-if="scope.row.status==='apply'">
                <el-button @click="editapply_pass(scope.row, 'apply_pass')" size="small" type="primary"
                           v-show="scope.row.status != 'received' && scope.row.status != 'apply_pass' && scope.row.status != 'reject' && scope.row.status != 'received_fail' && scope.row.status != 'received_wait'">
                  申请通过
                </el-button>
                <el-button @click="editreject(scope.row, 'reject')" size="small" type="danger"
                           v-show="scope.row.status != 'received' && scope.row.status != 'reject' && scope.row.status != 'received_wait' && scope.row.status != 'received_fail' ">
                  驳回
                </el-button>
                <el-popconfirm title="是否确认打款?" @confirm="editreceived(scope.row, 'received_wait')">
                  <template #reference>
                    <el-button size="small" type="success"
                               v-show="scope.row.status != 'received' && scope.row.status != 'reject' && scope.row.status != 'received_wait' && scope.row.status != 'received_fail'">
                      立即打款
                    </el-button>
                  </template>
                </el-popconfirm>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, unref ,onMounted} from "vue";
import pageHook from "@/hooks/pageHook";
import type {FormInstance} from 'element-plus'
import {Download} from "@element-plus/icons-vue";
import {getYMDHMS} from "@/utils/date"
import {ElMessage} from "element-plus";
import {aoaToSheetXlsx} from "@/utils/excel";
import {concurrencyRequest} from '@/utils/concurrencyRequest'
import {flattenArray} from '@/utils/list';
import {queryWithdrawal_api, withdrawalAudit_api,queryWithdrawalTotal_api} from "@/api/flowingwater"
import {past7daysStart, todayEnd} from '@/utils/date'


let totalPrices = ref(0);
let totalPoints = ref(0);


let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(queryWithdrawcash);
let withdrawcashTab = ref([])
const queryFormRef = ref<FormInstance>()
let opNull = ref<any>(null)
let date = ref<[Date, Date]>([
  new Date(past7daysStart),
  new Date(todayEnd),
])
let date2 = ref<any>(['', ''])
let Loading = ref(false);


const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
let queryForm = reactive<any>({
  id: null,
  userId: null,
  packageId: null,
  platform: null,
  paymentType: null,
  account: null,
  phone: null,
  idCard: null,
  status: null,
  startTime: past7daysStart,
  startReceivedTime: null,
  endTime: todayEnd,
  endReceivedTime: null,
  applicationId: 'com.dongxin.app'
})

async function queryWithdrawal() {
  let res = await queryWithdrawal_api({
    ...queryForm,
    applicationId: queryForm.applicationId,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  })
  let {data, total} = res.data
  withdrawcashTab.value = data
  totalNum.value = total
  // console.log(res);
}

queryWithdrawal()

async function fetchTotalWithdrawal(queryData = {}) {
  const res = await queryWithdrawalTotal_api(queryData);
  const data = res.data;
  console.log(data);  // 打印返回的数据
  if (data.code === 200 && data.success) {
    totalPrices.value = data.data.prices;
    totalPoints.value = data.data.points;
    console.log(totalPrices.value, totalPoints.value);  // 打印赋值后的变量
  }
}

onMounted(() => {
  fetchTotalWithdrawal();
});

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1] + 86399999;
};
const timeChange2 = (data: any) => {
  queryForm.startReceivedTime = data[0];
  queryForm.endReceivedTime = data[1] + 86399999;
};

async function queryWithdrawcash() {
  let queryData = {
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value
  };
  let res = await queryWithdrawal_api(queryData);
  let {data, total} = res.data;
  withdrawcashTab.value = data;
  totalNum.value = total;
  fetchTotalWithdrawal(queryData);
}

async function getKeys() {
  let queue: any = [];
  let pageNum = Math.ceil(totalNum.value / 10000);
  for (let i = 1; i <= pageNum; i++) {
    let urls = {
      data: {
        ...queryForm,
        pageNum: i,
        pageSize: 10000
      },
      url: '/admin/journal_account/withdrawal/query_withdrawal_execl'
    }

    queue.push(urls);
  }
  let res = await concurrencyRequest(queue, 2);
  return flattenArray(res);
}

// 导出Excel表格
const fileName = ref("提现管理");
const handleExcel = async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }
  Loading.value = true;
  //获取后端数据
  let res = await getKeys();

  Loading.value = false;
  let header = ['ID', '用户ID', '邀请人ID', '收款人姓名', '收款人证件号（身份证）', '提现收款类型', '收款账号', '状态', '提现套餐中的价格', '提现套餐中的所需积分', '提现申请前积分余额',
    '申请时间', '到账时间', '操作人', '提现套餐ID', '提现平台', '收款人手机号', '通过时间', '驳回时间', '失败原因', '订单号', '（第三方）失败的原因'];
  let listData = res;
  let datas = listData.map((item: any, _index: any) => {
    item.paymentType == "alipay" ? item.paymentType = "支付宝" : item.paymentType = "银行卡";
    item.status == "apply" ? item.status == "申请" : item.status == "received" ? item.status == "到账"
        : item.status.status == "received_wait" ? item.status == "到账中" : item.status == "apply_pass" ? item.status == "申请通过" : item.status == "reject" ? item.status == "驳回" :
            item.status == "到账失败"

    let {
      id,
      userId,
      bindInviteUserId,
      name,
      idCard,
      paymentType,
      account,
      status,
      packagePrice,
      packagePoints,
      pointBalance,
      applyTime,
      receivedTime,
      adminAccount,
      packageId,
      platform,
      phone,
      passTime,
      rejectTime,
      reason,
      orderNo,
      tradeNo
    } = item;
    return [id, userId, bindInviteUserId, name, idCard, paymentType, account, status, packagePrice, packagePoints, pointBalance, applyTime, receivedTime, adminAccount, packageId, platform, phone, passTime, rejectTime, reason, orderNo, tradeNo];
  });
  aoaToSheetXlsx({
    data: datas,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });
};
// 操作
const editreceived = async (row: any, received: any) => {
  await withdrawalAudit_api({id: row.id, status: received})
  queryWithdrawcash()
}
const editapply_pass = async (row: any, apply_pass: any) => {
  await withdrawalAudit_api({id: row.id, status: apply_pass})
  queryWithdrawcash()
}
const editreject = async (row: any, reject: any) => {
  await withdrawalAudit_api({id: row.id, status: reject})
  queryWithdrawcash()
}


const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = [
    new Date(past7daysStart),
    new Date(todayEnd),]
  date2.value = ['', '']
  queryForm.startReceivedTime = null;
  queryForm.endReceivedTime = null;
  queryForm.startTime = past7daysStart;
  queryForm.endTime = todayEnd;
  formEl.resetFields()
  queryWithdrawal()
}
const changeSelect = (e: string) => {
  queryForm.applicationId = e;
  queryWithdrawcash();
}
</script>

<style lang="scss" scoped>
.withdrawcash {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .card-header {
      .el-form-header {
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;

        :deep(.el-form-item__label) {
          width: 85px;
        }

        .el-form-item {
          width: 22%;

          .el-input {
            width: 215px;
          }
        }

        .item_time {
          width: 28%;
        }
      }
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }
}
</style>