<template>
  <div class="channel">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId"
                         :skipFirstSelect="false"
                         @changeSelect="handleAppChange"
                         style="margin-right: 20px;"/>
            </el-form-item>
            <el-form-item label="渠道选择">

              <el-cascader
                v-model="queryForm.data"
                :options="options"
                :props="{
                  label: 'channelName',
                  children: 'childChannels',
                  value: 'channelCode',
                }"
                clearable
                placeholder="请选择渠道">
              </el-cascader>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :shortcuts="shortcuts" size="default" @change="timeChange" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="margin-left: 20px;" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button class="ml" type="primary" @click="handleExportExcel">
                    <el-icon style="vertical-align: middle">
                      <Download />
                    </el-icon>
                    <span style="vertical-align: middle">导出Excel</span>
                  </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="uselistRef" :data="dataList" style="width: 100%; height: 100%" v-loading="listLoading"
          highlight-current-row stripe>
        <template v-for="v in newChannelColumn" :key="v.prop">
          <el-table-column v-if="v.prop != 'investmentAmount' && v.prop != 'downloadCnt'" :prop="v.prop" :label="v.title"
            :width="v.minWidth" :fixed="v.fixed">
            <template slot="header" #header="_scope">
              <span>{{ v.title }}</span>
              <el-tooltip class="box-item" v-if="v.text" effect="dark" :content="v.text" placement="top-start">
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <template #default="{ row }">
              {{ newChannelData(row, v.prop, v.type) }}
            </template>
          </el-table-column>
          <el-table-column v-else-if="v.prop == 'investmentAmount'" prop="investmentAmount" label="推广消耗" min-width="100">
            <template #default="scope">
              <span v-if="!scope.row.isShow">{{ scope.row.investmentAmount ? scope.row.investmentAmount : 0 }}</span>
              <el-input v-else v-model="scope.row.investmentAmount" />
            </template>
          </el-table-column>
          <el-table-column v-else-if="v.prop == 'downloadCnt'" prop="downloadCnt" label="下载数" min-width="100">
            <template #default="scope">
              <span v-if="!scope.row.isShow">{{ scope.row.downloadCnt ? scope.row.downloadCnt : 0 }}</span>
              <el-input v-else v-model="scope.row.downloadCnt" />
            </template>
          </el-table-column>
        </template>
        <el-table-column label="当日最新roi" width="180" prop="latestRoiVo" >
          <template #default="{row}">
            <span style="user-select: none;"  v-if="row.latestRoiVo?.paySumAmount"  :style="row?.latestRoiVo?.roiRate>1?'color:red':'' "  >累计金额：{{row?.latestRoiVo?.paySumAmount}}</span>
              <br style="user-select: none;" />
              <span style="user-select: none;" v-if="row.latestRoiVo?.roiRate" :style="row?.latestRoiVo?.roiRate>1?'color:red':''  " >roi：
               {{(row?.latestRoiVo?.roiRate*100).toFixed(2)}}%
                </span>
          </template>
        </el-table-column>
        <el-table-column label="次1留" width="180" prop="retention1" >
          <template #default="{row}">
              <span style="user-select: none;" v-if="row.retention1?.loginUserCount">活跃人数：{{ row.retention1.loginUserCount.toFixed() }}</span>
              <br style="user-select: none;" />
              <span style="user-select: none;" v-if="row.retention1?.retentionRate">
              留存：{{ (row.retention1?.retentionRate*100).toFixed(2) }}%</span>
            </template>
        </el-table-column>
        <el-table-column label="次3留" width="180" prop="retention3" >
          <template #default="{row}">
              <span style="user-select: none;" v-if="row.retention3?.loginUserCount">活跃人数：{{ row.retention3.loginUserCount.toFixed() }}</span>
              <br style="user-select: none;" />
              <span style="user-select: none;" v-if="row.retention3?.retentionRate">
              留存：{{ (row.retention3?.retentionRate*100).toFixed(2) }}%</span>
            </template>
        </el-table-column>
        <el-table-column label="次7留" width="180" prop="retention7" >
          <template #default="{row}">
              <span style="user-select: none;" v-if="row.retention7?.loginUserCount">活跃人数：{{ row.retention7.loginUserCount.toFixed() }}</span>
              <br style="user-select: none;" />
              <span style="user-select: none;" v-if="row.retention7?.retentionRate">
              留存：{{ (row.retention7?.retentionRate*100).toFixed(2) }}%</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button @click="onEdit(scope.row)" :disabled="!IsEdit" size="small"
              :type="scope.row.isShow ? 'success' : 'primary'">{{
                scope.row.isShow ? "完成" : "编辑" }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, reactive, onBeforeMount } from "vue";
import { newChannelColumn } from "@/views/datadetail/columnData/newChannelColumn";
import { queryNew_channel_details, saveChannelData_api, query_channel_list } from "@/api/datadetail";
import { QuestionFilled, Download } from "@element-plus/icons-vue";
import { getYMD } from '@/utils/date'
import { newChannelData } from '@/hooks/newChannelTableHook'
import type { FormInstance } from 'element-plus'
import { aoaToSheetXlsx } from "@/utils/excel";
import majaSelect from "@/components/majaSelect.vue";
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
// //最近30天
const queryFormRef = ref<FormInstance>()
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
let date = ref<[Date, Date]>([
  new Date(past7daysStart),
  new Date(todayEnd),
])
let options = ref<any>();
let queryForm = reactive<any>({
  startTime: past7daysStart,
  endTime: todayEnd,
  data: [],
  date: [],
  applicationId:'com.dongxin.app'
})
let IsEdit = false;
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
//查询筛选列表
async function query_list() {
  try {
    let res = await query_channel_list();
    let { data } = res.data

    if (data && Array.isArray(data)) {
      // 对第三层渠道进行排序
      data.forEach((item: any) => {
        if (item.childChannels && Array.isArray(item.childChannels)) {
          item.childChannels.forEach((secondLevel: any) => {
            if (secondLevel.childChannels && Array.isArray(secondLevel.childChannels)) {
              try {
                secondLevel.childChannels.sort((a: any, b: any) => {
                  const nameA = a.channelName || '';
                  const nameB = b.channelName || '';
                  return nameA.localeCompare(nameB);
                });
              } catch (sortError) {
                console.warn('排序第三层渠道时出错:', sortError, secondLevel);
              }
            }
          });
        }
      });

      options.value = data;
    } else {
      console.error('渠道数据格式错误:', data);
      options.value = [];
    }
  } catch (error) {
    console.error('获取渠道列表失败:', error);
    options.value = [];
  }
}


query_list();



let dataList = ref([])
let timer: any = null


const dateString = (timestamp: any) => {
  const date = new Date(timestamp);
  const year = date.getFullYear(); // 获取年份
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份，并补零
  const day = String(date.getDate()).padStart(2, '0'); // 获取日期，并补零
  return `${year}-${month}-${day}`;
}
dateString(toData)
//查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function query() {
  let params = {};
  if (queryForm.data) {
    params = {
      oneLevel: queryForm?.data[0],
      twoLevel: queryForm?.data[1],
      threeLevel: queryForm?.data[2],
      beginDate: getYMD('-', queryForm?.startTime),
      endDate: getYMD('-', queryForm?.endTime),
      applicationId: queryForm?.applicationId,
    }
  } else {
    params = {
      beginDate: getYMD('-', queryForm?.startTime),
      endDate: getYMD('-', queryForm?.endTime),
      applicationId: queryForm?.applicationId,
    }
  }
  if (queryForm?.data && queryForm?.data.length >= 3) {
    IsEdit = true;
  }
  const { data: { data } } = await queryNew_channel_details({ ...params })
  let sum:any = {
    registerDate: '总计',
    channel: '总计',
    newActiveRegisterPayCost: 0,
    newRegisterPayCost: 0,
    newActiveRegisterPayCnt: 0,
    newRegisterPayCnt: 0,
    investmentAmount: 0,
    downloadCnt: 0,
    downloadCost: 0,
    maleLoginCnt: 0,
    femaleLoginCnt: 0,
    sumLoginCht: 0,
    activeCnt: 0,
    activeCost: 0,
    downloadActiveConversion: 0,
    registerCnt: 0,
    registerCost: 0,
    maleRegisterCnt: 0,
    femaleRegisterCnt: 0,
    activeRegisterConversion: 0,
    newUserPayAmount: 0,
    oldUserPayAmount: 0,
    payAmount: 0,
    newUserPayCnt: 0,
    oldUserPayCnt: 0,
    payCnt: 0,
    newPayRate: 0,
    firstPayAmount:0,
    firstPayCnt:0,
    newPayCost: 0,
    newUserArpu: 0,
    oldUserArpu:0,
    newUserArppu: 0,
    oldUserArppu: 0,
    newActiveUserPayAmount:0,
    secondPayCnt:0,
    avgFirstPayMinutes:0,
    avgSecondPayMinutes:0,
    latestRoiVo:{
      roiRate:0,
      paySumAmount:0,
    },
    retention1:{
      retentionRate:0,
      loginUserCount:0,
    },
    retention3:{
      retentionRate:0,
      loginUserCount:0,
    },
    retention7:{
      retentionRate:0,
      loginUserCount:0,
    },
  }
  let retention1Length:number=0;
  let retention3Length:number=0;
  let retention7Length:number=0;
  data.forEach((item: any) => {
    item.isShow = false
    item.sumLoginCht = item.femaleLoginCnt + item.maleLoginCnt
    if (item.newActiveRegisterPayCost) sum.newActiveRegisterPayCost += item.newActiveRegisterPayCost/data.length;
    if (item.newRegisterPayCost) sum.newRegisterPayCost += item.newRegisterPayCost/data.length;
    if (item.newActiveRegisterPayCnt) sum.newActiveRegisterPayCnt += item.newActiveRegisterPayCnt;
    if (item.newRegisterPayCnt) sum.newRegisterPayCnt += item.newRegisterPayCnt;
    if (item.investmentAmount) sum.investmentAmount += item.investmentAmount;
    if (item.downloadCnt) sum.downloadCnt += item.downloadCnt;
    if (item.downloadCost) sum.downloadCost += item.downloadCost/data.length;
    if (item.maleLoginCnt) sum.maleLoginCnt += item.maleLoginCnt;
    if(item.newActiveUserPayAmount) sum.newActiveUserPayAmount += item.newActiveUserPayAmount;
    if (item.femaleLoginCnt) sum.femaleLoginCnt += item.femaleLoginCnt;
    if(item.firstPayAmount) sum.firstPayAmount += item.firstPayAmount;
    if (item.sumLoginCht) sum.sumLoginCht += item.sumLoginCht;
    if(item.firstPayCnt) sum.firstPayCnt += item.firstPayCnt;
    if (item.activeCnt) sum.activeCnt += item.activeCnt;
    if (item.oldUserArpu) sum.oldUserArpu += item.oldUserArpu/data.length;
    if (item.activeCost) sum.activeCost += item.activeCost/data.length;
    if (item.downloadActiveConversion) sum.downloadActiveConversion += item.downloadActiveConversion/data.length;
    if (item.registerCnt) sum.registerCnt += item.registerCnt;
    if (item.registerCost) sum.registerCost += item.registerCost/data.length;
    if (item.maleRegisterCnt) sum.maleRegisterCnt += item.maleRegisterCnt;
    if (item.femaleRegisterCnt) sum.femaleRegisterCnt += item.femaleRegisterCnt;
    if (item.activeRegisterConversion) sum.activeRegisterConversion += item.activeRegisterConversion/data.length;
    if (item.newUserPayAmount) sum.newUserPayAmount += item.newUserPayAmount;
    if (item.oldUserPayAmount) sum.oldUserPayAmount += item.oldUserPayAmount;
    if (item.payAmount) sum.payAmount += item.payAmount;
    if (item.newUserPayCnt) sum.newUserPayCnt += item.newUserPayCnt;
    if (item.oldUserPayCnt) sum.oldUserPayCnt += item.oldUserPayCnt;
    if (item.payCnt) sum.payCnt += item.payCnt;
    if (item.newPayRate) sum.newPayRate += item.newPayRate/data.length;
    if (item.newPayCost) sum.newPayCost += item.newPayCost/data.length;
    if (item.newUserArpu) sum.newUserArpu += item.newUserArpu/data.length;
    if (item.newUserArppu) sum.newUserArppu += item.newUserArppu/data.length;
    if (item.oldUserArppu) sum.oldUserArppu += item.oldUserArppu/data.length;
    if (item.secondPayCnt) sum.secondPayCnt += item.secondPayCnt;
    if (item.avgFirstPayMinutes) sum.avgFirstPayMinutes += item.avgFirstPayMinutes/data.length;
    if (item.avgSecondPayMinutes) sum.avgSecondPayMinutes += item.avgSecondPayMinutes/data.length;
    if(item.latestRoiVo?.roiRate){
      sum.latestRoiVo.roiRate += item.latestRoiVo.roiRate;
    }
    if(item.latestRoiVo?.paySumAmount)sum.latestRoiVo.paySumAmount += item.latestRoiVo.paySumAmount;
    if(item.retention3?.loginUserCount)sum.retention3.loginUserCount += item.retention3?.loginUserCount;
    if(item.retention7?.loginUserCount)sum.retention7.loginUserCount += item.retention7?.loginUserCount;
    if(item.retention1?.retentionRate){
      retention1Length++;
      sum.retention1.retentionRate += item.retention1?.retentionRate
    };
    if(item.retention1?.loginUserCount)sum.retention1.loginUserCount += item.retention1?.loginUserCount;
    if(item.retention3?.retentionRate){
      retention3Length++;
      sum.retention3.retentionRate += item.retention3?.retentionRate
    };
    if(item.retention7?.retentionRate){
      retention7Length++;
      sum.retention7.retentionRate += item.retention7?.retentionRate
    };
  })

  sum.newActiveRegisterPayCost=sum.newActiveRegisterPayCost.toFixed(2);
  sum.newRegisterPayCost=(sum.newRegisterPayCost.toFixed(2));
  sum.investmentAmount=(sum.investmentAmount.toFixed(2));
  sum.downloadCost=(sum.downloadCost.toFixed(2));
  sum.activeCost=(sum.activeCost.toFixed(2));
  sum.registerCost=(sum.registerCost.toFixed(2));
  sum.newPayCost=(sum.newPayCost.toFixed(2));
  sum.newUserArpu=(sum.newUserArpu.toFixed(2));
  sum.oldUserArppu=(sum.oldUserArppu.toFixed(2));
  sum.oldUserArpu=(sum.oldUserArpu.toFixed(2));
  sum.newUserArppu=(sum.newUserArppu.toFixed(2));
  sum.avgFirstPayMinutes=(sum.avgFirstPayMinutes.toFixed(2));
  sum.avgSecondPayMinutes=(sum.avgSecondPayMinutes.toFixed(2));
  sum.latestRoiVo.roiRate=((sum.latestRoiVo.paySumAmount/sum.investmentAmount).toFixed(2));
  sum.retention1.retentionRate=(sum.retention1.retentionRate/retention1Length)
  sum.retention1.loginUserCount=(sum.retention1.loginUserCount/retention1Length)
  sum.retention3.retentionRate=(sum.retention3.retentionRate/retention3Length)
  sum.retention3.loginUserCount=(sum.retention3.loginUserCount/retention3Length)
  sum.retention7.retentionRate=(sum.retention7.retentionRate/retention7Length)
  sum.retention7.loginUserCount=(sum.retention7.loginUserCount/retention7Length)
  data.push(sum);
  dataList.value = data
}


query();

//重置
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = [
    new Date(past7daysStart),
    new Date(todayEnd),
  ]
  queryForm.data = []
  queryForm.startTime = past7daysStart,
    queryForm.endTime = todayEnd,
    query();
}


//编辑
const onEdit = async (row: any) => {
  row.isShow = !row.isShow;
  const time = new Date(row.registerDate);
  if (row.isShow==false) {
    await saveChannelData_api({
      id: row.id,
      dateTime: time.getTime(),
      channel: row.channel,
      applicationId: queryForm?.applicationId,
      popularize: row.investmentAmount!=0?row.investmentAmount.toString().replace(/\,/g,'')*1:row.investmentAmount,
      downNum: row.downloadCnt!=0? row.downloadCnt.toString().replace(/\,/g,'')*1: row.downloadCnt,
    });
    query()
  }
};

// 表格相关
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});




let { listLoading } = toRefs(state);

onBeforeMount(() => {
  clearInterval(timer)
})

const handleExportExcel = async () => {
  let header:Array<string>=[];
  for (const key in newChannelColumn) {
    header.push(newChannelColumn[key].title)
  }

  let data=dataList.value.map((item:any)=>{
   const {registerDate,channel,newActiveRegisterPayCost,newRegisterPayCost,newActiveRegisterPayCnt,newRegisterPayCnt,investmentAmount,downloadCnt,downloadCost,maleLoginCnt,femaleLoginCnt,sumLoginCht,activeCnt,activeCost,downloadActiveConversion,registerCnt,registerCost,maleRegisterCnt,femaleRegisterCnt,activeRegisterConversion,newUserPayAmount,newActiveUserPayAmount,oldUserPayAmount,payAmount, newUserPayCnt,oldUserPayCnt,firstPayCnt,secondPayCnt,avgFirstPayMinutes,avgSecondPayMinutes,firstPayAmount,payCnt,newPayRate,newPayCost,newUserArpu,oldUserArpu,newUserArppu, oldUserArppu}=item;
  return[registerDate,channel,newActiveRegisterPayCost,newRegisterPayCost,newActiveRegisterPayCnt,newRegisterPayCnt,investmentAmount,downloadCnt,downloadCost,maleLoginCnt,femaleLoginCnt,sumLoginCht,activeCnt,activeCost,downloadActiveConversion,registerCnt,registerCost,maleRegisterCnt,femaleRegisterCnt,activeRegisterConversion,newUserPayAmount,newActiveUserPayAmount,oldUserPayAmount,payAmount, newUserPayCnt,oldUserPayCnt,firstPayCnt,secondPayCnt,avgFirstPayMinutes,avgSecondPayMinutes,firstPayAmount,payCnt,newPayRate,newPayCost,newUserArpu,oldUserArpu,newUserArppu, oldUserArppu]
  })

  aoaToSheetXlsx({
    data,
    header,
    filename: `渠道详情.xlsx`,
  });


};

// 添加处理马甲包改变的函数
const handleAppChange = (e: string) => {
  queryForm.applicationId = e;
  query();
};
</script>

<style lang="scss" scoped>
.channel {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }
  }

  .card-header {

    .mb-header {
      display: flex;

      :deep(.el-range-editor) {
        margin-left: 20px;
        flex: 1;
      }

      .el-input {
        margin-left: 20px;
        flex: 1;
      }

      .el-button {
        margin-left: 20px;
      }
    }
  }

}
</style>
