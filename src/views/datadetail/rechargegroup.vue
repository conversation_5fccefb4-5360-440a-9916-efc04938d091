<template>
  <div class="rechargegroup">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
              <!-- <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item> -->
              <el-form-item label="是否排序" prop="hasSort">
                <el-select v-model="queryForm.hasSort" placeholder="请选择">
                  <el-option label="否" :value="false" />
                  <el-option label="是" :value="true" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间选择" prop="date">
                <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                  format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  :shortcuts="shortcuts" size="default" @change="timeChange" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="query">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
              <el-form-item>
                <div class="mb-header">
                  <el-button class="ml" type="primary" @click="handleExportExcel">
                    <el-icon style="vertical-align: middle">
                      <Download />
                    </el-icon>
                    <span style="vertical-align: middle">导出Excel</span>
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <tables :tabData="listData" :tabHeader="rechargegroupColumn" :Loading="listLoading" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, unref, reactive, onBeforeMount } from "vue";
import tables from "@/components/tables.vue";
import { getYMDHMS } from "@/utils/date"
import { rechargegroupColumn } from "@/views/datadetail/columnData/rechargegroupColumn";
import { queryreChargeAmountAnalyst_api } from "@/api/datadetail";
import { Download } from "@element-plus/icons-vue";
import type { FormInstance } from 'element-plus'
import { aoaToSheetXlsx } from "@/utils/excel";
import { ElMessage } from "element-plus";

let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间

let date = ref<[Date, Date]>([
  new Date(todayStart),
  new Date(todayEnd),
])
const queryFormRef = ref<FormInstance>()
let queryForm = reactive<any>({
  startTime: todayStart,
  endTime: todayEnd,
  hasSort: false,
  // applicationId:'com.dongxin.app'
})
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

let dataList = ref([])
let timer: any = null

//查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function query() {
  let res = await queryreChargeAmountAnalyst_api({ ...queryForm })
  let { data } = res.data
  data.forEach((v: any) => {
    if (v.gender == 1) {
      v.gender = "男"
    }
    if (v.gender == 2) {
      v.gender = "女"
    }
    if (v.firstRechargeTime) v.firstRechargeTime = getYMDHMS("-", ":", v.firstRechargeTime)
    if (v.registerTime) v.registerTime = getYMDHMS("-", ":", v.registerTime)
  })
  listData.value = data
}
query()
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = [
    new Date(todayStart),
    new Date(todayEnd),
  ]
  queryForm.startTime = todayStart
  queryForm.endTime = todayEnd
  formEl.resetFields()
  query()
}

// 表格相关
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});
// 导出Excel表格
const fileName = ref("充值金额分组");
const handleExportExcel = () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }

  listLoading.value = true;

  //标题数组
  let titleArr = Object.values(rechargegroupColumn).map((v: any) => v.title)

  let table = unref(state.listData); //取消数据响应
  let header = titleArr;
  let data = table.map((item: any, _index: any) => {
    let { time, userId, nickname, chanelName, gender, rechargeAmount, rechargeDetail, firstRechargeTime,registerTime } = item;
    return [time, userId, nickname, chanelName, gender, rechargeAmount, rechargeDetail, firstRechargeTime, registerTime];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });

  timer = setInterval(() => {
    listLoading.value = false;
  }, 1000)
};

let { listData, listLoading } = toRefs(state);

onBeforeMount(() => {
  clearInterval(timer)
})
// const changeSelect=(e:string)=>{
//   queryForm.applicationId=e;
//   query();
// }
</script>

<style lang="scss" scoped>
.rechargegroup {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }
  }

  .card-header {
    display: flex;

    .mb-header {
      display: flex;
      width: 300px;

      .el-button {
        margin-left: 20px;
        // width: 100%;
        // margin-top: 10px;
      }
    }
  }

}
</style>