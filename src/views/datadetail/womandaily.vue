<template>
  <div class="womandaily">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form
              ref="queryFormRef"
              :inline="true"
              :model="queryForm"
              class="demo-form-inline"
            >
              <!-- <el-form-item label="是否排序" prop="hasSort">
                <el-select v-model="queryForm.hasSort" placeholder="请选择">
                  <el-option label="否" :value="false" />
                  <el-option label="是" :value="true" />
                </el-select>
              </el-form-item> -->
              <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
              <el-form-item label="用户ID">
                <el-input v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item label="时间选择" prop="date">
                <el-date-picker
                  v-model="date"
                  :clearable="false"
                  type="daterange"
                  unlink-panels
                  value-format="x"
                  format="YYYY-MM-DD"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :shortcuts="shortcuts"
                  size="default"
                  @change="timeChange"
                />
              </el-form-item>
              <el-form-item class="el-form-itembtn">
                <el-button type="primary" @click="query">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
              <el-form-item>
                <div class="mb-header">
                  <el-button class="ml" type="primary" @click="handleExportExcel" :disabled="listLoading" > 
                    <el-icon style="vertical-align: middle">
                      <Download />
                    </el-icon>
                    <span style="vertical-align: middle">导出Excel</span>
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table_main">
        <Tables
          :tabData="listData"
          :tabHeader="womandailyColumn"
          :Loading="listLoading"
          :elementLoadingText="elementLoadingText"
        />
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, unref, reactive } from "vue";
import Tables from "@/components/womanTables.vue";
import { ChangeDecimalToPercentage } from "@/utils/percentialize";
import { womandailyColumn } from "@/views/datadetail/columnData/womandailyColumn";
import { getYMDHMS, getYMD, secondsToHms,time } from "@/utils/date";
import { queryWoManUserAnalyst_api } from "@/api/datadetail";
import { Download } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import { aoaToSheetXlsx } from "@/utils/excel";
import { ElMessage } from "element-plus";
import {flattenArray} from '@/utils/list';
import pageHook from "@/hooks/pageHook";
import {concurrencyRequest} from '@/utils/concurrencyRequest'
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  query
);
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
let date = ref<[Date, Date]>([new Date(todayStart), new Date(todayEnd)]);
const queryFormRef = ref<FormInstance>();
let elementLoadingText=ref("");
let queryForm = reactive<any>({
  userId: null,
  beginDate: getYMD("-", todayStart),
  endDate: getYMD("-", todayEnd),
  applicationId:'com.dongxin.app'
  // hasSort: false,
});
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

let dataList = ref([]);
// 表格相关
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});

//查询
const timeChange = (data: any) => {
  queryForm.beginDate = getYMD("-", data[0]);
  queryForm.endDate = getYMD("-", data[1]);
};

async function query() {
  state.listLoading = true;
  elementLoadingText.value='加载中'
  let res = await queryWoManUserAnalyst_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data,total } = res.data;
  data.forEach((v: any) => {
    v.createTime = getYMDHMS("-", ":", v.createTime);
    v.videoGiftsScale = ChangeDecimalToPercentage(v.videoGiftsScale);
    v.isInviter = v.isInviter ? "是" : "否";
    v.giftRate = v.giftRate ? (v.giftRate * 100).toFixed(2) + "%" : "0%";
    v.isOnline = v.isOnline ? "在线" : "离线";
    v.registerTime=time(v.registerTime/1000);
    v.fateChatIncome = v.fateChatIncome ? v.fateChatIncome : 0;
    v.onlineDuration = secondsToHms(v.onlineDuration);
    v.unionVideoRate = v.unionVideoRate
      ? (v.unionVideoRate * 100).toFixed(2) + "%"
      : "0%";
    v.sayHelloManPeopleRate = v.sayHelloManPeopleRate
      ? (v.sayHelloManPeopleRate * 100).toFixed(2) + "%"
      : "0%";
      v.replyReplyMoleculeRate=v.replyReplyMolecule?((v.replyReplyMolecule/v.replyReplyDenominator)*100).toFixed(2) + "%": "0%";
  });
  state.listLoading = false;
  totalNum.value=total;
  listData.value = data;
}
query();
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  date.value = [new Date(todayStart), new Date(todayEnd)];
  queryForm.beginDate = getYMD("-", todayStart);
  queryForm.endDate = getYMD("-", todayEnd);

  formEl.resetFields();
  query();
};

async function getKeys() {
  let queue:any = [];
      let pageNum=Math.ceil(totalNum.value/10000);
        for (let i=1; i<=pageNum; i++){
          let urls={
          data:{  ...queryForm,
            pageNum:i,
            pageSize:10000},
            url:'/admin/data_analyst/woman_user_analyst_v2/query' }
        
          queue.push(urls);
        }
  let res = await concurrencyRequest(queue, 2);
    return flattenArray(res);
	}


 
// 导出Excel表格
const fileName = ref("女性日报");
const handleExportExcel = async() => {
  
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }
  elementLoadingText.value="正在努力生成Excel中，请耐心等待";
  listLoading.value = true;
  let res = await getKeys();
  res.forEach((v: any) => {
    v.createTime = getYMDHMS("-", ":", v.createTime);
    v.videoGiftsScale = ChangeDecimalToPercentage(v.videoGiftsScale);
    v.isInviter = v.isInviter ? "是" : "否";
    v.registerTime=time(v.registerTime/1000);
    v.giftRate = v.giftRate ? (v.giftRate * 100).toFixed(2) + "%" : "0%";
    v.isOnline = v.isOnline ? "在线" : "离线";
    v.fateChatIncome = v.fateChatIncome ? v.fateChatIncome : 0;
    v.onlineDuration = secondsToHms(v.onlineDuration);
    v.unionVideoRate = v.unionVideoRate
      ? (v.unionVideoRate * 100).toFixed(2) + "%"
      : "0%";
    v.sayHelloManPeopleRate = v.sayHelloManPeopleRate
      ? (v.sayHelloManPeopleRate * 100).toFixed(2) + "%"
      : "0%";
  });


  //标题数组
  let titleArr = Object.values(womandailyColumn).map((v: any) => v.title);

  let header = titleArr;
  header[0]="开始时间";
  header.splice(1,0,'结束时间');
  header.push("状态")
  header.push("真人认证状态")
  header.push("实名认证状态")
  let data = res.map((item: any, _index: any) => {
    item.replyMaleMsgCntPercent= (item.replyMaleMsgCnt!=0? ((item.replyMaleMsgCntLess3Minutes/item.replyMaleMsgCnt)*100).toFixed(2):'0')+'%';
    item.replyReplyMoleculeRate=item.replyReplyMolecule?((item.replyReplyMolecule/item.replyReplyDenominator)*100).toFixed(2) + "%": "0%";
    switch (item.status) {
      case 0:item.status="正常"
        break;
      case 1:item.status="已注销"
        break;
      case 2:item.status="被封禁"
        break;
      case 3:item.status="临时封禁"
        break;
      default:
        break;
    }
    switch (item.realAvatarAuthState) {
      case 0:item.realAvatarAuthState="未提交"
        break;
      case 1:item.realAvatarAuthState="认证审核中"
        break;
      case 2:item.realAvatarAuthState="认证成功"
        break;
      case 3:item.realAvatarAuthState="认证失败"
        break;
      default:
        break;
    }
    switch (item.realNameAuthState) {
      case 0:item.realNameAuthState="未提交"
        break;
      case 1:item.realNameAuthState="认证审核中"
        break;
      case 2:item.realNameAuthState="认证成功"
        break;
      case 3:item.realNameAuthState="认证失败"
        break;
      default:
        break;
    }
    let {
      time,
      timeEnd,
      userId,
      isInviter,
      nickname,
      bindInviteUserId,
      channel,
      grade,
      charmVal,
      charmLevel,
      isOnline,
      voiceEarningsDiamondNum,
      inviteSumIncome,
      videoEarningsDiamondNum,
      giftsEarningsDiamondNum,
      freeVideoCardIncome,
      fateChatIncome,
      fateGiftIncome,
      msgCardIncome,
      // intimacyBagIncome,
      otherEarningsDiamondNum,
      weekActivityIncome,
      todayIncome,
      chatEarningsDiamondNum,
      onlineDuration,
      voiceCalledPartyNum,
      voiceAnswerNum,
      voiceAnswerNumManCall,
      voiceInitiativeNum,
      voiceInitiativePeopleNum,
      videoCalledPartyNum,
      videoAnswerNum,
      videoAnswerNumManCall,
      videoInitiativeNum,
      videoInitiativePeopleNum,
      personalLetterNum,
      personalLetterPeopleNum,
      dynamicNum,
      constructionUnionPeopleNum,
      initiativeIMPeopleNum,
      videoGiftsScale,
      sayHelloManNum,
      matchPeopleNum,
      // sayHelloManRtnAvg,
      // userConversionRatio,
      replyMaleMsgCntPercent,
      replyReplyMoleculeRate,
      registerTime,
      status,
      realAvatarAuthState,
      realNameAuthState
    } = item;
    return [
      time,
      timeEnd,
      userId,
      isInviter,
      nickname,
      bindInviteUserId,
      channel,
      grade,
      charmVal,
      charmLevel,
      isOnline,
      voiceEarningsDiamondNum,
      inviteSumIncome,
      videoEarningsDiamondNum,
      giftsEarningsDiamondNum,
      freeVideoCardIncome,
      fateChatIncome,
      fateGiftIncome,
      msgCardIncome,
      // intimacyBagIncome,
      otherEarningsDiamondNum,
      weekActivityIncome,
      todayIncome,
      chatEarningsDiamondNum,
      onlineDuration,
      voiceCalledPartyNum,
      voiceAnswerNum,
      voiceAnswerNumManCall,
      voiceInitiativeNum,
      voiceInitiativePeopleNum,
      videoCalledPartyNum,
      videoAnswerNum,
      videoAnswerNumManCall,
      videoInitiativeNum,
      videoInitiativePeopleNum,
      personalLetterNum,
      personalLetterPeopleNum,
      dynamicNum,
      constructionUnionPeopleNum,
      initiativeIMPeopleNum,
      videoGiftsScale,
      sayHelloManNum,
      matchPeopleNum,
      // sayHelloManRtnAvg,
      // userConversionRatio,
      replyMaleMsgCntPercent,
      replyReplyMoleculeRate,
      registerTime,
      status,
      realAvatarAuthState,
      realNameAuthState
    ];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });

    listLoading.value = false;
};

let { listData, listLoading } = toRefs(state);

const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}
</script>

<style lang="scss" scoped>
.womandaily {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .box-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table_main {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .card-header {
      display: flex;

      .mb-header {
        display: flex;
        width: 300px;

        .el-button {
          margin-left: 20px;
        }
      }
    }
  }
}
</style>
