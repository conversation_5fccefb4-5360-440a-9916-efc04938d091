<template>
  <div class="channel">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="queryForm"
            class="demo-form-inline"
          >
            <el-form-item>
              <majaSelect 
                :applicationId="queryForm.applicationId" 
                :skipFirstSelect="false"
                @changeSelect="handleAppChange"
                style="margin-right: 20px;"
              />
            </el-form-item>
            <el-form-item label="渠道选择">
              <el-cascader
                popper-class="cascaderRadio"
                v-model="queryForm.data"
                :options="options"
                :show-all-levels="false"
                :props="{
                  expandTrigger: 'hover',
                  label: 'channelName',
                  children: 'childChannels',
                  value: 'channelCode',
                }"
                clearable
              >
              </el-cascader>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker
                v-model="date"
                :clearable="false"
                type="daterange"
                unlink-panels
                value-format="x"
                format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                size="default"
                @change="timeChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="margin-left: 20px" @click="query"
                >查询</el-button
              >
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button class="ml" type="primary" @click="handleExportExcel">
                <el-icon style="vertical-align: middle">
                  <Download />
                </el-icon>
                <span style="vertical-align: middle">导出Excel</span>
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table
          ref="uselistRef"
          :data="dataList"
          style="width: 100%; height: 100%"
          v-loading="listLoading"
        >
        <el-table-column
          prop="date"
          min-width="120"
          label="日期"
          fixed="left"
        ></el-table-column>
        <el-table-column prop="channel" min-width="180" label="媒体"></el-table-column>
        <el-table-column
          prop="investmentAmount"
          min-width="100"
          label="消耗(现金)"
        ></el-table-column>
        <el-table-column prop="registerCnt" label="新增注册"></el-table-column>
        <el-table-column prop="registerCost" label="注册成本">
          <template slot="header" #header="_scope">
            <span>注册成本</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="注册成本 = 消耗/注册人数"
              placement="top-start"
            >
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="payRate" min-width="100" label="当天付费率(注册)">
          <template #default="{ row }"> {{ (row.payRate * 100).toFixed(2) }}% </template>
        </el-table-column>
        <el-table-column prop="payCost" label="当天付费成本" min-width="130">
          <template slot="header" #header="_scope">
            <span>当天付费成本</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content=" 当天付费成本 = 消耗/当日付费人数"
              placement="top-start"
            >
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="{ row }"> {{ row.payCost.toFixed(2) }} </template>
        </el-table-column>
        <el-table-column
          prop="payRetention1Cnt"
          label="付费次1留人数（前日） "
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="registerRetention1Cnt"
          label="注册次1留(前日)"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="roi0" min-width="100" label="当日roi">
          <template slot="header" #header="_scope">
            <span>当日roi</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="当日roi = 当日充值/消耗"
              placement="top-start"
            >
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="{ row }">
            {{ ChangeDecimalToPercentage(row.roi0) }}
          </template>
        </el-table-column>
        <el-table-column prop="roiLatest" min-width="100" label="当月roi（最新roi）">
          <template slot="header" #header="_scope">
            <span>当月roi（最新roi）</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="最新roi = 当月充值/当月消耗"
              placement="top-start"
            >
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="{ row }">
            {{ ChangeDecimalToPercentage(row.roiLatest) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="payMonthAmount"
          min-width="100"
          label="当月充值"
        ></el-table-column>
        <el-table-column
          prop="payDayAmount"
          min-width="100"
          label="当日充值"
        ></el-table-column>
        <el-table-column
          prop="payCnt"
          min-width="100"
          label="当日付费人数"
        ></el-table-column>
        <el-table-column prop="arppu" min-width="100" label="ARPPU"></el-table-column>
        <el-table-column
          prop="monthInvestmentAmount"
          min-width="100"
          label="当月总消耗"
        ></el-table-column>
        <el-table-column prop="giftIncomeRate" min-width="100" label="礼物占比">
          <template #default="{ row }">
            {{ ChangeDecimalToPercentage(row.giftIncomeRate) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="giftGoldsAmount"
          min-width="100"
          label="礼物金币流水"
        ></el-table-column>
        <el-table-column
          prop="totalGoldsAmount"
          min-width="100"
          label="总金币流水"
        ></el-table-column>
        <el-table-column
          prop="femaleActiveCnt"
          min-width="100"
          label="女用户活跃人数"
        ></el-table-column>
        <el-table-column
          prop="femaleActiveIncome100Cnt"
          min-width="100"
          label="100元以上人数"
        ></el-table-column>
      </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, reactive, unref } from "vue";
import { channel_report_api, query_channel_list } from "@/api/datadetail";
import { getYMD } from "@/utils/date";
import { ChangeDecimalToPercentage } from "@/utils/percentialize";
import type { FormInstance } from "element-plus";
import { aoaToSheetXlsx } from "@/utils/excel";
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
// //最近30天
const queryFormRef = ref<FormInstance>();
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近1天
let past7daysStart = toData - 1 * 3600 * 24 * 1000;
let date = ref<[Date, Date]>([new Date(past7daysStart), new Date(todayEnd)]);
let options = ref<any>();
let queryForm = reactive<any>({
  startTime: past7daysStart,
  endTime: todayEnd,
  data: [],
  date: [],
  applicationId: 'com.dongxin.app'
});
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
//查询筛选列表
async function query_list() {
  let res = await query_channel_list();
  let { data } = res.data;
  data.forEach((item: any) => {
    if (item.childChannels) {
      item.childChannels.forEach((v: any) => {
        v.childChannels.sort((a: { channelName: string }, b: { channelName: any }) =>
          a.channelName.localeCompare(b.channelName)
        );
      });
    }
  });

  options.value = data;
}

query_list();

let dataList = ref([]);

const dateString = (timestamp: any) => {
  const date = new Date(timestamp);
  const year = date.getFullYear(); // 获取年份
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 获取月份，并补零
  const day = String(date.getDate()).padStart(2, "0"); // 获取日期，并补零
  return `${year}-${month}-${day}`;
};
dateString(toData);
//查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function query() {
  let params = {};
  if (queryForm.data) {
    params = {
      oneLevel: queryForm?.data[0],
      twoLevel: queryForm?.data[1],
      threeLevel: queryForm?.data[2],
      beginDate: getYMD("-", queryForm?.startTime),
      endDate: getYMD("-", queryForm?.endTime),
      applicationId:queryForm.applicationId,
    };
  } else {
    params = {
      beginDate: getYMD("-", queryForm?.startTime),
      endDate: getYMD("-", queryForm?.endTime),
      applicationId:queryForm.applicationId,
    };
  }
  const {
    data: { data },
  } = await channel_report_api({ ...params });
  dataList.value = data;
}
query();
//重置
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  date.value = [new Date(past7daysStart), new Date(todayEnd)];
  queryForm.data = [];
  (queryForm.startTime = past7daysStart), (queryForm.endTime = todayEnd), query();
};

// 表格相关
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});
// const changeSelect=(e:string)=>{
//   queryForm.applicationId=e;
//   query();
// }
let { listLoading } = toRefs(state);

const handleExportExcel = async () => {
  let table = unref(dataList); //取消数据响应
  let header: any = [
    "日期",
    "媒体",
    "消耗(现金)",
    "新增注册",
    "注册成本",
    "当天付费率(注册)",
    "当天付费成本",
    "付费次1留人数（前日）",
    "注册次1留(前日)",
    "当日roi",
    "当月roi（最新roi）",
    "当月充值",
    "当日充值",
    "当日付费人数",
    "当月总消耗",
    "礼物占比",
    "礼物金币流水",
    "总金币流水",
    "女用户人数",
    "100元以上人数",
  ];
  let data = table.map((item: any, _index: any) => {
    let {
      date,
      channel,
      investmentAmount,
      registerCnt,
      payRate,
      payCost,
      payRetention1Cnt,
      registerRetention1Cnt,
      roi0,
      roiLatest,
      payMonthAmount,
      payDayAmount,
      payCnt,
      arppu,
      monthInvestmentAmount,
      giftIncomeRate,
      giftGoldsAmount,
      totalGoldsAmount,
      femaleActiveCnt,
      femaleActiveIncome100Cnt,
    } = item;
    return [
      date,
      channel,
      investmentAmount,
      registerCnt,
      payRate,
      payCost,
      payRetention1Cnt,
      registerRetention1Cnt,
      roi0,
      roiLatest,
      payMonthAmount,
      payDayAmount,
      payCnt,
      arppu,
      monthInvestmentAmount,
      giftIncomeRate,
      giftGoldsAmount,
      totalGoldsAmount,
      femaleActiveCnt,
      femaleActiveIncome100Cnt,
    ];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `渠道日报.xlsx`,
  });
};

const handleAppChange = (e: string) => {
  queryForm.applicationId = e;
  query();
};
</script>

<style lang="scss" scoped>
.channel {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }
  }

  .card-header {
    .mb-header {
      display: flex;

      :deep(.el-range-editor) {
        margin-left: 20px;
        flex: 1;
      }

      .el-input {
        margin-left: 20px;
        flex: 1;
      }

      .el-button {
        margin-left: 20px;
      }
    }
  }
  .leaf {
    :deep(.el-range-editor) {
      display: none;
    }
  }
}
</style>
