<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
         <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <!-- <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item> -->
            <el-form-item label="渠道选择">
              <el-cascader popper-class="cascaderRadio" v-model="queryForm.data" :options="options"
                :props="{ expandTrigger: 'hover', label: 'channelName', children: 'childChannels', value: 'channelCode', checkStrictly: true }"
                clearable>
                <template #default="{ node, data }">
                  <span class="custom-node leaf" v-if="node.isLeaf == 0" @mouseenter="mouseenterLeaf(node)">{{
                    data.channelName }}</span>
                  <span v-else class="custom-node noLeaf" @mouseenter="mouseenterSubcat(node)">{{ data.channelName
                  }}</span>
                </template>
              </el-cascader>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              size="default" @change="timeChange" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="margin-left: 20px;" @click="query(true)">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="listData" style="width: 100%; height: 100%"  :border="true"  ref="tableRef"  default-expand-all>
        <el-table-column label="注册时间" fixed="left" min-width="120" prop="date" />
        <el-table-column label="投入金额" fixed="left" min-width="120">
          <template #default="scope">
            <!-- <el-input-number :controls="false" style="width: 70px;" v-model="scope.row.investmentAmount"
              v-if="scope.$index === index && isShow" @blur="(e) => handleChange(e, scope)" /> -->
            <div style="cursor: pointer;" @click="handleClick(scope)" >{{ scope.row.investmentAmount }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="实时roi" min-width="150">
            <template #default="{row}">
              累计金额： {{ row.realtimeRoi.paySumAmount}}
              <br/>
               roi： {{ (row.realtimeRoi.roiRate*100).toFixed(2) }}%
            </template>
          </el-table-column> -->
        <template v-for="(v, index) in listColmun" :key="v.prop">
          <el-table-column v-if="v.show" :label="v.title"  :min-width="v.minWidth">
            <template #default="scope">
              <span style="user-select: none;" :style="roi_active(scope.row?.channelRoiVOS, index-1)?'color:red':'' " v-if="paySumAmount_num(scope.row?.channelRoiVOS, index-1) " >累计金额：{{
                paySumAmount_num(scope.row?.channelRoiVOS, index-1) }}</span>
              <br style="user-select: none;" />
              <span style="user-select: none;" :style="roi_active(scope.row?.channelRoiVOS, index-1)?'color:red':'' " v-if="roi_data(scope.row?.channelRoiVOS, index-1) ">roi：{{
                roi_data(scope.row?.channelRoiVOS, index-1) }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { queryChannel_roi_list2, query_channel_list } from "@/api/datadetail"
import pageHook from "@/hooks/pageHook";
import { roi_data, paySumAmount_num ,roi_active} from '@/hooks/numdata';
import{todayEnd,past30daysStart,getYMD} from '@/utils/date'
const listColmun = reactive<any>([
]);
let listData = ref([])
let isShow=ref<boolean>(false);
  let date = ref<[Date, Date]>([
  new Date(past30daysStart),
  new Date(todayEnd),
])
let queryForm = reactive<any>({
  beginDate: past30daysStart,
  endDate: todayEnd,
  data:[],
  // applicationId:'com.dongxin.app'
});
let options = ref<any>();
let index = ref<any>(null);
  // let tableHeight = ref(); // 已移除，使用CSS布局
  let tableRef=ref<any>();
// 移除动态高度计算，使用CSS布局
// onMounted(()=>{
//   tableHeight.value = window.innerHeight - tableRef.value.$el.offsetTop - 150;
//   window.onresize = () => {
//     tableHeight.value = window.innerHeight - tableRef.value.$el.offsetTop - 150;
//   };
// })

let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(query);


//查询
async function query(e=isShow.value) {
  let params = {};
  if (queryForm.data) {
    params = {
      oneLevel: queryForm.data[0],
      twoLevel: queryForm.data[1],
      threeLevel: queryForm.data[2],
      beginDate: getYMD('-', queryForm?.beginDate),
      endDate: getYMD('-', queryForm?.endDate),
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      applicationId:queryForm.applicationId,
    }
  } else {
    params = {
      beginDate: getYMD('-', queryForm?.beginDate),
      endDate: getYMD('-', queryForm?.endDate),
      applicationId:queryForm.applicationId,
      pageSize: pageSize.value,
      pageNum: currentPage.value
    }
  }
    
  let res = await queryChannel_roi_list2(params);
  let { data, total } = res.data
  totalNum.value = total
  listData.value = data;
  isShow.value=e;
  index.value =null;
}
query();



const timeChange = (data: any) => {
  queryForm.beginDate = data[0];
  queryForm.endDate = data[1];
};

const onreset = () => {
  queryForm.data = [];
  date.value = [
    new Date(past30daysStart),
    new Date(todayEnd),
  ]
  queryForm.beginDate = past30daysStart,
    queryForm.endDate = todayEnd,
  query(false);
}

const handleClick = (e: any) => {
  index.value = e.$index;
}

// const handleChange = (e: any, scope: any) => {
//   console.log(scope.row);
  
//   const params = {
//     investmentAmount: e.target.value*1,
//     date: scope.row.date,
//     oneLevel: queryForm?.value[0],
//     twoLevel: queryForm?.value[1],
//     threeLevel: queryForm?.value[2],
//   }
//   queryChannel_roi(params).then(() => {
//     query(true);
//   })
//   index.value =null;
// }

function mouseenterSubcat(node: any) {
  let el_node = document.querySelectorAll('.el-popper.el-cascader__dropdown.specialCascaderRadio .el-cascader-panel .el-cascader-menu');
  if (el_node[node.level] && el_node.length > 0) {
    (el_node[node.level] as HTMLElement).style.display = "none";
    cascaderRecursion(el_node, node)
  }
}

function mouseenterLeaf(node: any) {
  let el_node = document.querySelectorAll('.el-popper.el-cascader__dropdown.specialCascaderRadio .el-cascader-panel .el-cascader-menu');
  if (el_node[node.level] && el_node.length > 0) {
    (el_node[node.level] as HTMLElement).style.display = "block";
  }

}

function cascaderRecursion(el_node: any, node: any) {
  function handle(i: any) {
    if (el_node[node.level + i]) {
      el_node[node.level + i].style.display = "none";
      i++
      handle(i)
    } else {
      return
    }
  }
  handle(1)
}



//查询筛选列表
async function query_list() {
  let res = await query_channel_list();
  let { data } = res.data
  options.value = data;

}
query_list();


function createTablePopper() {
  for (let index = 0; index < 61; index++) {
    let item={};
    if(index===0){
      item = {
      prop: `roi${index}`,
      title: `今日`,
      show: true,
      minWidth: "150",
    }
    }else{
      item = {
      prop: `roi${index}`,
      title: `roi${index}`,
      show: true,
      minWidth: "150",
    }
    }  
    listColmun.push(item)
  }
}


createTablePopper();
// const changeSelect=(e:string)=>{
//   queryForm.applicationId=e;
//   query();
// }
</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>