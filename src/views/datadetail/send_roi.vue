<template>
  <div class="send_roi">
    <el-card class="box-card">
      <template #header>
        <el-form :inline="true" :model="queryForm" class="demo-form-inline">
          <el-form-item label="时间选择">
            <el-date-picker
              v-model="date"
              :clearable="false"
              unlink-panels
              value-format="x"
              format="YYYY-MM-DD"
              start-placeholder="开始日期"
              size="default"
              @change="timeChange"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="query">查询</el-button>
            <el-button @click="onreset(queryFormRef)">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
      <div class="table-container">
        <el-table :data="TableLists" style="width: 100%; height: 100%" v-loading="Loading">
        <el-table-column prop="time" label="召回日期" min-width="60"></el-table-column>
        <el-table-column
          prop="offlineDay"
          label="未登录天数"
          min-width="60"
        ></el-table-column>
        <el-table-column
          prop="sendUserCount"
          label="发送用户数量"
          min-width="60"
        ></el-table-column>
        <el-table-column
          prop="amountSpent"
          label="花费金额"
          min-width="60"
        ></el-table-column>
        <el-table-column prop="recallCount" label="回归数量" min-width="60">
          <template #default="{ row }">
            {{ row.recallCount ? row.recallCount : 0 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="recallSpent"
          label="回归付费金额"
          min-width="60"
        ></el-table-column>
        <el-table-column prop="roi" label="roi" min-width="60">
          <template #default="{ row }"> {{ row.roi }}% </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { query_jm_send_recall_roi } from "@/api/index";
import { ref, reactive } from "vue";
import { getYMD } from "@/utils/date";
import pageHook from "@/hooks/pageHook";
import type { FormInstance } from "element-plus";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  query
);
const date = ref<any>();
const Loading = ref<boolean>(false);
const queryForm = reactive<any>({
  date: null,
});
const queryFormRef = ref<FormInstance>();
let TableLists = ref([]);

const onreset = (_formEl: FormInstance | undefined) => {
  (queryForm.date = null), (date.value = null), query();
};

async function query() {
  Loading.value = true;
  query_jm_send_recall_roi({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  }).then((res) => {
    TableLists.value = res.data.data;
    totalNum.value = res.data.total;
    Loading.value = false;
  });
}

const timeChange = (data: any) => {
  queryForm.date = getYMD("-", data);
};
query();
</script>

<style lang="scss" scoped>
.send_roi {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;
}

.send_roi .el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.send_roi :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
  position: relative;
  overflow: hidden;
}

.table-container {
  flex: 1;
  overflow: auto;
  min-height: 0;
}

.send_roi .el-pagination {
  margin-top: auto;
  margin-bottom: 0;
  background-color: #fff;
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  z-index: 10;
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
