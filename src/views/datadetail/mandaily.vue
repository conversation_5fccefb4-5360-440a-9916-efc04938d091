<template>
  <div class="mandaily">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
              <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
              <el-form-item label="用户ID">
                <el-input v-model="queryForm.userId" />
              </el-form-item>
              <!-- <el-form-item label="是否排序" prop="hasSort">
                <el-select v-model="queryForm.hasSort" placeholder="请选择">
                  <el-option label="否" :value="false" />
                  <el-option label="是" :value="true" />
                </el-select>
              </el-form-item> -->
              <el-form-item label="时间选择" prop="date">
                <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                  format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  :shortcuts="shortcuts" size="default" @change="timeChange" />
              </el-form-item>
              <el-form-item class="el-form-itembtn">
                <el-button type="primary" @click="query">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
              <el-form-item>
                <div class="mb-header">
                  <el-button class="ml" type="primary" @click="handleExportExcel">
                    <el-icon style="vertical-align: middle">
                      <Download />
                    </el-icon>
                    <span style="vertical-align: middle">导出Excel</span>
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <ManTables :tabData="listData" :tabHeader="mandailyColumn" :Loading="listLoading" @handleShowPeople="handleShowPeople" />
      </div>

      <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :small="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalNum"
          :background="true"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
    <el-dialog
      v-model="isShow"
      :title="isShowTitle"
      destroy-on-close
      @close="isShow = false"
      width="15%"
      custom-class="menu-dialog-height"
    >
      <el-table :data="tabData" max-height="500px">
        <el-table-column prop="userId" label="ID">
          <template #default="scope">
            <span @click="handleTo(scope.row)" style="cursor: pointer">
              {{ scope.row }}</span
            ></template
          >
        </el-table-column>
      </el-table>
    </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">

import { toRefs, ref, unref, reactive, onBeforeMount } from "vue";
import ManTables from "@/components/manTables.vue";
import { mandailyColumn } from "@/views/datadetail/columnData/mandailyColumn";
import { getYMDHMS } from "@/utils/date";
import { queryManUserAnalyst_api } from "@/api/datadetail"
import { Download } from "@element-plus/icons-vue";
import { ElMessage, type FormInstance } from 'element-plus';
import { aoaToSheetXlsx } from "@/utils/excel";
import pageHook from "@/hooks/pageHook";
import {concurrencyRequest} from '@/utils/concurrencyRequest'
import {flattenArray} from '@/utils/list';
import { useRouter } from "vue-router";
import userIdStore from "@/store/userIdStore";
let store = userIdStore();
let router = useRouter();
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  query
);
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
// 表格相关
let isShow = ref<any>(false);
let isShowTitle= ref<any>();
let tabData =ref<any>([]);
let date = ref<[Date, Date]>([
  new Date(todayStart),
  new Date(todayEnd),
])
const queryFormRef = ref<FormInstance>()
let queryForm = reactive<any>({
  startTime: todayStart,
  endTime: todayEnd,
  hasSort: true,
  userId: null,
  applicationId: 'com.dongxin.app'
})

const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

let dataList = ref([])
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});

let timer: any = null

//查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

async function query() {
  state.listLoading=true;
  let res = await queryManUserAnalyst_api({ ...queryForm, 
    pageSize: pageSize.value,
    pageNum: currentPage.value, })
  let { data,total } = res.data
  data.forEach((v: any) => {
    v.createTime = getYMDHMS("-", ":", v.createTime);
    v.isInviter = v.isInviter ? '是' : '否';
    v.wealthVal=v.wealthVal?v.wealthVal:0;
    v.inviteSumIncome=v.inviteSumIncome?v.inviteSumIncome:0;
    v.income=v.income?v.income:0;
    v.voiceAndNum=v.voiceAndNum?v.voiceAndNum:0;
    v.voiceAcceptNum=v.voiceAcceptNum?v.voiceAcceptNum:0;
    v.voiceActiveNum=v.voiceActiveNum?v.voiceActiveNum:0;
    v.videoAndNum=v.videoAndNum?v.videoAndNum:0;
    v.videoAcceptNum=v.videoAcceptNum?v.videoAcceptNum:0;
    v.videoActiveNum=v.videoActiveNum?v.videoActiveNum:0;
    v.sendLetterCount=v.sendLetterCount?v.sendLetterCount:0;
    v.sendLetterPeopleNum=v.sendLetterPeopleNum?v.sendLetterPeopleNum:0;
    v.sendLetterRtnAvg=v.sendLetterRtnAvg?v.sendLetterRtnAvg:0;
    v.dynamicNum=v.dynamicNum?v.dynamicNum:0;
    v.connectedNum=v.connectedNum?v.connectedNum:0;
    v.sendLetterRtnAvg=v.sendLetterRtnAvg?v.sendLetterRtnAvg+'%':'0%';
    v.sendLetterThreeRtnAvg=v.sendLetterThreeRtnAvg?v.sendLetterThreeRtnAvg+'%':'0%';
    v.registerTime = getYMDHMS("-", ":", v.registerTime);
  })
  state.listLoading=false;
  totalNum.value=total;
  listData.value = data
}
query()

async function getKeys() {
  let queue:any = [];
      let pageNum=Math.ceil(totalNum.value/10000);
        for (let i=1; i<=pageNum; i++){
          let urls={
          data:{  ...queryForm,
            pageNum:i,
            pageSize:10000},
            url:'/admin/data_analyst/man_user_analyst/query' }
        
          queue.push(urls);
        }
  let res = await concurrencyRequest(queue, 2);
    return flattenArray(res);
	}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  date.value = [
    new Date(todayStart),
    new Date(todayEnd),
  ]
  queryForm.startTime = todayStart
  queryForm.endTime = todayEnd
  queryForm.userId = null;

  formEl.resetFields()
  query()
}

// 导出Excel表格
const fileName = ref("男性日报");
const handleExportExcel =async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }

  listLoading.value = true;
  let res = await getKeys();
  res.forEach((v: any) => {
    v.createTime = getYMDHMS("-", ":", v.createTime);
    v.isInviter = v.isInviter ? '是' : '否';
    v.wealthVal=v.wealthVal?v.wealthVal:0;
    v.inviteSumIncome=v.inviteSumIncome?v.inviteSumIncome:0;
    v.income=v.income?v.income:0;
    v.voiceAndNum=v.voiceAndNum?v.voiceAndNum:0;
    v.voiceAcceptNum=v.voiceAcceptNum?v.voiceAcceptNum:0;
    v.voiceActiveNum=v.voiceActiveNum?v.voiceActiveNum:0;
    v.videoAndNum=v.videoAndNum?v.videoAndNum:0;
    v.videoAcceptNum=v.videoAcceptNum?v.videoAcceptNum:0;
    v.videoActiveNum=v.videoActiveNum?v.videoActiveNum:0;
    v.sendLetterCount=v.sendLetterCount?v.sendLetterCount:0;
    v.sendLetterPeopleNum=v.sendLetterPeopleNum?v.sendLetterPeopleNum:0;
    v.sendLetterRtnAvg=v.sendLetterRtnAvg?v.sendLetterRtnAvg:0;
    v.dynamicNum=v.dynamicNum?v.dynamicNum:0;
    v.connectedNum=v.connectedNum?v.connectedNum:0;
    v.sendLetterRtnAvg=v.sendLetterRtnAvg?v.sendLetterRtnAvg+'%':'0%';
    v.sendLetterThreeRtnAvg=v.sendLetterThreeRtnAvg?v.sendLetterThreeRtnAvg+'%':'0%';
    v.registerTime = getYMDHMS("-", ":", v.registerTime);
  })
  //标题数组
  let titleArr = Object.values(mandailyColumn).map((v: any) => v.title)
  let header = titleArr;
  let data = res.map((item: any, _index: any) => {
    let { userId,isInviter, nickname,registerTime,time, channelName, onlineDuration, payAmount,wealthVal, wealthLevel,inviteSumIncome,voiceAndNum,voiceAcceptNum,voiceActiveNum,videoAndNum,videoAcceptNum,videoActiveNum,sendLetterCount,connectedNum,sendLetterRtnAvg,sendLetterPeopleNum,hearbeatMatchCount,toSendLetterPeopleNum,hitOnMeCount,sendLetterThreeRtnAvg,dynamicNum} = item;
    return [userId,isInviter, nickname,registerTime,time, channelName, onlineDuration, payAmount,wealthVal, wealthLevel,inviteSumIncome,voiceAndNum,voiceAcceptNum,voiceActiveNum,videoAndNum,videoAcceptNum,videoActiveNum,sendLetterCount,connectedNum,sendLetterRtnAvg,sendLetterPeopleNum,hearbeatMatchCount,toSendLetterPeopleNum,hitOnMeCount,sendLetterThreeRtnAvg,dynamicNum];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });

  timer = setInterval(() => {
    listLoading.value = false;
  }, 1000)
};

let { listData, listLoading } = toRefs(state);

onBeforeMount(() => {
  clearInterval(timer)
})
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}
const handleShowPeople =(e:any,row:any)=>{
  if(e.prop==='connectedNum'){
    //建联人数
    isShow.value = true;
    isShowTitle.value='建联人数';
    tabData.value=row.connectedSet;
  }else if (e.prop==='sendLetterPeopleNum'){
    isShow.value = true;
    isShowTitle.value = '私信人数';
    tabData.value=row.sendLetterPeopleSet;
  }
}
const handleTo = (e: number) => {
  store.userId = e;
  router.push({
    path: "/foreground_user/foregrounduser",
  });
};
</script>

<style lang="scss" scoped>
.mandaily {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    display: flex;

    .mb-header {
      display: flex;
      width: 300px;

      .el-button {
        margin-left: 20px;
        // width: 100%;
        // margin-top: 10px;
      }
    }
  }

}
</style>