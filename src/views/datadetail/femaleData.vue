<template>
  <div class="channel">
    <el-card class="box-card">
      <template #header>
        <majaSelect :applicationId="applicationId" @changeSelect="changeSelect"/>
      </template>
      <div class="table-container">
        <el-table ref="uselistRef" :data="dataList" style="width: 100%; height: 100%" v-loading="listLoading" highlight-current-rowstripe>
        <template v-for="item in femaleDataColumn" :key="item.prop">
          <el-table-column :prop="item.prop" :label="item.title" :min-width="item.minWidth">
            <template #default="{row}">
           <span v-if="!item.isAvg">{{row[item.prop]?row[item.prop]:0}}</span>
           <span v-else>{{row[item.prop]?row[item.prop]+'%':'0%'}}</span>
            </template>
          </el-table-column>
       </template>
      </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup >
import majaSelect from '@/components/majaSelect.vue'
import { toRefs, ref, reactive, onBeforeMount } from "vue";
import { femaleDataColumn } from "@/views/datadetail/columnData/femaleDataColumn";
import {query_woman_data} from "@/api/index";
let applicationId=ref('com.dongxin.app')
let dataList = ref([])
let timer = null
// 表格相关
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});

const query=async()=>{
  state.listLoading=true;
 let {data:{data}} = await query_woman_data({applicationId:applicationId.value});
 dataList.value=data;
 state.listLoading=false;
}
query();




let { listLoading } = toRefs(state);

onBeforeMount(() => {
  clearInterval(timer)
})

const changeSelect=(e)=>{
  applicationId.value=e;
  query();
}

</script>

<style lang="scss" scoped>
.channel {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }
  }

  .card-header {

    .mb-header {
      display: flex;

      :deep(.el-range-editor) {
        margin-left: 20px;
        flex: 1;
      }

      .el-input {
        margin-left: 20px;
        flex: 1;
      }

      .el-button {
        margin-left: 20px;
      }
    }
  }

}
</style>