<template>
  <div class="illegallist">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <!-- <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item> -->
            <el-form-item label="渠道选择">
              <el-cascader
                popper-class="cascaderRadio"
                v-model="queryForm.data"
                :options="options"
                :props="{
                  expandTrigger: 'hover',
                  label: 'channelName',
                  children: 'childChannels',
                  value: 'channelCode',
                  checkStrictly: true,
                }"
                clearable
              >
                <template #default="{ node, data }">
                  <span
                    class="custom-node leaf"
                    v-if="node.isLeaf == 0"
                    @mouseenter="mouseenterLeaf(node)"
                    >{{ data.channelName }}</span
                  >
                  <span
                    v-else
                    class="custom-node noLeaf"
                    @mouseenter="mouseenterSubcat(node)"
                    >{{ data.channelName }}</span
                  >
                </template>
              </el-cascader>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker
                v-model="date"
                :clearable="false"
                type="daterange"
                unlink-panels
                value-format="x"
                format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                size="default"
                @change="timeChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" style="margin-left: 20px" @click="query"
                >查询</el-button
              >
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table
          :data="listData"
          style="width: 100%; height: 100%"
          :border="true"
          ref="tableRef"
          v-loading="loading"
        >
        <el-table-column label="注册时间" fixed="left" min-width="120" prop="date" />
        <el-table-column label="当日" min-width="180" prop="regSumCount">
          <template #default="scope">
            <span style="user-select: none"
              >付费人数
              <el-tooltip
                class="box-item"
                effect="dark"
                content="当天付费的"
                placement="top-start"
              >
                <el-icon>
                  <QuestionFilled />
                </el-icon> </el-tooltip
              >：{{ scope.row.retentionVOS[0].todayPayUserSize }}</span
            ><br />
            <span style="user-select: none"
              >新增付费人数<el-tooltip
                class="box-item"
                effect="dark"
                content="当天是首充付费的"
                placement="top-start"
              >
                <el-icon>
                  <QuestionFilled />
                </el-icon> </el-tooltip
              >：{{ scope.row.retentionVOS[0].newPayUserSize }} </span
            ><br />
            <span style="user-select: none"
              >付费总人数<el-tooltip
                class="box-item"
                effect="dark"
                content="截止当前的最新付费总人数"
                placement="top-start"
              >
                <el-icon>
                  <QuestionFilled />
                </el-icon> </el-tooltip
              >：{{ scope.row.retentionVOS[0].currentPayUserSize }} </span
            ><br />
            <span style="user-select: none"
              >付费留存率<el-tooltip
                class="box-item"
                effect="dark"
                content="付费人数/付费总人数"
                placement="top-start"
              >
                <el-icon>
                  <QuestionFilled />
                </el-icon> </el-tooltip
              >：{{ (scope.row.retentionVOS[0].retention * 100).toFixed(2) + "%" }}</span
            ><br />
          </template>
        </el-table-column>
        <template v-for="(v, index) in listColmun" :key="v.prop">
          <el-table-column
            v-if="v.show"
            :label="v.title"
            :fixed="v.fixed"
            :min-width="v.minWidth"
          >
            <template #default="scope">
              <div
                v-if="
                  payMan_num(scope.row.retentionVOS, index) ||
                  addPayMan_num(scope.row.retentionVOS, index) ||
                  addPayMan_sum(scope.row.retentionVOS, index) ||
                  addPayMan_retention(scope.row.retentionVOS, index)
                "
              >
                <span style="user-select: none"
                  >付费人数
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="当天付费的"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon> </el-tooltip
                  >：{{ payMan_num(scope.row.retentionVOS, index) }}</span
                ><br />
                <span style="user-select: none"
                  >新增付费人数<el-tooltip
                    class="box-item"
                    effect="dark"
                    content="当天是首充付费的"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon> </el-tooltip
                  >：{{ addPayMan_num(scope.row.retentionVOS, index) }}</span
                ><br />
                <span style="user-select: none"
                  >付费总人数<el-tooltip
                    class="box-item"
                    effect="dark"
                    content="截止当前的最新付费总人数"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon> </el-tooltip
                  >：{{ addPayMan_sum(scope.row.retentionVOS, index) }}</span
                ><br />
                <span style="user-select: none"
                  >付费留存率<el-tooltip
                    class="box-item"
                    effect="dark"
                    content="付费人数/付费总人数"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon> </el-tooltip
                  >：{{ addPayMan_retention(scope.row.retentionVOS, index) }}</span
                ><br />
                <span style="user-select: none"
                  >昨日付费用户在线超过60秒的用户数：{{
                    onlineMinterCnt_hook(scope.row.retentionVOS, index)
                  }}</span
                ><br />
                <span style="user-select: none"
                  >私信回复率
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="私信发出去后3分钟之内收到回复的数量/主动私信总数"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon> </el-tooltip
                  >：{{ replyRate_hooks(scope.row.retentionVOS, index) }}</span
                ><br />
                <span style="user-select: none"
                  >人均建联：{{ jianlian_hook(scope.row.retentionVOS, index) }}</span
                ><br />
                <span style="user-select: none"
                  >人均主动私信：{{ sixinCnt_hook(scope.row.retentionVOS, index) }}</span
                ><br />
                <span style="user-select: none"
                  >主动发起视频次数：{{
                    callVideoCnt_hook(scope.row.retentionVOS, index)
                  }}</span
                ><br />
                <span style="user-select: none"
                  >视频接通次数：{{
                    videoConnectCnt_hook(scope.row.retentionVOS, index)
                  }}</span
                ><br />
              </div>
            </template>
          </el-table-column>
        </template>
      </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { query_payretention_list, query_channel_list } from "@/api/userillegal";
import {
  addPayMan_sum,
  payMan_num,
  addPayMan_num,
  addPayMan_retention,
  jianlian_hook,
  sixinCnt_hook,
  callVideoCnt_hook,
  videoConnectCnt_hook,
  replyRate_hooks,
  onlineMinterCnt_hook,
} from "@/hooks/numdata";
let loading = ref(false);
const listColmun = reactive<any>([]);
let listData = ref([]);
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
// //最近30天
let past30daysStart = toData - 30 * 3600 * 24 * 1000;
// const tableHeight = ref(); // 已移除，使用CSS布局

let queryForm = reactive<any>({
  data: [],
  startTime: past30daysStart,
  endTime: todayEnd,
  // applicationId:'com.dongxin.app'
});
let options = ref<any>();
let tableRef = ref<any>();

let date = ref<[Date, Date]>([new Date(past30daysStart), new Date(todayEnd)]);
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

// 移除动态高度计算，使用CSS布局
// onMounted(() => {
//   tableHeight.value = window.innerHeight - tableRef.value.$el.offsetTop - 150;
//   window.onresize = () => {
//     tableHeight.value = window.innerHeight - tableRef.value.$el.offsetTop - 150;
//   };
// });

//查询
async function query() {
  let params = {};
  loading.value = true;
  if (queryForm.data) {
    params = {
      oneLevel: queryForm?.data[0],
      twoLevel: queryForm?.data[1],
      threeLevel: queryForm?.data[2],
      startTime: queryForm?.startTime,
      endTime: queryForm?.endTime,
      applicationId: queryForm.applicationId
    };
  } else {
    params = {
      startTime: queryForm?.startTime,
      endTime: queryForm?.endTime,
      applicationId: queryForm.applicationId
    };
  }
  let res = await query_payretention_list(params);
  let { data } = res.data;
  listData.value = data;
  loading.value = false;
}
query();

const onreset = () => {
  queryForm = {
    date: [],
    startTime: past30daysStart,
    endTime: todayEnd,
  };
  query();
};

function mouseenterSubcat(node: any) {
  let el_node = document.querySelectorAll(
    ".el-popper.el-cascader__dropdown.specialCascaderRadio .el-cascader-panel .el-cascader-menu"
  );
  if (el_node[node.level] && el_node.length > 0) {
    (el_node[node.level] as HTMLElement).style.display = "none";
    cascaderRecursion(el_node, node);
  }
}

function mouseenterLeaf(node: any) {
  let el_node = document.querySelectorAll(
    ".el-popper.el-cascader__dropdown.specialCascaderRadio .el-cascader-panel .el-cascader-menu"
  );
  if (el_node[node.level] && el_node.length > 0) {
    (el_node[node.level] as HTMLElement).style.display = "block";
  }
}

function cascaderRecursion(el_node: any, node: any) {
  function handle(i: any) {
    if (el_node[node.level + i]) {
      el_node[node.level + i].style.display = "none";
      i++;
      handle(i);
    } else {
      return;
    }
  }
  handle(1);
}

//查询筛选列表
async function query_list() {
  let res = await query_channel_list();
  let { data } = res.data;
  options.value = data;
}
query_list();

function createTablePopper() {
  for (let index = 1; index < 31; index++) {
    let item = {
      prop: `leave${index}`,
      title: `次${index}日留`,
      show: true,
      minWidth: "200",
    };
    listColmun.push(item);
  }
}
createTablePopper();

// const changeSelect=(e:string)=>{
//   queryForm.applicationId=e;
//   query();
// }
createTablePopper();
</script>

<style lang="scss" scoped>
.illegallist {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>
