<template>
  <div class="channel">
    <el-card class="box-card">
      <template #header>
        <majaSelect :applicationId="applicationId" @changeSelect="changeSelect"/>
      </template>
      <div class="table-container">
        <el-table ref="uselistRef" :data="dataList" style="width: 100%; height: 100%" v-loading="listLoading" highlight-current-rowstripe>
        <template v-for="item in manData" :key="item.prop">
          <el-table-column :prop="item.prop" :label="item.title" :min-width="item.minWidth">
            <template slot="header" #header="_scope">
            <span>{{ item.title }}</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="item.showTooltip"
              placement="top-start"
            >
              <el-icon v-if="item.showTool">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
          <div v-if="item.isGreetedRate">
          <p>  分子:&nbsp;{{ scope.row.newMaleSayHelloCnt ||0}}</p>
          <p>  分母:&nbsp;{{ scope.row.newMaleCnt||0 }}</p>
          <p>  比例:{{ scope.row[item.prop] }}</p>
          </div>
          <div v-else-if="item.isNewManMessgeRate">
          <p>  分子:&nbsp;{{ scope.row.newMaleSendMsgCnt||0 }}</p>
          <p>  分母:&nbsp;{{ scope.row.newMaleCnt||0 }}</p>
          <p>  比例:{{ scope.row[item.prop] }}</p>
          </div>
          <span v-else>{{scope.row[item.prop]?scope.row[item.prop]:0}}</span>
          </template>
          </el-table-column>
       </template>
      </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup >
import { toRefs, ref, reactive, onBeforeMount } from "vue";
import majaSelect from '@/components/majaSelect.vue'
import { manData } from "./columnData/manDataColumn";
import {man_user_data_api} from "@/api/index";
let applicationId=ref('com.dongxin.app')
let dataList = ref([])
// let timer = null

const query=async()=>{
 let {data:{data}} = await man_user_data_api({applicationId:applicationId.value});
 data.forEach((item)=>{
  item.newManMessgeRate=item.newMaleCnt?((item.newMaleSendMsgCnt/item.newMaleCnt)*100).toFixed(2)+'%':"0%";
  item.greetedRate=item.newMaleCnt?((item.newMaleSayHelloCnt/item.newMaleCnt)*100).toFixed(2)+'%':"0%";
 })
 dataList.value=data;
}
query();

const changeSelect=(e)=>{
  applicationId.value=e;
  query();
}

</script>

<style lang="scss" scoped>
.channel {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: hidden;
      min-height: 0;
    }
  }

  .card-header {

    .mb-header {
      display: flex;

      :deep(.el-range-editor) {
        margin-left: 20px;
        flex: 1;
      }

      .el-input {
        margin-left: 20px;
        flex: 1;
      }

      .el-button {
        margin-left: 20px;
      }
    }
  }

}
</style>