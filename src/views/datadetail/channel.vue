<template>
  <div class="channel">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryFormV1" class="demo-form-inline">
              <el-form-item label="马甲包">
              <majaSelect :applicationId="queryFormV1.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
              <el-form-item label="一级渠道" prop="oneLevel">
                <el-select v-model="queryFormV1.oneLevel" placeholder="请选择">
                  <el-option label="全部" :value="opNull" />
                  <el-option v-for="(v, i) in oneLevel" :key="i" :label="v.channelName" :value="v.channel" />
                </el-select>
              </el-form-item>
              <el-form-item label="二级渠道" prop="twoLevel">
                <el-select v-model="queryFormV1.twoLevel" placeholder="请选择">
                  <el-option label="全部" :value="opNull" />
                  <el-option v-for="(v, i) in twoLevel" :key="i" :label="v.channelName" :value="i" />
                </el-select>
              </el-form-item>
              <el-form-item label="三级渠道" prop="threeLevel">
                <el-select v-model="queryFormV1.threeLevel" placeholder="请选择">
                  <el-option label="全部" :value="opNull" />
                  <el-option v-for="(v, i) in threeLevel" :key="i" :label="v.channelName" :value="v.channel" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否排序" prop="hasSort">
                <el-select v-model="queryFormV1.hasSort" placeholder="请选择">
                  <el-option label="否" :value="false" />
                  <el-option label="是" :value="true" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否信息流" prop="hasMarket">
                <el-select v-model="queryFormV1.hasMarket" placeholder="请选择">
                  <el-option label="否" :value="false" />
                  <el-option label="是" :value="true" />
                </el-select>
              </el-form-item>
              <el-form-item label="选择时间">
                <el-date-picker v-model="dateV1" type="date" placeholder="选择日期" @change="timeChangeV1" />
              </el-form-item>
              <el-form-item class="qubtn">
                <el-button type="primary" @click="queryV1">查询</el-button>
                <el-button @click="onresetV1(queryFormRef)">重置</el-button>
              </el-form-item>
              <el-form-item>
                <div class="mb-header">
                  <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                    format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    :shortcuts="shortcuts" size="default" @change="timeChange" />
                  <el-button class="ml" type="primary" @click="handleExportExcel">
                    <el-icon style="vertical-align: middle">
                      <Download />
                    </el-icon>
                    <span style="vertical-align: middle">导出Excel</span>
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <el-table ref="uselistRef" :data="dataListV1" style="width: 100%" v-loading="listLoading" highlight-current-row
        stripe>
        <template v-for="v in channelColumn" :key="v.prop">
          <el-table-column v-if="v.prop == 'popularize'" prop="popularize" label="推广消耗" min-width="100">
            <template #default="scope">
              <span v-if="!scope.row.isShow">{{ scope.row.popularize ? scope.row.popularize : 0 }}</span>
              <el-input v-else v-model="scope.row.popularize" />
            </template>
          </el-table-column>
          <el-table-column v-if="v.prop == 'downNum'" prop="downNum" label="下载数" min-width="100">
            <template #default="scope">
              <span v-if="!scope.row.isShow">{{ scope.row.downNum ? scope.row.downNum : 0 }}</span>
              <el-input v-else v-model="scope.row.downNum" />
            </template>
          </el-table-column>
          <el-table-column v-if="v.show" :label="v.title" :fixed="v.fixed" :min-width="v.minWidth">
            <template #default="scope">
              <span>{{ scope.row[v.prop] ? scope.row[v.prop] : 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="v.prop == 'edit'" label="操作" width="120" :fixed="v.fixed">
            <template #default="scope">
              <el-button :disabled="!isEdit || scope.row.time === '总计(不包含当天数据)'||scope.row.time === dateString(toData)" @click="onEdit(scope.row)" size="small"
                :type="scope.row.isShow ? 'success' : 'primary'">{{
                  scope.row.isShow ? "完成" : "编辑" }}</el-button>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { toRefs, ref, unref, reactive, onBeforeMount, watch } from "vue";
import { channelColumn } from "@/views/datadetail/columnData/channelColumn";
import { ChangeDecimalToPercentage } from "@/utils/percentialize";
import { queryChannelAnalyst_api, queryChannelAnalystList_api, saveChannelData_api, queryChannel_api } from "@/api/datadetail";
import { Download } from "@element-plus/icons-vue";
import type { FormInstance } from 'element-plus'
import { aoaToSheetXlsx } from "@/utils/excel";
import { ElMessage } from "element-plus";

let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天


let todayStart = toData; //开始时间




let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
let date = ref<[Date, Date]>([
  new Date(past7daysStart),
  new Date(todayEnd),
])
let dateV1 = ref<Date>(new Date(todayStart))
const queryFormRef = ref<FormInstance>()
let queryForm = reactive<any>({
  startTime: past7daysStart,
  endTime: todayEnd,
})
let oneLevel = ref<any>([])
let twoLevel = ref<any>([])
let threeLevel = ref<any>([])
let queryFormV1 = reactive<any>({
  oneLevel: null,
  twoLevel: null,
  threeLevel: null,
  time: todayStart,
  hasSort: false,
  hasMarket: true,
  applicationId:'com.dongxin.app'
})
let opNull = ref<any>(null)
let isEdit = false;
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

watch(() => [queryFormV1.oneLevel, queryFormV1.twoLevel, queryFormV1.threeLevel],
  val => {
    let isEt = val.every((item) => {
      return item !== null
    })
    isEdit = isEt;
  },
  { deep: true, immediate: true })


let dataListV1 = ref([])
let dataList = ref([])
let timer: any = null


const dateString = (timestamp: any) => {
  const date = new Date(timestamp);
  const year = date.getFullYear(); // 获取年份
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份，并补零
  const day = String(date.getDate()).padStart(2, '0'); // 获取日期，并补零
  return `${year}-${month}-${day}`;
}
dateString(toData)
//查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

const timeChangeV1 = (data: any) => {
  queryFormV1.time = data.getTime()
}

//页面展示
async function queryV1() {
  let par = { ...queryFormV1 };
  queryFormV1.twoLevel !== null ? par.twoLevel = twoLevel.value[queryFormV1.twoLevel].channel : '';
  let res = await queryChannelAnalystList_api({ ...par })
  let { data } = res.data
  let zongji = data[0]
  data.forEach((v: any) => {
    v.isShow = false
    if (v.downActivationRate >= 0) v.downActivationRate = ChangeDecimalToPercentage(v.downActivationRate)
    if (v.activationRegistrationRate >= 0) v.activationRegistrationRate = ChangeDecimalToPercentage(v.activationRegistrationRate)
    if (v.savePayRate >= 0) v.savePayRate = ChangeDecimalToPercentage(v.savePayRate)
    if (v.nextDayRate >= 0) v.nextDayRate = ChangeDecimalToPercentage(v.nextDayRate)
    if (v.time == "总计(不包含当天数据)") { delete data[0]; data.push(zongji) }
    v.sumNum = v.animateWomanNum + v.animateManNum;
  })

  dataListV1.value = data
}
queryV1()

const onresetV1 = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  dateV1.value = new Date(todayStart)
  queryFormV1.time = todayStart
  formEl.resetFields()
  queryV1()
}

//查询渠道
const queryChannel = async () => {
  let res = await queryChannel_api()
  let { data } = res.data
  data.forEach((v: any) => {
    if (v.level == 1) {
      oneLevel.value.push(v)
    }
    if (v.level == 2) {
      twoLevel.value.push(v)
    }
    if (v.level == 3) {
      threeLevel.value.push(v)
    }
  })
}
queryChannel()
//编辑
const onEdit = async (row: any) => {
  row.isShow = !row.isShow;
  const time = new Date(row.time);
  if (!row.isShow) {
    await saveChannelData_api({
      id: row.id,
      dateTime: time.getTime(),
      channel: row.channelName,
      popularize: row.popularize,
      downNum: row.downNum,
    });
    queryV1()
  }
};

// 表格相关
const state = reactive({
  listData: dataList.value, // 表格中要显示的数据
  listLoading: false, // 加载中
});

// 导出Excel表格
const fileName = ref("渠道详情");
const handleExportExcel = async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }
  //获取后端数据
  let res = await queryChannelAnalyst_api({
    ...queryForm,
    oneLevel: queryFormV1.oneLevel,
    twoLevel: queryFormV1.twoLevel,
    threeLevel: queryFormV1.threeLevel,
    hasSort: queryFormV1.hasSort,
    hasMarket: queryFormV1.hasMarket,
  })
  res.data.data.forEach((v: any) => {
    if (v.downActivationRate >= 0) v.downActivationRate = ChangeDecimalToPercentage(v.downActivationRate)
    if (v.activationRegistrationRate >= 0) v.activationRegistrationRate = ChangeDecimalToPercentage(v.activationRegistrationRate)
    if (v.savePayRate >= 0) v.savePayRate = ChangeDecimalToPercentage(v.savePayRate)
    if (v.nextDayRate >= 0) v.nextDayRate = ChangeDecimalToPercentage(v.nextDayRate)
    v.sumNum = v.animateWomanNum + v.animateManNum;
  })
  state.listData = res.data.data

  listLoading.value = true;
  //标题数组
  let titleArr = Object.values(channelColumn).map((v: any) => {
    if (v.title != "操作") {
      return v.title
    }
  })

  let table = unref(state.listData); //取消数据响应
  let header = titleArr;
  let data = table.map((item: any, _index: any) => {
    let { time, channelName, popularize, downNum, downCost, animateManNum, animateWomanNum,sumNum, activateNum, activateCost, downActivationRate, registerNum, registerCost, manRegisterNum, womanRegisterNum, activationRegistrationRate, newUserPayAmount, oldUserPayAmount, totalAmount, newUserPayPeopleCounting, oldUserPayPeopleCounting, payNum, savePayRate, savePayCost, newUserARPU, oldUserARPU, newUserARPPU, oldUserARPPU, intradayRoi, accRoi, nextDayRate } = item;
    return [time, channelName, popularize, downNum, downCost, animateManNum, animateWomanNum, sumNum,activateNum, activateCost, downActivationRate, registerNum, registerCost, manRegisterNum, womanRegisterNum, activationRegistrationRate, newUserPayAmount, oldUserPayAmount, totalAmount, newUserPayPeopleCounting, oldUserPayPeopleCounting, payNum, savePayRate, savePayCost, newUserARPU, oldUserARPU, newUserARPPU, oldUserARPPU, intradayRoi, accRoi, nextDayRate];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });

  timer = setInterval(() => {
    listLoading.value = false;
  }, 1000)
};

let { listLoading } = toRefs(state);

onBeforeMount(() => {
  clearInterval(timer)
})
const changeSelect=(e:string)=>{
  queryFormV1.applicationId=e;
  queryV1();
}
</script>

<style lang="scss" scoped>
.channel {

  .card-header {

    .mb-header {
      display: flex;

      :deep(.el-range-editor) {
        margin-left: 20px;
        flex: 1;
      }

      .el-input {
        margin-left: 20px;
        flex: 1;
      }

      .el-button {
        margin-left: 20px;
      }
    }
  }

}
</style>