import { reactive } from "vue";

// 自己定义表格头部的数据
export let manData = reactive({
  time: {
    prop: "date",
    title: "日期",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  incomePointSum: {
    prop: "incomePointSum",
    title: "总收益",
    minWidth: "100",
    show: true,
    type:null,
  },
  onlineNum: {
    prop: "onlineNum",
    title: "在线时间（秒）",
    minWidth: "150",
    show: true,
    type:null,
  },
  voiceAndNum: {
    prop: "voiceAndNum",
    title: "语音被呼叫次数",
    minWidth: "150",
    show: true,
    type:null,
  },
  voiceAcceptNum: {
    prop: "voiceAcceptNum",
    title: "语音接通次数",
    minWidth: "100",
    show: true,
    type:null,
  },
  voiceActiveNum: {
    prop: "voiceActiveNum",
    title: "主动发起语音人数",
    minWidth: "150",
    show: true,
    type:null,
  },
  videoAndNum: {
    prop: "videoAndNum",
    title: "视频被呼叫次数",
    minWidth: "150",
    show: true,
    type:null,
  },
  videoAcceptNum: {
    prop: "videoAcceptNum",
    title: "视频接通次数",
    minWidth: "150",
    show: true,
    type:null,
  },
  videoActiveNum: {
    prop: "videoActiveNum",
    title: "主动发起视频次数",
    minWidth: "150",
    show: true,
    type:null,
  },
  sendLetterCount: {
    prop: "sendLetterCount",
    title: "文字私信条数",
    minWidth: "150",
    show: true,
    type:null,
  },
  sendLetterPeopleNum: {
    prop: "sendLetterPeopleNum",
    title: "私信人数",
    minWidth: "100",
    show: true,
    type:null,
  },
  sendLetterRtnAvg: {
    prop: "sendLetterRtnAvg",
    title: "私信回复率",
    minWidth: "100",
    show: true,
    type:null,
  },
  dynamicNum: {
    prop: "dynamicNum",
    title: "动态条数",
    minWidth: "100",
    show: true,
    type:null,
  },
  connectedNum: {
    prop: "connectedNum",
    title: "建联人数",
    minWidth: "100",
    show: true,
    type:null,
  },
   greetedRate: {
    prop: "greetedRate",
    title: "被打招呼率",
    show: true,
    showTool:true,
    showTooltip:'被打招呼的人头数/新增男用户数',
    minWidth:"200",
    isGreetedRate: true,
  },
  newManMessgeRate: {
    prop: "newManMessgeRate",
    title: "新男用户消息发送率",
    show: true,
    showTool:true,
    showTooltip:'发消息的人头数/新增男用户数',
    minWidth:"200",
    isNewManMessgeRate: true,
  },
})