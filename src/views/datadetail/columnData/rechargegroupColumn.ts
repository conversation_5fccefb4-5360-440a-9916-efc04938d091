import { reactive } from "vue";

// 自己定义表格头部的数据
export let rechargegroupColumn = reactive({
  time: {
    prop: "time", 
    title: "日期", 
    show: true, 
    minWidth:"120", 
    fixed: true 
  },
  userId: {
    prop: "userId", 
    title: "用户id", 
    show: true, 
    minWidth:"120", 
    fixed: true 
  },
  nickname: {
    prop: "nickname", 
    title: "昵称", 
    show: true, 
    minWidth:"100", 
  },
  chanelName: {
    prop: "chanelName", 
    title: "渠道名称", 
    show: true, 
    minWidth:"120", 
  },
  gender: {
    prop: "gender", 
    title: "性别", 
    show: true, 
    minWidth:"100", 
  },
  rechargeAmount: {
    prop: "rechargeAmount", 
    title: "充值总金额", 
    show: true, 
    minWidth:"120", 
  },
  rechargeDetail: {
    prop: "rechargeDetail", 
    title: "充值明细", 
    show: true, 
    minWidth:"120", 
  },
  firstRechargeTime: {
    prop: "firstRechargeTime", 
    title: "首充时间", 
    show: true, 
    minWidth:"120", 
  },
  registerTime: {
    prop: "registerTime", 
    title: "注册时间", 
    show: true, 
    minWidth:"120", 
  },
})