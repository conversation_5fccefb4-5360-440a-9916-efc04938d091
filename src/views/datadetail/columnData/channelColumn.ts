import { reactive } from "vue";

// 自己定义表格头部的数据
export let channelColumn = reactive({
  // id: {
  //   prop: "id", //对应的是返回的数据
  //   title: "id", //表头的标题名
  //   show: false, //是否显示隐藏
  //   minWidth: "80", // 最小宽度
  //   fixed: "left" //是否固定
  // },
  time: {
    prop: "time",
    title: "日期",
    minWidth: "120",
    show: true,
    fixed: "left",
  },
  channelName: {
    prop: "channelName",
    title: "渠道",
    minWidth: "100",
    show: true,
    fixed: false
  },
  popularize: {
    prop: "popularize",
    title: "推广消耗",
    minWidth: "100",
    show: false,
    fixed: false,
  },
  downNum: {
    prop: "downNum",
    title: "下载数",
    minWidth: "100",
    show: false,
    fixed: false,
  },
  downCost: {
    prop: "downCost",
    title: "下载成本",
    minWidth: "100",
    show: true,
    fixed: false
  },
  animateManNum: {
    prop: "animateManNum",
    title: "男活跃用户",
    minWidth: "100",
    show: true,
    fixed: false
  },
  animateWomanNum: {
    prop: "animateWomanNum",
    title: "女活跃用户",
    minWidth: "100",
    show: true,
    fixed: false
  },
  sumNum: {
    prop: "sumNum",
    title: "总活跃用户",
    minWidth: "100",
    show: true,
    fixed: false
  },
  activateNum: {
    prop: "activateNum",
    title: "激活数",
    minWidth: "100",
    show: true,
    fixed: false
  },
  activateCost: {
    prop: "activateCost",
    title: "激活成本",
    minWidth: "100",
    show: true,
    fixed: false
  },
  downActivationRate: {
    prop: "downActivationRate",
    title: "下载到激活转化率",
    minWidth: "150",
    show: true,
    fixed: false
  },
  registerNum: {
    prop: "registerNum",
    title: "注册数",
    minWidth: "100",
    show: true,
    fixed: false
  },
  registerCost: {
    prop: "registerCost",
    title: "注册成本",
    minWidth: "100",
    show: true,
    fixed: false
  },
  manRegisterNum: {
    prop: "manRegisterNum",
    title: "男注册用户",
    minWidth: "100",
    show: true,
    fixed: false
  },
  womanRegisterNum: {
    prop: "womanRegisterNum",
    title: "女注册用户",
    minWidth: "100",
    show: true,
    fixed: false
  },
  activationRegistrationRate: {
    prop: "activationRegistrationRate",
    title: "激活到注册转化率",
    minWidth: "150",
    show: true,
    fixed: false
  },
  newUserPayAmount: {
    prop: "newUserPayAmount",
    title: "新用户付费金额",
    minWidth: "150",
    show: true,
    fixed: false
  },
  oldUserPayAmount: {
    prop: "oldUserPayAmount",
    title: "老用户付费金额",
    minWidth: "150",
    show: true,
    fixed: false
  },
  totalAmount: {
    prop: "totalAmount",
    title: "付费总金额",
    minWidth: "100",
    show: true,
    fixed: false
  },
  newUserPayPeopleCounting: {
    prop: "newUserPayPeopleCounting",
    title: "新用户付费人数",
    minWidth: "150",
    show: true,
    fixed: false
  },
  oldUserPayPeopleCounting: {
    prop: "oldUserPayPeopleCounting",
    title: "老用户付费人数",
    minWidth: "150",
    show: true,
    fixed: false
  },
  payNum: {
    prop: "payNum",
    title: "付费总人数",
    minWidth: "100",
    show: true,
    fixed: false
  },
  savePayRate: {
    prop: "savePayRate",
    title: "新增付费率",
    minWidth: "100",
    show: true,
    fixed: false
  },
  savePayCost: {
    prop: "savePayCost",
    title: "新增付费成本",
    minWidth: "120",
    show: true,
    fixed: false
  },
  newUserARPU: {
    prop: "newUserARPU",
    title: "新用户ARPU",
    minWidth: "120",
    show: true,
    fixed: false
  },
  oldUserARPU: {
    prop: "oldUserARPU",
    title: "老用户ARPU",
    minWidth: "120",
    show: true,
    fixed: false
  },
  newUserARPPU: {
    prop: "newUserARPPU",
    title: "新用户ARPPU",
    minWidth: "120",
    show: true,
    fixed: false
  },
  oldUserARPPU: {
    prop: "oldUserARPPU",
    title: "老用户ARPPU",
    minWidth: "120",
    show: true,
    fixed: false
  },
  intradayRoi: {
    prop: "intradayRoi",
    title: "当日ROI",
    minWidth: "100",
    show: true,
    fixed: false
  },
  accRoi: {
    prop: "accRoi",
    title: "累积roi",
    minWidth: "100",
    show: true,
    fixed: false
  },
  nextDayRate: {
    prop: "nextDayRate",
    title: "次留存率",
    minWidth: "100",
    show: true,
    fixed: false
  },
  edit: {
    prop: "edit", //定义是否显示按钮 操作
    title: "操作",
    show: false,
    minWidth:"100",
    fixed: "right"
  },
})