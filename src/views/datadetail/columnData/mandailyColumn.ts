import { reactive } from "vue";

// 自己定义表格头部的数据
export let mandailyColumn = reactive({
  userId: {
    prop: "userId",
    title: "用户id",
    minWidth:"150",
    show: true,
    fixed: true
  },
  isInviter: {
    prop: "isInviter",
    title: "是否是邀请人",
    minWidth:"150",
    show: true,
    fixed: false
  },
  nickname: {
    prop: "nickname",
    title: "昵称",
    minWidth:"150",
    show: true,
    fixed: false
  },
  registerTime: {
    prop: "registerTime",
    title: "注册时间",
    minWidth:"300",
    show: true
  },
  time: {
    prop: "time",
    title: "日期",
    minWidth:"180",
    show: true
  },
  channelName: {
    prop: "channelName",
    title: "渠道",
    minWidth:"300",
    show: true
  },
  onlineDuration: {
    prop: "onlineDuration",
    title: "在线时长秒",
    minWidth:"120",
    show: true
  },
  payAmount: {
    prop: "payAmount",
    title: "付费金额",
    minWidth:"120",
    show: true
  },
  wealthVal: {
    prop: "wealthVal",
    title: "土豪值",
    minWidth:"120",
    show: true
  },
  wealthLevel: {
    prop: "wealthLevel",
    title: "土豪等级",
    minWidth:"120",
    show: true
  },
  inviteSumIncome: {
    prop: "inviteSumIncome",
    title: "邀请人获得收益",
    minWidth:"120",
    show: true
  },

  // income: {
  //   prop: "income",
  //   title: "总收益",
  //   minWidth:"120",
  //   show: true
  // },
  
  voiceAndNum: {
    prop: "voiceAndNum",
    title: "音频被呼叫次数",
    minWidth:"120",
    show: true
  },
  
  voiceAcceptNum: {
    prop: "voiceAcceptNum",
    title: "语音接通次数",
    minWidth:"120",
    show: true
  },
  voiceActiveNum: {
    prop: "voiceActiveNum",
    title: "主动发起语音人数",
    minWidth:"120",
    show: true
  },
  videoAndNum: {
    prop: "videoAndNum",
    title: "视频被呼叫次数",
    minWidth:"120",
    show: true
  },
  videoAcceptNum: {
    prop: "videoAcceptNum",
    title: "视频接通次数",
    minWidth:"120",
    show: true
  },
  videoActiveNum: {
    prop: "videoActiveNum",
    title: "主动发起视频次数",
    minWidth:"120",
    show: true
  },
  sendLetterCount: {
    prop: "sendLetterCount",
    title: "文字私信条数",
    minWidth:"120",
    show: true
  },
  connectedNum: {
    prop: "connectedNum",
    title: "建联人数",
    minWidth:"120",
    show: true
  },
  sendLetterPeopleNum: {
    prop: "sendLetterPeopleNum",
    title: "私信人数",
    minWidth:"120",
    show: true
  },
  sendLetterRtnAvg: {
    prop: "sendLetterRtnAvg",
    title: "私信回复率",
    minWidth:"120",
    showTool:true,
    showTooltip:'建联人数/私信人数',
    isAvg:true,
    show: true
  },
  hearbeatMatchCount: {
    prop: "hearbeatMatchCount",
    title: "男用户被匹配次数",
    minWidth:"120",
    show: true
  },
  toSendLetterPeopleNum: {
    prop: "toSendLetterPeopleNum",
    title: "被私信人数",
    minWidth:"120",
    show: true
  },
  hitOnMeCount: {
    prop: "hitOnMeCount",
    title: "男用户被搭讪次数",
    minWidth:"120",
    show: true
  },
  dynamicNum: {
    prop: "dynamicNum",
    title: "动态条数",
    minWidth:"120",
    show: true
  },
})


