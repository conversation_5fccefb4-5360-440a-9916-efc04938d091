import { reactive } from "vue";

// 自己定义表格头部的数据
export let womandailyColumn = reactive({
  time: {
    prop: "time",
    title: "日期",
    minWidth:"120",
    show: true,
    fixed: true
  },
  userId: {
    prop: "userId",
    title: "uid",
    show: true,
    minWidth:"120",
    fixed: true
  },
  isInviter: {
    prop: "isInviter",
    title: "是否是邀请人",
    minWidth:"120",
    show: true
  },
  nickname: {
    prop: "nickname",
    title: "昵称",
    minWidth:"120",
    show: true
  },
  bindInviteUserId: {
    prop: "bindInviteUserId",
    title: "邀请人id",
    minWidth:"180",
    show: true
  },
  channel: {
    prop: "channel",
    title: "渠道",
    minWidth:"120",
    show: true
  },
  grade: {
    prop: "grade",
    title: "评级",
    show: true,
    minWidth:"100",
  },
  charmVal: {
    prop: "charmVal",
    title: "魅力值",
    show: true,
    minWidth:"100",
  },
  charmLevel: {
    prop: "charmLevel",
    title: "魅力等级",
    show: true,
    minWidth:"100",
  },
  isOnline: {
    prop: "isOnline",
    title: "是否在线",
    show: true,
    minWidth:"100",
  },
  voiceEarningsDiamondNum: {
    prop: "voiceEarningsDiamondNum",
    title: "语音收益积分数",
    show: true,
    minWidth:"150",
  },
  inviteSumIncome: {
    prop: "inviteSumIncome",
    title: "邀请人获得收益",
    show: true,
    minWidth:"150",
  },
  videoEarningsDiamondNum: {
    prop: "videoEarningsDiamondNum",
    title: "视频收益积分数",
    show: true,
    minWidth:"150",
  },
  giftsEarningsDiamondNum: {
    prop: "giftsEarningsDiamondNum",
    title: "礼物收益积分数",
    show: true,
    minWidth:"150",
  },
  freeVideoCardIncome: {
    prop: "freeVideoCardIncome",
    title: "视频卡收益",
    show: true,
    minWidth:"150",
  },
  fateChatIncome: {
    prop: "fateChatIncome",
    title: "缘分消息收益",
    show: true,
    minWidth:"150",
  },
  fateGiftIncome: {
    prop: "fateGiftIncome",
    title: "缘分礼物收益",
    show: true,
    minWidth:"150",
  },
  msgCardIncome: {
    prop: "msgCardIncome",
    title: "消息卡收益",
    show: true,
    minWidth:"150",
  },
  // intimacyBagIncome: {
  //   prop: "intimacyBagIncome",
  //   title: "亲密度礼包收益",
  //   show: true,
  //   minWidth:"150",
  // },
  otherEarningsDiamondNum: {
    prop: "otherEarningsDiamondNum",
    title: "其他收益",
    show: true,
    minWidth:"150",
  },
  weekActivityIncome: {
    prop: "weekActivityIncome",
    title: "周奖励积分数",
    show: true,
    minWidth:"150",
  },
  todayIncome: {
    prop: "todayIncome",
    title: "总收益",
    show: true,
    minWidth:"150",
  },
  chatEarningsDiamondNum: {
    prop: "chatEarningsDiamondNum",
    title: "聊天收益积分数",
    show: true,
    minWidth:"150",
  },
  onlineDuration: {
    prop: "onlineDuration",
    title: "在线时间（秒）",
    show: true,
    minWidth:"150",
  },
  voiceCalledPartyNum: {
    prop: "voiceCalledPartyNum",
    title: "音频被呼叫次数",
    show: true,
    minWidth:"150",
  },
  voiceAnswerNum: {
    prop: "voiceAnswerNum",
    title: "音频接通次数",
    show: true,
    minWidth:"150",
  },
  voiceAnswerNumManCall: {
    prop: "voiceAnswerNumManCall",
    title: "男打女音频接通次数",
    show: true,
    minWidth:"150",
  },
  voiceInitiativeNum: {
    prop: "voiceInitiativeNum",
    title: "主动发起音频次数",
    show: true,
    minWidth:"150",
  },
  voiceInitiativePeopleNum: {
    prop: "voiceInitiativePeopleNum",
    title: "主动发起音频人数",
    show: true,
    minWidth:"150",
  },
  videoCalledPartyNum: {
    prop: "videoCalledPartyNum",
    title: "视频被呼叫次数",
    show: true,
    minWidth:"150",
  },
  videoAnswerNum: {
    prop: "videoAnswerNum",
    title: "视频接通次数",
    show: true,
    minWidth:"150",
  },
  videoAnswerNumManCall: {
    prop: "videoAnswerNumManCall",
    title: "男打女视频接通次数",
    show: true,
    minWidth:"150",
  },
  videoInitiativeNum: {
    prop: "videoInitiativeNum",
    title: "主动发起视频次数",
    show: true,
    minWidth:"150",
  },
  videoInitiativePeopleNum: {
    prop: "videoInitiativePeopleNum",
    title: "主动发起视频人数",
    show: true,
    minWidth:"150",
  },
  personalLetterNum: {
    prop: "personalLetterNum",
    title: "文字私信条数",
    show: true,
    minWidth:"120",
  },
  personalLetterPeopleNum: {
    prop: "personalLetterPeopleNum",
    title: "私信人数",
    show: true,
    minWidth:"120",
  },
  dynamicNum: {
    prop: "dynamicNum",
    title: "动态条数",
    show: true,
    minWidth:"120",
  },
  constructionUnionPeopleNum: {
    prop: "constructionUnionPeopleNum",
    title: "建联人数",
    show: true,
    minWidth:"120",
  },
  initiativeIMPeopleNum: {
    prop: "initiativeIMPeopleNum",
    title: "主动IM消息人数",
    show: true,
    minWidth:"150",
  },
  videoGiftsScale: {
    prop: "videoGiftsScale",
    title: "视频礼物收入占比",
    show: true,
    minWidth:"150",
  },
  sayHelloManNum: {
    prop: "sayHelloManNum",
    title: "主动搭讪男用户数量",
    show: true,
    minWidth:"150",
  },
  matchPeopleNum: {
    prop: "matchPeopleNum",
    title: "心动匹配人数",
    show: true,
    minWidth:"150",
  },
  // sayHelloManRtnAvg: {
  //   prop: "sayHelloManRtnAvg",
  //   title: "主动搭讪男用户的回复率",
  //   show: true,
  //   minWidth:"150",
  // },
  // userConversionRatio: {
  //   prop: "userConversionRatio",
  //   title: "女用户与新建联男用户的视频转化率",
  //   show: true,
  //   minWidth:"150",
  // },
  replyMaleMsgCnt: {
    prop: "replyMaleMsgCnt",
    title: "女三分钟内回复率",
    show: true,
    showTool:true,
    showTooltip:'当天回复男主动消息数量，其中3分钟之内回复的数量/当天回复男主动消息数量',
    minWidth:"200",
    isCustom: true,
  },
  replyReplyMoleculeRate: {
    prop: "replyReplyMoleculeRate",
    title: "当天回复率",
    show: true,
    showTool:true,
    showTooltip:'女回复的人数/主动给女用户发消息的男用户人数',
    minWidth:"200",
    isCustom: false,
    isreplyReplyMoleculeRate: true,
  },
  registerTime: {
    prop: "registerTime",
    title: "注册日期",
    show: true,
    minWidth:"200",
  },
  
})