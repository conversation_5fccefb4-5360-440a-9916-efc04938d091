import { reactive } from "vue";

// 自己定义表格头部的数据
export let rechargegroupColumn = reactive({
  time: {
    prop: "time", 
    title: "日期", 
    show: true, 
    minWidth:"120", 
    fixed: true 
  },
  userId: {
    prop: "userId", 
    title: "用户id", 
    show: true, 
    minWidth:"120", 
    fixed: true 
  },
  nickname: {
    prop: "nickname", 
    title: "昵称", 
    show: true, 
    minWidth:"100", 
  },
  chanelName: {
    prop: "chanelName", 
    title: "渠道名称", 
    show: true, 
    minWidth:"120", 
  },
  gender: {
    prop: "gender", 
    title: "性别(仅男)", 
    show: true, 
    minWidth:"100", 
  },
  rechargeAmount: {
    prop: "rechargeAmount", 
    title: "充值总金额", 
    show: true, 
    minWidth:"120", 
  },
  rechargeDetail: {
    prop: "rechargeDetail", 
    title: "充值明细", 
    show: true, 
    minWidth:"120", 
  },
  firstRechargeTime: {
    prop: "firstRechargeTime", 
    title: "首充时间(仅注册当日用户)", 
    show: true, 
    minWidth:"180", 
  },
  firstSubMinutes: {
    prop: "firstSubMinutes", 
    title: "注册时间到首充时间间隔", 
    show: true, 
    minWidth:"180", 
  },
  secondRechargeTime: {
    prop: "secondRechargeTime", 
    title: "二次充值时间", 
    show: true, 
    minWidth:"180", 
  },
  secondRechargeAmount: {
    prop: "secondRechargeAmount",  
    title: "二次充值金额", 
    show: true, 
    minWidth:"120", 
  },
  secondSubMinutes: {
    prop: "secondSubMinutes", 
    title: "首充到二次充值时间间隔", 
    show: true, 
    minWidth:"150", 
  },
  registerTime: {
    prop: "registerTime", 
    title: "注册时间（仅记录注册当日用户）", 
    show: true, 
    minWidth:"180", 
  },
})