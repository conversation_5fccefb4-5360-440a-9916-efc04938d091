import { reactive } from "vue";

// 自己定义表格头部的数据
export let newChannelColumn:any = reactive({
  time: {
    prop: "registerDate",
    title: "日期",
    text:null,
    minWidth: "120",
    show: true,
    type:null,
    fixed: "left",
  },
  channel: {
    prop: "channel",
    title: "渠道",
    text:null,
    type:null,
    minWidth:"180",
    show: true,
    fixed: false
  },
  newActiveRegisterPayCost: {
    prop: "newActiveRegisterPayCost",
    title: "新增激活当日付费成本",
    type:null,
    text:'新增激活当日付费成本 =推广费用/新增激活当日付费人数',
    minWidth: "120",
    show: true,
    fixed: false
  },
  newRegisterPayCost: {
    prop: "newRegisterPayCost",
    type:null,
    title: "新增注册当日付费成本",
    text:'新增注册当日付费成本 = 推广费用/新增注册当日付费人数 ',
    minWidth: "120",
    show: true,
    fixed: false
  },
  newActiveRegisterPayCnt: {
    prop: "newActiveRegisterPayCnt",
    title: "新增激活当日付费人数",
    text:null,
    minWidth: "120",
    type:null,
    show: true,
    fixed: false
  },
  newRegisterPayCnt: {
    prop: "newRegisterPayCnt",
    title: "新增注册当日付费人数",
    minWidth: "100",
    type:null,
    text:null,
    show: true,
    fixed: false
  },
  investmentAmount: {
    prop: "investmentAmount",
    title: "推广消耗",
    minWidth: "100",
    type:"BigDecimal",
    text:null,
    show: true,
    fixed: false
  },
  downloadCnt: {
    prop: "downloadCnt",
    title: "下载数",
    minWidth: "100",
    type:null,
    text:null,
    show: true,
    fixed: false
  },
  downloadCost: {
    prop: "downloadCost",
    title: "下载成本",
    text:'下载成本 =  推广费用 /下载数',
    minWidth: "100",
    type:null,
    show: true,
    fixed: false
  },
  maleLoginCnt: {
    prop: "maleLoginCnt",
    title: "男活跃用户",
    text:null,
    minWidth: "100",
    type:null,
    show: true,
    fixed: false
  },
  femaleLoginCnt: {
    prop: "femaleLoginCnt",
    title: "女活跃用户",
    text:null,
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  sumLoginCht: {
    prop: "sumLoginCht",
    title: "总活跃人数",
    text:null,
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  activeCnt: {
    prop: "activeCnt",
    title: "激活数",
    text:null,
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  activeCost: {
    prop: "activeCost",
    title: "激活成本",
    type:null,
    text:'激活数成本 = 推广费用 / 激活数 ',
    minWidth: "100",
    show: true,
    fixed: false
  },
  downloadActiveConversion: {
    prop: "downloadActiveConversion",
    type:"BigDecimal",
    title: "下载激活转化率",
    text:'下载激活转化率 = 激活数 / 下载数',
    minWidth: "100",
    show: true,
    fixed: false
  },
  registerCnt: {
    prop: "registerCnt",
    title: "注册数",
    text:null,
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  registerCost: {
    prop: "registerCost",
    type:null,
    title: "注册成本",
    text:"注册成本 = 推广费用/注册数 ",
    minWidth: "100",
    show: true,
    fixed: false
  },
  maleRegisterCnt: {
    prop: "maleRegisterCnt",
    title: "男注册用户",
    text:null,
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  femaleRegisterCnt: {
    prop: "femaleRegisterCnt",
    title: "女注册用户",
    text:null,
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  activeRegisterConversion: {
    prop: "activeRegisterConversion",
    type:"BigDecimal",
    title: "激活到注册转化率",
    text:'激活到注册转化率 = 注册数 / 激活数',
    minWidth: "100",
    show: true,
    fixed: false
  },
  newUserPayAmount: {
    prop: "newUserPayAmount",
    type:null,
    title: "新用户付费金额（注册当日）",
    text:'今天注册的，今天付费的付费总额',
    minWidth: "100",
    show: true,
    fixed: false
  },
  newActiveUserPayAmount: {
    prop: "newActiveUserPayAmount",
    type:null,
    title: "新用户付费金额 (激活当日)",
    text:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  oldUserPayAmount: {
    prop: "oldUserPayAmount",
    type:null,
    title: "老用户付费金额",
    text:'非今天注册的，今天付费的付费总额',
    minWidth: "100",
    show: true,
    fixed: false
  },
  payAmount: {
    prop: "payAmount",
    title: "付费总金额",
    type:null,
    text:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  newUserPayCnt: {
    prop: "newUserPayCnt",
    title: "新用户付费人数（注册当天）",
    text:'今天注册的，今天付费的人头数',
    minWidth: "100",
    type:null,
    show: true,
    fixed: false
  },
  oldUserPayCnt: {
    prop: "oldUserPayCnt",
    title: "老用户付费人数",
    text:'非今天注册的，今天付费的付费人头数',
    minWidth: "100",
    type:null,
    show: true,
    fixed: false
  },
  firstPayCnt: {
    prop: "firstPayCnt",
    title: "首次付费人数",
    text:'不论注册时间，今天首次付费的人头数',
    minWidth: "100",
    type:null,
    show: true,
    fixed: false
  },
  secondPayCnt: {
    prop: "secondPayCnt",
    title: "二次付费人数",
    minWidth: "100",
    text:null,
    type:null,
    show: true,
    fixed: false
  },
  avgFirstPayMinutes: {
    prop: "avgFirstPayMinutes",
    title: "平均首次付费时长（分钟）",
    minWidth: "100",
    text:null,
    type:null,
    show: true,
    fixed: false
  },
  avgSecondPayMinutes: {
    prop: "avgSecondPayMinutes",
    title: "平均二次付费时长（分钟）",
    minWidth: "100",
    text:null,
    type:null,
    show: true,
    fixed: false
  },
  firstPayAmount: {
    prop: "firstPayAmount",
    title: "首次付费金额",
    text:'不论注册时间，今天首次付费的总金额',
    minWidth: "100",
    type:null,
    show: true,
    fixed: false
  },
  payCnt: {
    prop: "payCnt",
    title: "付费总人数",
    text:null,
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  newPayRate: {
    prop: "newPayRate",
    title: "新增付费率",
    type:"BigDecimal",
    text:'新增付费人数 / 新用户注册人数',
    minWidth: "100",
    show: true,
    fixed: false
  },
  newPayCost: {
    prop: "newPayCost",
    title: "新增付费成本（注册当日）",
    text:'当日注册的，推广费用 /当日付费的人数 ',
    minWidth: "120",
    type:null,
    show: true,
    fixed: false
  },
  newUserArpu: {
    prop: "newUserArpu",
    title: "新用户ARPU",
    type:null,
    text:' 新用户付费总额 / 新注册用户人数',
    minWidth: "130",
    show: true,
    fixed: false
  },
  oldUserArpu: {
    prop: "oldUserArpu",
    title: "老用户ARPU",
    type:null,
    text:'非当日注册用户充值金额 / 非当日注册活跃用户',
    minWidth: "130",
    show: true,
    fixed: false
  },
  newUserArppu: {
    prop: "newUserArppu",
    title: "新用户ARPPU",
    type:null,
    text:'新付费用户总额 / 新付费用户人数',
    minWidth: "130",
    show: true,
    fixed: false
  },
  oldUserArppu: {
    prop: "oldUserArppu",
    title: "老用户ARPPU",
    type:null,
    text:'老用户付费总额/ 老付费用户人数',
    minWidth: "130",
    show: true,
    fixed: false
  },
  // roi1: {
  //   prop: "roi1",
  //   title: "当日ROI",
  //   type:"BigDecimal",
  //   text:null,
  //   minWidth: "100",
  //   show: true,
  //   fixed: false
  // },
  // roiLatest: {
  //   prop: "roiLatest",
  //   title: "累计roi",
  //   type:"BigDecimal",
  //   text:null,
  //   minWidth: "100",
  //   show: true,
  //   fixed: false
  // },
  // retentionDay1: {
  //   prop: "retentionDay1",
  //   title: "次留存",
  //   type:"BigDecimal",
  //   text:null,
  //   minWidth: "100",
  //   show: true,
  //   fixed: false
  // },


 
})