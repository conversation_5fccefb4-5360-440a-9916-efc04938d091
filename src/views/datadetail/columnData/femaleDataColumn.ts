import { reactive } from "vue";

// 自己定义表格头部的数据
export let femaleDataColumn = reactive({
  time: {
    prop: "date",
    title: "日期",
    minWidth: "100",
    show: true,
    type:null,
    fixed: "left",
  },
  channel: {
    prop: "femaleNum",
    title: "新增人数",
    type:null,
    minWidth:"100",
    show: true,
    fixed: false
  },
  activeNum: {
    prop: "activeNum",
    title: "活跃人数",
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  incomeNum: {
    prop: "incomeNum",
    type:null,
    title: "有收益人数",
    minWidth: "100",
    show: true,
    fixed: false
  },
  onlineNum: {
    prop: "onlineNum",
    title: "在线分钟数",
    minWidth: "100",
    type:null,
    show: true,
    fixed: false
  },
  videoNum: {
    prop: "videoNum",
    title: "视频通话人数",
    minWidth: "120",
    type:"null",
    show: true,
    fixed: false
  },
  videoCallMinutes: {
    prop: "videoCallMinutes",
    title: "视频总时长（分）",
    minWidth: "140",
    type:null,
    show: true,
    fixed: false
  },
  videoCallMinutesAvg: {
    prop: "videoCallMinutesAvg",
    title: "人均视频时长（分）",
    minWidth: "150",
    type:"null",
    show: true,
    fixed: false
  },
  voiceNum: {
    prop: "voiceNum",
    title: "语音通话人数",
    type:null,
    minWidth: "120",
    show: true,
    fixed: false
  },
  voiceCallMinutes: {
    prop: "voiceCallMinutes",
    title: "语音总时长（分）",
    type:null,
    minWidth: "120",
    show: true,
    fixed: false
  },
  voiceCallMinutesAvg: {
    prop: "voiceCallMinutesAvg",
    title: "人均语音时长（分）",
    type:null,
    minWidth: "120",
    show: true,
    fixed: false
  },

  sendLetterPeopleNum: {
    prop: "sendLetterPeopleNum",
    type:null,
    title: "发送私信人数",
    minWidth: "120",
    show: true,
    fixed: false
  },
  sendLetterCount: {
    prop: "sendLetterCount",
    title: "发送私信条数",
    type:null,
    minWidth: "120",
    show: true,
    fixed: false
  },

  giftIncomePeopleNum: {
    prop: "giftIncomePeopleNum",
    type:null,
    title: "礼物收益人数",
    minWidth: "120",
    show: true,
    fixed: false
  },
  incomeGt10PeopleNum: {
    prop: "incomeGt10PeopleNum",
    title: "收益大于10元（人）",
    type:null,
    minWidth:"180",
    show: true,
    fixed: false
  },
  incomeGt100PeopleNum: {
    prop: "incomeGt100PeopleNum",
    title: "收益大于100元（人）",
    type:null,
    minWidth:"180",
    show: true,
    fixed: false
  },
  incomeGt200PeopleNum: {
    prop: "incomeGt200PeopleNum",
    title: "收益大于200元（人）",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  incomeGt500PeopleNum: {
    prop: "incomeGt500PeopleNum",
    title: "收益大于500元（人）",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  incomeGt1000PeopleNum: {
    prop: "incomeGt1000PeopleNum",
    title: "收益大于1000元（人）",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  incomePointSum: {
    prop: "incomePointSum",
    title: "收益积分总额",
    minWidth: "120",
    type:null,
    show: true,
    fixed: false
  },
  videoCallIncomePoints: {
    prop: "videoCallIncomePoints",
    title: "视频收益积分",
    minWidth: "120",
    type:null,
    show: true,
    fixed: false
  },
  videoCallIncomePointsAvg: {
    prop: "videoCallIncomePointsAvg",
    title: "视频收益占比",
    type:null,
    minWidth: "120",
    show: true,
    isAvg:true,
    fixed: false
  },
  voiceCallIncomePoints: {
    prop: "voiceCallIncomePoints",
    type:null,
    title: "语音收益积分",
    minWidth: "120",
    show: true,
    fixed: false
  },
  voiceCallIncomePointsAvg: {
    prop: "voiceCallIncomePointsAvg",
    title: "语音收益占比",
    type:null,
    minWidth: "120",
    show: true,
    isAvg:true,
    fixed: false
  },
  sendLetterIncome: {
    prop: "sendLetterIncome",
    title: "私信消息收益",
    type:null,
    minWidth: "100",
    show: true,
    fixed: false
  },
  sendLetterIncomeAgv: {
    prop: "sendLetterIncomeAgv",
    title: "消息收益占比",
    minWidth: "120",
    show: true,
    isAvg:true,
    fixed: false
  },
  videoCardIncomePoints: {
    prop: "videoCardIncomePoints",
    title: "视频卡收益积分",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  videoCardIncomePointsAvg: {
    prop: "videoCardIncomePointsAvg",
    title: "视频卡收益占比",
    type:null,
    minWidth: "180",
    show: true,
    isAvg:true,
    fixed: false
  },
  fateMessageIncomePoints: {
    prop: "fateMessageIncomePoints",
    title: "缘分消息收益积分",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  fateMessageIncomePointsAvg: {
    prop: "fateMessageIncomePointsAvg",
    title: "缘分消息收益占比",
    type:null,
    minWidth: "180",
    show: true,
    isAvg:true,
    fixed: false
  },
  msgCardIncomePoints: {
    prop: "msgCardIncomePoints",
    title: "消息卡收益积分",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  
  msgCardIncomePointsAvg: {
    prop: "msgCardIncomePointsAvg",
    title: "消息卡收益占比",
    type:null,
    minWidth: "180",
    show: true,
    isAvg:true,
    fixed: false
  },
  inviteSumIncomePoints: {
    prop: "inviteSumIncomePoints",
    title: "邀请收益积分",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  inviteSumIncomePointsAvg: {
    prop: "inviteSumIncomePointsAvg",
    title: "邀请收益占比",
    type:null,
    minWidth: "180",
    show: true,
    isAvg:true,
    fixed: false
  },
  otherIncomePoints: {
    prop: "otherIncomePoints",
    title: "其他收益积分",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  otherIncomePointsAvg: {
    prop: "otherIncomePointsAvg",
    title: "其他收益占比",
    type:null,
    minWidth: "180",
    show: true,
    isAvg:true,
    fixed: false
  },
  giftIncomePoints: {
    prop: "giftIncomePoints",
    title: "礼物收益",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  giftIncomeAvg: {
    prop: "giftIncomeAvg",
    title: "礼物占比",
    minWidth: "120",
    type:null,
    show: true,
    isAvg:true,
    fixed: false
  },
  fateGiftIncomePoints: {
    prop: "fateGiftIncomePoints",
    title: "缘分礼物收益",
    type:null,
    minWidth: "180",
    show: true,
    fixed: false
  },
  fateGiftIncomeAvg: {
    prop: "fateGiftIncomeAvg",
    title: "缘分礼物占比",
    type:null,
    minWidth: "180",
    show: true,
    isAvg:true,
    fixed: false
  },
})