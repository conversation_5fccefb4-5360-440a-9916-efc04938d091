import { reactive } from "vue";

// 自己定义表格头部的数据
export let userColmun = reactive({
  time: {
    prop: "time", 
    title: "日期", 
    show: true, 
    minWidth:"180", 
    fixed: true 
  },
  animateUserTotal: {
    prop: "animateUserTotal", 
    title: "总留存数", 
    show: true, 
    minWidth:"120", 
  },
  newUserNum: {
    prop: "newUserNum", 
    title: "新用户活跃人数", 
    show: true, 
    minWidth:"150", 
  },
  oldUserNum: {
    prop: "oldUserNum", 
    title: "老用户活跃人数", 
    show: true, 
    minWidth:"150", 
  },
  rechargeAmountTotal: {
    prop: "rechargeAmountTotal", 
    title: "总充值金额", 
    show: true, 
    minWidth:"150", 
  },
  userPeopleCounting: {
    prop: "userPeopleCounting", 
    title: "用户充值人数", 
    show: true, 
    minWidth:"150", 
  },
  newUserAmount: {
    prop: "newUserAmount", 
    title: "新用户充值金额", 
    show: true, 
    minWidth:"150", 
  },
  newUserPeopleCounting: {
    prop: "newUserPeopleCounting", 
    title: "新用户充值人数", 
    show: true, 
    minWidth:"150", 
  },
  oldUserAmount: {
    prop: "oldUserAmount", 
    title: "老用户充值金额", 
    show: true, 
    minWidth:"150", 
  },
  oldUserPeopleCounting: {
    prop: "oldUserPeopleCounting", 
    title: "老用户充值人数", 
    show: true, 
    minWidth:"150", 
  },
  totalPayoutRate: {
    prop: "totalPayoutRate", 
    title: "总付费率", 
    show: true, 
    minWidth:"120", 
  },
  newUserPayoutRate: {
    prop: "newUserPayoutRate", 
    title: "新用户付费率", 
    show: true, 
    minWidth:"150", 
  },
  newUserarpu: {
    prop: "newUserarpu", 
    title: "新用户arpu", 
    show: true, 
    minWidth:"150", 
  },
  newUserarppu: {
    prop: "newUserarppu", 
    title: "新用户arppu", 
    show: true, 
    minWidth:"150", 
  },
  oldUserPayoutRate: {
    prop: "oldUserPayoutRate", 
    title: "老用户付费率", 
    show: true, 
    minWidth:"150", 
  },
  oldUserarpu: {
    prop: "oldUserarpu", 
    title: "老用户arpu", 
    show: true, 
    minWidth:"150", 
  },
  oldUserarppu: {
    prop: "oldUserarppu", 
    title: "老用户arppu", 
    show: true, 
    minWidth:"150", 
  },
  nextDay: {
    prop: "nextDay", 
    title: "次日留存", 
    show: true, 
    minWidth:"120", 
  },
  nextDayRate: {
    prop: "nextDayRate", 
    title: "次日留存率", 
    show: true, 
    minWidth:"120", 
  },
  threeDay: {
    prop: "threeDay", 
    title: "三日留存", 
    show: true, 
    minWidth:"120", 
  },
  threeDayRate: {
    prop: "threeDayRate", 
    title: "三日留存率", 
    show: true, 
    minWidth:"120", 
  },
  fifteenDay: {
    prop: "fifteenDay", 
    title: "十五日留存", 
    show: true, 
    minWidth:"120", 
  },
  fifteenDayRate: {
    prop: "fifteenDayRate", 
    title: "十五日留存率", 
    show: true, 
    minWidth:"120", 
  },
  twentyDay: {
    prop: "twentyDay", 
    title: "二十日留存", 
    show: true, 
    minWidth:"120", 
  },
  twentyDayRate: {
    prop: "twentyDayRate", 
    title: "二十日留存率", 
    show: true, 
    minWidth:"120", 
  },
  thirtyDay: {
    prop: "thirtyDay", 
    title: "三十日留存", 
    show: true, 
    minWidth:"120", 
  },
  thirtyDayRate: {
    prop: "thirtyDayRate", 
    title: "三十日留存率", 
    show: true, 
    minWidth:"120", 
  },
  sixtyDay: {
    prop: "sixtyDay", 
    title: "六十日留存", 
    show: true, 
    minWidth:"120", 
  },
  sixtyDayRate: {
    prop: "sixtyDayRate", 
    title: "六十日留存率", 
    show: true, 
    minWidth:"120", 
  }
})