<template>
  <div class="illegallist">
    <el-card class="box-card">
      <el-table :data="listData" style="width: 100%"  :border="true" >
        <el-table-column label="日期-统计日" prop="statisticDate" min-width="120"></el-table-column>
        <el-table-column label="发放体验卡张数" prop="sendCardNum" min-width="120"></el-table-column>
        <el-table-column label="一共多少体验卡(包含之前的)" prop="sumCardNum" min-width="120"></el-table-column>
        <el-table-column label="使用多少张" prop="useCardNum" min-width="120"></el-table-column>
        <el-table-column label="剩余多少张(包含之前的)" prop="cardNum" min-width="120"></el-table-column>
        <el-table-column label="使用视频体验卡平均时长" prop="duration" min-width="120"></el-table-column>
        <el-table-column label="视频卡产生的费用(接听方收益)" prop="income" min-width="120"></el-table-column>
        </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, } from "vue";
import { queryideo_statistic } from "@/api/index"
let listData = ref([])

//查询筛选列表
async function query() {
  let res = await queryideo_statistic();
  let { data } = res.data
  listData.value=data;

}
query();




</script>

<style lang="scss" scoped>
.illegallist {
  .el-pagination {
    margin-top: 20px;
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>