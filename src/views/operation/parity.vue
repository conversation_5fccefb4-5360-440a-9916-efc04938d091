<template>
  <div class="parity">
    <el-card class="box-card">
      <template #header>
        <el-form :inline="true" :model="queryForm" class="demo-form-inline">
          <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker v-model="date" :clearable="false" unlink-panels value-format="x"
                format="YYYY-MM-DD" start-placeholder="开始日期"
                size="default" @change="timeChange" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
      </template>
      <div class="table-container">
        <el-table :data="TableLists" style="width: 100%; height: 100%" v-loading="Loading">
        <el-table-column prop="date" label="日期" min-width="120"></el-table-column>
        <el-table-column prop="oddRegCount" label="奇数注册人数" min-width="120"></el-table-column>
        <el-table-column prop="evenRegCount" label="偶数注册人数" min-width="120"></el-table-column>
        <el-table-column prop="oddRechargeCount" label="奇数充值人数" min-width="120"></el-table-column>
        <el-table-column prop="evenRechargeCount" label="偶数充值人数" min-width="120"></el-table-column>
        <el-table-column prop="oddRate" label="奇数充值率" min-width="120">
        <template #default="{row}">
        {{ (row.oddRate *100).toFixed(2)}}%
        </template>
        </el-table-column>
        <el-table-column prop="evenRate" label="偶数充值率" min-width="120">
          <template #default="{row}">
            {{ (row.evenRate *100).toFixed(2)}}%
        </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {parity_recharge_data_api} from '@/api/index'
import { ref ,reactive} from 'vue';
import pageHook from "@/hooks/pageHook";
import {getYMD} from '@/utils/date'
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(query);
import type { FormInstance } from "element-plus";
const date=ref<any>(null);
const Loading=ref<boolean>(false);
const queryForm = reactive<any>({
  date: null,
  applicationId:'com.dongxin.app'
});
const queryFormRef = ref<FormInstance>();
let TableLists = ref<any>([]);

const onreset = (_formEl: FormInstance | undefined) => {
  queryForm.date=null;
  date.value=null
  query();
};

async function query() {
  Loading.value=true;
  let sum:any ={
    date:'总计',
    oddRegCount:0,
    evenRegCount:0,
    oddRechargeCount:0,
    evenRechargeCount:0,
    oddRate:0,
    evenRate:0,
  }
  parity_recharge_data_api({
    ...queryForm,  
     pageSize: pageSize.value,
    pageNum: currentPage.value}).then(res=>{
      res.data.data.map((item:any) => {
        sum.oddRegCount+=item.oddRegCount;
        sum.evenRegCount+=item.evenRegCount;
        sum.oddRechargeCount+=item.oddRechargeCount;
        sum.evenRechargeCount+=item.evenRechargeCount;
      
      });
    TableLists.value=res.data.data;
    sum.oddRate+=(sum.oddRechargeCount/sum.oddRegCount);
    sum.evenRate+=(sum.evenRechargeCount/sum.evenRegCount);
    TableLists.value.push(sum);
    Loading.value=false;
    totalNum = res.data.total;
  })
}

const timeChange = (data: any) => {
  queryForm.date=getYMD('-',data)
  
};
query();

const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}


</script>

<style lang="scss" scoped>
.parity {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    width: 100%;
  }
}
</style>