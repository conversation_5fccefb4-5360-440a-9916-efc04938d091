<template>
  <div class="useful">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item>
              <el-button :icon="Plus" @click="addUsefullist">增加</el-button>
            </el-form-item>
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="内容" prop="text">
              <el-input v-model="queryForm.text" />
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select v-model="queryForm.type" placeholder="请选择">
                <el-option label="全部" value="null" />
                <el-option label="男打招呼" :value="1" />
                <el-option label="女打招呼" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间" prop="text">
              <el-time-picker v-model="dates" is-range range-separator="到" value-format='HH:mm:ss'
                @change="handleChangeDates" start-placeholder="开始时间" end-placeholder="结束时间" />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query_useful">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="usefulListTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" width="80" fixed>
        </el-table-column>
        <el-table-column prop="applicationId" label="马甲包" width="80" fixed></el-table-column>
        <el-table-column prop="type" label="类型" width="80" fixed>
          <template #default="scope">
            <span>{{ scope.row.type === 1 ? '男打招呼' : "女打招呼" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="begin" label="开始时间" width="80" fixed></el-table-column>
        <el-table-column prop="end" label="结束" width="80" fixed></el-table-column>
        <el-table-column prop="text" label="内容">
          <template #default="scope">
            <span>{{ scope.row.text }}</span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editreqly(scope.row)" size="small" type="primary" :icon="Edit">编辑</el-button>
            <el-popconfirm title="你确定删除吗?" @confirm="delreqly(scope.row)">
              <template #reference>
                <el-button type="danger" :icon="Delete" size="small"></el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog v-model="isEdit" title="编辑">
        <el-form :rules="rules" ref="addFormRef" :model="editForm" label-width="100px" label-position="right">
          <el-form-item label="马甲包">
              <majaSelect :applicationId="editForm.applicationId" @changeSelect="changeSelect2"/>
            </el-form-item>
          <el-form-item label="内容" prop="text">
            <el-input v-model="editForm.text" />
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="editForm.type" placeholder="请选择">
              <el-option label="男打招呼" :value="1" />
              <el-option label="女打招呼" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间" prop="begin">
            <el-time-picker v-model="editForm.begin" value-format='HH:mm:ss' placeholder="请选择" />
          </el-form-item>
          <el-form-item label="结束" prop="end">
            <el-time-picker v-model="editForm.end" value-format='HH:mm:ss' placeholder="请选择" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isEdit = false">取消</el-button>
            <el-button type="primary" @click="editUseful(addFormRef)">确认</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog v-model="isAdd" ref="mydialog">
        <el-button type="primary" style="margin-bottom: 30px;" @click="appendForm">添加</el-button>
        <el-form :rules="rules" ref="addFormRef" :model="addForm" label-width="100px" label-position="right">
          <div v-for="(item, index) in addForm.List" :key="index" @validate="validate">
            <div class="del" @click="RemoveForm(index)">
              <span style="margin-left: 20px;">添加{{ index + 1 }}</span>
              <span style="float: right;margin-right: 20px; color: rgb(64, 158, 255); cursor: pointer;">删除</span>
            </div>
            <el-form-item label="马甲包">
              <majaSelect :applicationId="item.applicationId" @changeSelect="(e:string)=>changeSelect1(e,index)"/>
            </el-form-item>
            <el-form-item :label="'内容 ' + (index + 1)" :prop="'List.' + index + '.text'"
              :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
              <el-input v-model="item.text" />
            </el-form-item>
            <el-form-item :label="'类型 ' + (index + 1)" :prop="'List.' + index + '.type'">
              <el-select v-model="item.type" placeholder="请选择">
                <el-option label="男打招呼" :value="1" />
                <el-option label="女打招呼" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item :label="'开始时间 ' + (index + 1)" :prop="'List.' + index + '.begin'"
              :rules="{ required: true, message: '请选择开始时间', trigger: 'change' }">
              <el-time-picker v-model="item.begin" value-format='HH:mm:ss' placeholder="请选择" />
            </el-form-item>
            <el-form-item :label="'结束 ' + (index + 1)" :prop="'List.' + index + '.end'"
              :rules="{ required: true, message: '请选择开始时间', trigger: 'change' }">
              <el-time-picker v-model="item.end" value-format='HH:mm:ss' placeholder="请选择" />
            </el-form-item>
          </div>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="isAdd = false">取消</el-button>
            <el-button type="primary" @click="addUseful(addFormRef)">确认</el-button>
          </span>
        </template>
      </el-dialog>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from "vue";
import type { FormInstance } from "element-plus";
import { Edit, Plus, Delete } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
import { queryPeration_management, savePeration_management, delPeration_management } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(query_useful);
const queryFormRef = ref<FormInstance>();
let usefulListTab = ref([]);
const dates = ref<[any, any]>([
  null, null
])
let mydialog = ref(null)
let addFormRef = ref<any>();
let isEdit = ref<boolean>(false);
let isAdd = ref<boolean>(false);
let queryForm = reactive<any>({
  begin: null,
  end: null,
  type: null,
  applicationId:'com.dongxin.app',
});
let addForm = reactive<any>({
  List: [
    {
      text: '',
      type: 1,
      begin: null,
      end: null,
      applicationId:'com.dongxin.app',
    }
  ]
})
let editForm = reactive<any>({
  id: null,
  text: '',
  type: 1,
  begin: null,
  end: null,
  applicationId:'',
})

const rules = reactive<any>({
  text: [
    { required: true, message: '请输入内容', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'blur' },
  ],
  begin: [
    { required: true, message: '请输入开始时间', trigger: 'blur' },
  ],
  end: [
    { required: true, message: '请输入结束', trigger: 'blur' },
  ]
})

async function getreqlylist() {
  let res = await queryPeration_management({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId
  });
  let { data, total } = res.data;
  usefulListTab.value = data;
  totalNum.value = total;
}
const appendForm = () => {
  let parmas = {
    text: '',
    type: 1,
    begin: null,
    end: null,
    applicationId:'com.dongxin.app'
  }
  addForm.List.push(parmas)
  let elements = document.getElementsByClassName("el-overlay-dialog");
  nextTick(() => {
    elements[1].scrollTo(0, elements[1].scrollHeight)
  })
}

const validate = (e: any) => {
  console.log(e);

}
const RemoveForm = (index: Number) => {
  addForm.List.splice(index, 1);
}

const handleChangeDates = (e: any) => {
  if (!e) return;
  queryForm.begin = e[0];
  queryForm.end = e[1];
}

getreqlylist();
// 修改
const editreqly = async (row: any) => {
  isEdit.value = true;
  let begin = row.begin.length < 8 ? `${row.begin}:00` : row.begin;
  let end = row.end.length < 8 ? `${row.end}:00` : row.end;
  editForm = reactive({
    begin: begin,
    text: row.text,
    type: row.type,
    end: end,
    id: row.id,
    applicationId:row.applicationId
  })
};

let changeSelect1=(e:string,index: number)=>{
  addForm.List[index].applicationId=e
}


// 增加
const addUsefullist = () => {
  isAdd.value = true
  addForm = reactive({
    List: [
      {
        text: '',
        type: 1,
        begin: null,
        end: null,
        applicationId:'com.dongxin.app'
      }
    ]
  })
}
const addUseful = (_addFormRef: any) => {
  _addFormRef.validate((valid: any) => {
    if (valid) {
      let list = [...addForm.List];


      savePeration_management({ chatupHellos:list }).then(() => {
        isAdd.value = false
        query_useful()
        addForm = reactive({
          List: [],
        })
      })


    }
  })

}

const editUseful = (addFormRef: any) => {
  addFormRef.validate((valid: any) => {
    if (valid) {
      savePeration_management({ ...editForm }).then(() => {
        isEdit.value = false
        query_useful()
        editForm = reactive({
        })
      })
    }
  })


}



//删除
const delreqly = async (row: any) => {
  await delPeration_management({ id: row.id })
  query_useful()
}

// 查询
async function query_useful() {
  let res = await queryPeration_management({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  usefulListTab.value = data;
  totalNum.value = total;
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  dates.value = [null, null]
  handleChangeDates([null, null])
  formEl.resetFields()
  query_useful();
}
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query_useful();
}
const changeSelect2=(e:string)=>{
  editForm.applicationId=e;

}

</script>

<style lang="scss" scoped>
.useful {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }
  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }

  .el-pagination {
    margin-top: auto;
    margin-bottom: 0;
    background-color: #fff;
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
    z-index: 10;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
  }
}

.del {
  background-color: hsla(0, 2%, 63%, .1);
  height: 36px;
  line-height: 36px;
  margin: 20px 0;
}
</style>
