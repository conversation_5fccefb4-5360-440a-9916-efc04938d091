<template>
  <div class="writerCopy">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="queryForm"
            class="demo-form-inline"
          >
          <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item>
              <el-button :icon="Plus" @click="addReqlylist">增加</el-button>
            </el-form-item>
            <el-form-item label="状态" prop="wordStatus">
              <el-select v-model="queryForm.wordStatus">
                <el-option label="开启" :value="1" />
                <el-option label="关闭" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query_reqly">查询</el-button>
              <el-button @click="onreset()">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="reqlyListTab" style="width: 100%; height: 100%">
        <el-table-column prop="applicationId" label="马甲包"></el-table-column>
        <el-table-column prop="wordText" label="文案">
          <template #default="scope">
            <span v-if="!scope.row.isShow">{{ scope.row.wordText }}</span>
            <el-input v-else v-model="scope.row.wordText" />
          </template>
        </el-table-column>
        <el-table-column prop="wordStatus" label="状态">
          <template #default="scope">
            <el-switch
              v-model="scope.row.wordStatus"
              :disabled="scope.row.isShow"
              @change="(e) => handleSwitchChange(e, scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button
              @click="editreqly(scope.row)"
              size="small"
              :type="scope.row.isShow ? 'success' : 'primary'"
              :icon="Edit"
              >{{ scope.row.isShow ? "完成" : "编辑" }}</el-button
            >
            <el-popconfirm title="你确定删除吗?" @confirm="delreqly(scope.row)">
              <template #reference>
                <el-button type="danger" :icon="Delete" size="small"></el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog v-model="addShow" title="添加维护语" width="35%">
        <el-form ref="addFormRef" :model="addForm" label-width="120px">
          <el-form-item label="添加维护语">
            <el-input
              type="wordTextarea"
              v-model="addForm.wordText"
              autocomplete="off"
              style="width: 400px"
            />
          </el-form-item>
          <el-form-item label="马甲包">
              <majaSelect :applicationId="addForm.applicationId" @changeSelect="changeSelect1"/>
            </el-form-item>
          <el-form-item label="状态"
            ><el-switch v-model="addForm.wordStatus"
          /></el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="addShow = false">取消</el-button>
            <el-button type="primary" @click="addreqly">确认</el-button>
          </span>
        </template>
      </el-dialog>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import { Edit, Plus, Delete } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
import {
  stock_query_api,
  stock_modify_api,
  stock_add_api,
  stock_del_api,
} from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  query_reqly
);
const queryFormRef = ref<FormInstance>();
let reqlyListTab = ref([]);
let queryForm = reactive<any>({
  id: null,
  applicationId:'com.dongxin.app'
});
let addShow = ref(false);
let addForm = reactive<any>({
  wordText: null,
  wordStatus: null,
  wordType: 1,
  applicationId:'com.dongxin.app'
});

async function getreqlylist() {
  let res = await stock_query_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId
  });
  let { data, total } = res.data;
  data.forEach((v: any) => {
    v.isShow = false;
    v.wordStatus ? v.wordStatus = true : v.wordStatus = false;
  });
  reqlyListTab.value = data;
  totalNum.value = total;
}

const handleSwitchChange = async (e: any, row: any) => {
  await stock_modify_api({
    id: row.id,
    wordStatus: e ? 1 : 0,
    wordText: row.wordText,
    wordType: 1,
  });
};
getreqlylist();

// 修改
const editreqly = async (row: any) => {
  row.isShow = !row.isShow;
  if (!row.isShow) {
    await stock_modify_api({
      id: row.id,
      applicationId:row.applicationId,
      wordText: row.wordText,
      wordStatus: row.wordStatus?1:0,
      wordType: 1,
    });
  }
};

// 增加
const addReqlylist = () => {
  addShow.value = true;
};
const addreqly = async () => {
  let params={
      wordText: addForm.wordText,
      wordStatus: addForm.wordStatus?1:0,
      wordType: 1,
      applicationId:addForm.applicationId
  }
  await stock_add_api({ ...params });
  query_reqly();
  let datas = reactive<any>(
    {
      wordText: null,
      wordStatus: false,
      wordType: 1,
    },
  );
  addForm = datas;
  addShow.value = false;
};

//删除
const delreqly = async (row: any) => {
  await stock_del_api({ id: row.id });
  query_reqly();
};

// 查询
async function query_reqly() {
  let res = await stock_query_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  data.forEach((v: any) => {
    v.isShow = false;
    v.wordStatus ? v.wordStatus = true : v.wordStatus = false;
  });
  reqlyListTab.value = data;
  totalNum.value = total;
}

const onreset = () => {
  queryForm.id = null;
  queryForm.wordStatus=null;
  query_reqly();
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query_reqly();
}

let changeSelect1=(e:string)=>{
  addForm.applicationId=e
}

</script>

<style lang="scss" scoped>
.writerCopy {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }
  :deep(.el-dialog) {
    width: 35%;
    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }

  .el-pagination {
    margin-top: auto;
    margin-bottom: 0;
    background-color: #fff;
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
    z-index: 10;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
  }
}
</style>
