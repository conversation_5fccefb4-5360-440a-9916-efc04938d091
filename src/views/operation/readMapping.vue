<template>
  <div class="readMapping">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="等级选择">
              <el-select v-model="queryForm.femaleLevel" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="S" value="S" />
                <el-option label="A" value="A" />
                <el-option label="B1" value="B1" />
                <el-option label="B2" value="B2" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker
                v-model="queryForm.date"
                :clearable="false"
                type="daterange"
                unlink-panels
                value-format="x"
                format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                size="default"
                @change="timeChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="faceRef" :data="faceData" style="width: 100%; height: 100%">
          <el-table-column label="日期" prop="matchDate" fixed> </el-table-column>
          <el-table-column label="等级"  prop="femaleLevel">
          </el-table-column>
          <el-table-column label="匹配功能使用人数"  prop="useUserSum">
          </el-table-column>
          <el-table-column label="当天使用匹配功能的时长"  prop="useSecondSum">
          </el-table-column>
          <el-table-column label="匹配功能使用率"  prop="useRate">
            <template slot="header" #header="_scope">
            <span>匹配功能使用率</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="匹配使用人数/当天活跃人数"
              placement="top-start"
            >
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
            <template #default="scope">
            {{ scope.row.useRate?Math.round(scope.row.useRate*100):0.00 }}%
            </template>
          </el-table-column>
          <el-table-column label="匹配消息回复率"  prop="replyRate">
            <template slot="header" #header="_scope">
            <span>匹配消息回复率</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="回复过的消息/当天发出的全部消息"
              placement="top-start"
            >
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
            <template #default="scope">
            {{ scope.row.replyRate?Math.round(scope.row.replyRate*100):0.00 }}%
            </template>
          </el-table-column>
          <el-table-column label="匹配到的人数"  prop="sendMsgSumCnt">
          </el-table-column>
          <el-table-column label="任务完成率"  prop="taskCompleteRate"> 
            <template slot="header" #header="_scope">
            <span>任务完成率</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="任务完成的用户/匹配使用的人数"
              placement="top-start"
            >
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
            <template #default="scope">
            {{ scope.row.taskCompleteRate?Math.round(scope.row.taskCompleteRate*100):0.00 }}%
            </template>
          </el-table-column>
          <el-table-column label="任务一用户完成数量"  prop="task1Sum">
          </el-table-column>
          <el-table-column label="任务二用户完成数量"  prop="task2Sum">
          </el-table-column>
          <el-table-column label="任务三用户完成数量"  prop="task3Sum">
          </el-table-column>
        </el-table>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[3, 5, 10, 20]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import pageHook from "@/hooks/pageHook";
// import { Edit } from "@element-plus/icons-vue";
import { match_list_api } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  querySubmit
);
let faceData = ref([]); // 表格数据
let opNull = ref<any>(null);
const queryForm = reactive<any>({
  date: [],
  femaleLevel: null,
  startTime: null,
  endTime: null,
  applicationId:'com.dongxin.app'
});
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 请求列表数据
async function getfaceList() {
  let res = await match_list_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    state: queryForm.state,
    applicationId:queryForm.applicationId,
    femaleLevel:null,
  });

  let { data, total } = res.data;
  faceData.value = data;
  totalNum = total;
}
getfaceList();

// 查询
const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};
async function querySubmit() {
  let {femaleLevel,startTime, endTime ,applicationId} = queryForm;
  let res = await match_list_api({
    femaleLevel,
    startTime,
    endTime,
    applicationId,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  faceData.value = data;
  totalNum = total;
}
const onreset = () => {
  queryForm.userId = null;
  queryForm.date = [];
  queryForm.femaleLevel = null;
  getfaceList();
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  querySubmit();
}

</script>

<style scoped lang="scss">
.readMapping {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .card-header {
      display: flex;
      align-items: center;
      width: 100%;

      .el-form {
        width: 100%;
      }
    }

    .none {
      display: none;
    }

    .image-slot {
      font-size: 30px;

      .el-icon {
        font-size: 30px;
        display: flex;
      }
    }

    .el-image {
      width: 100%;
    }

    :deep(.el-dialog) {
      width: 35%;
    }

    :deep(.dia-ipt) {
      width: 215px;
    }
  }
}
</style>
