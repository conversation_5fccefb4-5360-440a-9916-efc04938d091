<template>
  <div>
    <el-form style="margin-top: 50px; margin-left: 50px;" :model="listForm" ref="FormRef" label-width="100px"
      :rules="rules">
      <el-form-item label="马甲包">
              <majaSelect :applicationId="listForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
      <el-form-item label="召回方式" v-model="listForm.category" prop="category" :rules="rules.type">
        <el-select v-model="listForm.category">
          <el-option label="系统召回" :value="0" />
          <el-option label="短信召回" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="未登录时间" v-model="listForm.days" prop="days" :rules="rules.days">
        <el-select v-model="listForm.days">
          <el-option label="3天" :value="3" />
          <el-option label="5天" :value="5" />
          <el-option label="7天" :value="7" />
          <el-option label="30天" :value="30" />
          <el-option label="45天" :value="45" />
          <el-option label="60天" :value="60" />
          <el-option label="90天" :value="90" />
          <el-option label="180天" :value="180" />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" v-model="listForm.type" prop="type" :rules="rules.type">
        <el-select v-model="listForm.type" placeholder="请选择">
          <el-option label="全部男" :value="0" />
          <el-option label="全部女" :value="1" />
          <el-option label="付费男" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="标题" prop="title" :rules="rules.title">
        <el-input v-model="listForm.title" style="width: 500px;"></el-input>
      </el-form-item>
      <div v-for="(_item, index) in listForm.contents" :key="index">
        <el-form-item :label="`内容${index + 1}`" :prop="`listForm.contents[${index}]`">
          <el-input v-model="listForm.contents[index]" style="width: 500px;">
          </el-input>
          <el-icon style="margin-left: 20px; cursor: pointer; font-size: 25px;" @click="appendForm()"
            v-if="index + 1 === listForm.contents.length * 1">
            <CirclePlusFilled />
          </el-icon>
          <el-icon style="margin-left: 20px; cursor: pointer; font-size: 25px;" @click="RemoveForm(index)" v-else>
            <RemoveFilled />
          </el-icon>
        </el-form-item>
      </div>
      <el-form-item class="el-form-itembtn">
        <el-button type="primary" @click="handlequery(FormRef)">提交</el-button>
      </el-form-item>

    </el-form>
  </div>
</template>

<script setup lang="ts">
import { offline_push_msg } from '@/api/index'
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus'
let listForm = reactive<any>({
  type: null,
  title: null,
  contents: [''],
  category:0,
  days:3,
  applicationId:'com.dongxin.app'
});
let FormRef = ref<any>(null)

const handlequery = (FormRef: any) => {
  let isShow = true;
  FormRef.validate((valid: any) => {
    if (valid) {
   let list=listForm.contents.filter((item: any) => {
        if (item !== '') {
          isShow = false;
          return item;
        }
      })
      if (isShow) {
        ElMessage({
          message: '至少输入一个内容',
          type: 'error',
        })
        return;
      }else{
        let parmas= listForm;
        parmas.contents=list;
        offline_push_msg({...parmas}).then(res=>{
          ElMessage({
          message: res.data.message,
          type: 'success',
        })
        })
      }
    }
  })

}
const appendForm = () => {
  listForm.contents.push('');
}

const rules = reactive<any>({
  days: [
    { required: true, message: '请选择未登录时间', trigger: 'blur' },
  ],
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
  ],
  category: [
    { required: true, message: '请选择召回方式', trigger: 'blur' },
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'blur' },
  ],

})

const RemoveForm = (index: Number) => {
  listForm.contents.splice(index, 1);

}

const changeSelect=(e:string)=>{
  listForm.applicationId=e;

}

</script>

<style scoped></style>