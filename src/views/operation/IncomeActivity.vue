<template>
  <div class="IncomeActivity">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId" />
            </el-form-item>
            <el-form-item label="结算状态" prop="status">
              <el-select v-model="queryForm.status" placeholder="请选择">
                <el-option label="全部" :value="opNull" />
                <el-option label="未结算" :value="0" />
                <el-option label="已结算" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="querySettlement">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
              <el-button class="ml" type="primary" @click="handleExportExcel">
                    <el-icon style="vertical-align: middle">
                      <Download />
                    </el-icon>
                    <span style="vertical-align: middle">导出Excel</span>
                  </el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="settlementTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" min-width="80" fixed></el-table-column>
        <el-table-column prop="userId" label="用户ID" min-width="120"></el-table-column>
        <el-table-column prop="inviteIncome" label="周收益（元）" min-width="150"></el-table-column>
        <el-table-column prop="point" label="获得收益（积分）" min-width="150"></el-table-column>
        <el-table-column prop="dateBegin" label="结算开始时间" min-width="120"></el-table-column>
        <el-table-column prop="dateEnd" label="结算结束时间" min-width="120"></el-table-column>
        <el-table-column prop="status" label="结算状态" min-width="120">
          <template #default="scope">
            {{ scope.row.status == 0 ? "未结算" : "已结算" }}
          </template>
        </el-table-column>
        <el-table-column prop="lastOperator" label="最后操作人" min-width="120"></el-table-column>
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button @click="editSettlement(scope.row, 0)" size="small" type="primary">未结算</el-button>
            <el-button @click="editSettlement(scope.row, 1)" size="small" type="success" >已结算</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>
  
<script setup lang="ts">
import { ref, reactive,unref } from "vue";
import type { FormInstance } from "element-plus";
import pageHook from "@/hooks/pageHook";
import { aoaToSheetXlsx } from "@/utils/excel";
import { ElMessage } from "element-plus";
import { query_income_activity_api, modify_income_activity_api } from "@/api/whitelist";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(querySettlement);
const queryFormRef = ref<FormInstance>();
let settlementTab = ref([]);
let queryForm = reactive<any>({
  id: null,
  userId: null,
  status: null,
  dateBegin: null,
  dateEnd: null,
  applicationId:'com.dongxin.app'
});
let opNull = ref<any>(null)
async function getSettlement() {
  let res = await query_income_activity_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    applicationId:queryForm.applicationId,
  });
  let { data, total } = res.data;
  settlementTab.value = data;
  totalNum.value = total;
}
getSettlement();


// 编辑
const editSettlement = async (row: any, status: any) => {
  await modify_income_activity_api({ id: row.id, status })
  querySettlement()
}




// 查询
async function querySettlement() {
  let res = await query_income_activity_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;
  settlementTab.value = data;
  totalNum.value = total;
}

const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  getSettlement();
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  querySettlement();
}
// 导出Excel表格
const fileName = ref("结算详情");
const handleExportExcel = async () => {
  if (!unref(fileName).trim()) {
    ElMessage({
      showClose: true,
      message: "请输入文件名",
      type: "warning",
    });
    return;
  }
 
  let res = await query_income_activity_api({
    ...queryForm,
    pageSize: 10000,
    pageNum: currentPage.value,
  });
  let { data } = res.data;
  let header = ['ID','用户ID','周收益（元）','获得收益（积分）','结算开始时间','结算结束时间','结算状态','最后操作人','是否存在重复数据'];
  let datas = data.map((item: any, _index: any) => {
    item.status===1?item.status="已结算":item.status="未结算";
    let {id,userId,inviteIncome,point,dateBegin,dateEnd,status,lastOperator,hasAbnormal} = item;
    return [id,userId,inviteIncome,point,dateBegin,dateEnd,status,lastOperator,hasAbnormal];
  });
  aoaToSheetXlsx({
    data:datas,
    header,
    filename: `${unref(fileName)}.xlsx`,
  });
}
</script>
  
<style lang="scss" scoped>
.IncomeActivity {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  :deep(.el-dialog) {
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
    }
  }
}
</style>
  