<template>
  <div>
    <el-card class="box-card">
      <template #header>
        <el-form :inline="true" :model="queryForm" class="demo-form-inline">
          <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
          <el-form-item label="时间选择" prop="date">
              <el-date-picker v-model="date" value-format="YYYY-MM-DD" :clearable="false" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="shortcuts" size="default" 
                @change="timeChange" />
                </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
      </template>
      <el-table :data="TableLists" style="width: 100%" height="604px" v-loading="Loading">
        <el-table-column prop="date" label="日期" min-width="120"></el-table-column>
        <el-table-column prop="count12" label="12元购买数量" min-width="120"></el-table-column>
        <el-table-column prop="count19" label="19元购买数量" min-width="120"></el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {xin_package_api} from '@/api/index'
import {ref ,reactive} from 'vue';
import {getYMD} from '@/utils/date'
import type { FormInstance } from "element-plus";
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近30天
let past30daysStart = toData - 30 * 3600 * 24 * 1000;
let date = ref<[string, string]>([
  getYMD('-',past30daysStart),
  getYMD('-',todayEnd),
])
const Loading=ref<boolean>(false);
let queryForm = reactive<any>({
  beginDate: getYMD('-',past30daysStart),
  endDate: getYMD('-',todayEnd),
  applicationId:'com.dongxin.app'
});
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];
const queryFormRef = ref<FormInstance>();
let TableLists = ref<any>([]);

const onreset = (_formEl: FormInstance | undefined) => {
  date.value = [
    getYMD('-',past30daysStart),
  getYMD('-',todayEnd),
  ]
  
  queryForm = {
  beginDate: getYMD('-',past30daysStart),
  endDate: getYMD('-',todayEnd),
};
  query();
};

async function query() {
  Loading.value=true;
  let sum:any={
    date:'总计',
    count12:0,
    count19:0,
  }
  xin_package_api({
    ...queryForm}).then(res=>{
      res.data.data.map((item:any) => {
        sum.count12+=item.count12;
        sum.count19+=item.count19;
      });
    TableLists.value=res.data.data;
    TableLists.value.push(sum);
    Loading.value=false;
  })
}
const timeChange = (data: any) => {
  queryForm.beginDate = data[0];
  queryForm.endDate = data[1];
};

query();


const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}


</script>

<style scoped>
.card-header {
  display: flex;
  align-items: center;
  width: 100%;

}
</style>