<template>
  <div>
    <el-form style="margin-top: 50px; margin-left: 50px;" :model="listForm" ref="FormRef" label-width="100px"
             :rules="rules">
      <el-form-item label="马甲包">
        <majaSelect :applicationId="listForm.applicationId" @changeSelect="changeSelect"/>
      </el-form-item>
      <el-form-item label="类型" v-model="listForm.type" prop="type" :rules="rules.type">
        <el-select v-model="listForm.type" placeholder="请选择">
          <el-option label="指定用户ID" :value="3"/>
          <el-option label="全部用户" :value="2"/>
          <el-option label="全部男" :value="0"/>
          <el-option label="全部女" :value="1"/>

        </el-select>
      </el-form-item>
      <el-form-item label="用户ID" v-if="listForm.type === 3">
        <el-input v-model="listForm.userId" style="width: 500px;"></el-input>
      </el-form-item>
      <el-form-item label="标题" prop="title" :rules="rules.title">
        <el-input v-model="listForm.title" style="width: 500px;"></el-input>
      </el-form-item>
      <div v-for="(link, idx) in listForm.linkList" :key="'link-' + idx" style="margin-bottom: 10px;">
        <el-form-item label="链接配置">
          <el-select v-model="link.linkType" placeholder="链接类型" style="width: 120px; margin-right: 10px;">
<!--            <el-option label="native(原生跳转)" :value="1" />-->
            <el-option label="web" :value="2" />
<!--            <el-option label="弹窗(显示弹窗)" :value="3" />-->
            <el-option label="复制" :value="4" />
<!--            <el-option label="接口(调用接口)" :value="5" />-->
            <el-option label="浏览器" :value="6" />
          </el-select>
          <el-input v-model="link.linkText" placeholder="链接文本" style="width: 180px; margin-right: 10px;" />
          <el-input v-model="link.linkUrl" placeholder="链接地址" style="width: 180px; margin-right: 10px;" />
          <el-button type="info" @click="insertPlaceholder(idx)" style="margin-right: 10px;">插入</el-button>
          <el-button type="danger" @click="removeLink(idx)" v-if="listForm.linkList.length > 1">删除</el-button>
          <el-button type="primary" @click="addLink" v-if="idx === listForm.linkList.length - 1">添加</el-button>
        </el-form-item>
      </div>
      <div v-for="(_item, index) in listForm.contents" :key="index">
        <el-form-item label="内容" :prop="`listForm.contents[${index}]`">
          <el-input type="textarea" v-model="listForm.contents[index]" :ref="(el) => setContentRef(el, index)" placeholder="链接占位符示例：{1}{2}依次递增(英文半角)" style="width: 500px;height: 120px">
          </el-input>
        </el-form-item>
      </div>

      <el-form-item class="el-form-itembtn">
        <el-button type="primary" @click="handlequery(FormRef)">提交</el-button>
      </el-form-item>

    </el-form>
  </div>
</template>

<script setup lang="ts">
import {offline_push_sendMsg} from '@/api/index'
import {reactive, ref} from 'vue';
import {ElMessage} from 'element-plus'

let listForm = reactive<any>({
  type: null,
  title: null,
  contents: [''],
  applicationId: 'com.dongxin.app',
  userId: null,
  linkList: [
    { linkType: 2, linkText: '', linkUrl: '' }
  ]
});
let FormRef = ref<any>(null)
let contentRefs = ref<any>({})

// 设置内容文本框的引用
const setContentRef = (el: any, index: number) => {
  if (el) {
    contentRefs.value[index] = el;
  }
}

const handlequery = (FormRef: any) => {
  let isShow = true;
  FormRef.validate((valid: any) => {
    if (valid) {
      let list = listForm.contents.filter((item: any) => {
        if (item !== '') {
          isShow = false;
          return item;
        }
      })
      if (isShow) {
        ElMessage({
          message: '至少输入一个内容',
          type: 'error',
        })
        return;
      } else {
        let params = {
          type: listForm.type,
          title: listForm.title,
          contents: list,
          applicationId: listForm.applicationId,
          userId: undefined,
          linkList: listForm.linkList.filter((l:any) => l.linkText && l.linkUrl)
        };
        if (listForm.type !== 0 && listForm.type !== 1 && listForm.type !== 2) {
          params.userId = listForm.userId;
        }
        offline_push_sendMsg(params).then(res => {
          ElMessage({
            message: res.data.message,
            type: 'success',
          })
        })
      }
    }
  })
}

const rules = reactive<any>({
  days: [
    {required: true, message: '请选择未登录时间', trigger: 'blur'},
  ],
  title: [
    {required: true, message: '请输入标题', trigger: 'blur'},
  ],
  category: [
    {required: true, message: '请选择召回方式', trigger: 'blur'},
  ],
  type: [
    {required: true, message: '请选择类型', trigger: 'blur'},
  ],

})

const changeSelect = (e: string) => {
  listForm.applicationId = e;

}

const addLink = () => {
  listForm.linkList.push({ linkType: 2, linkText: '', linkUrl: '' });
}
const removeLink = (idx: number) => {
  if (listForm.linkList.length > 1) {
    listForm.linkList.splice(idx, 1);
  }
}

// 插入占位符到内容文本框的光标位置
const insertPlaceholder = (linkIndex: number) => {
  const placeholder = `{${linkIndex + 1}}`;

  // 遍历所有内容文本框，找到当前聚焦的文本框
  for (let contentIndex = 0; contentIndex < listForm.contents.length; contentIndex++) {
    const textareaRef = contentRefs.value[contentIndex];
    if (textareaRef && textareaRef.textarea) {
      const textarea = textareaRef.textarea;

      // 检查是否是当前聚焦的文本框
      if (document.activeElement === textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const currentValue = listForm.contents[contentIndex];

        // 在光标位置插入占位符
        const newValue = currentValue.slice(0, start) + placeholder + currentValue.slice(end);
        listForm.contents[contentIndex] = newValue;

        // 设置新的光标位置
        setTimeout(() => {
          const newCursorPos = start + placeholder.length;
          textarea.setSelectionRange(newCursorPos, newCursorPos);
          textarea.focus();
        }, 0);

        return;
      }
    }
  }

  // 如果没有找到聚焦的文本框，默认插入到第一个文本框的末尾
  if (listForm.contents.length > 0) {
    listForm.contents[0] += placeholder;
  }
}

</script>

<style scoped></style>
