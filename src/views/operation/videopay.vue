<template>
  <div class="face">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="queryForm.userId"/>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="queryForm.type" placeholder="请选择">
                <el-option label="全部" :value="opNull"/>
                <el-option label="未审核" :value="1"/>
                <el-option label="审核通过" :value="2"/>
                <el-option label="审核失败" :value="3"/>
              </el-select>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker v-model="queryForm.date" :clearable="false" type="daterange" unlink-panels
                              value-format="x"
                              format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :shortcuts="shortcuts" size="default" @change="timeChange"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="querySubmit">查询</el-button>
              <el-button @click="onreset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table ref="faceRef" :data="faceData" style="width: 100%; height: 100%">
          <el-table-column label="用户ID" width="120">
            <template #default="scope">{{ scope.row.userId }}</template>
          </el-table-column>
          <el-table-column label="视频" width="250">
            <template #default="scope">
              <video style="height:120px; width:80px;" :src="scope.row.url" controls/>
            </template>
          </el-table-column>
          <el-table-column label="上传时间" width="170" prop="createTime"></el-table-column>
<!--          <el-table-column label="操作时间" width="170" prop="createTime"></el-table-column>-->
          <el-table-column label="状态" width="150" prop="status"></el-table-column>
          <el-table-column label="失败原因" min-width="120">
            <template #default="scope">{{ scope.row.content }}</template>
          </el-table-column>
                    <el-table-column label="最后操作人" min-width="120">
            <template #default="scope">{{ scope.row.lastOperator }}</template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button
                  @click="faceEdit(scope.row, 2, scope.row.userId)"
                  size="small"
                  type="success"
                  v-if="scope.row.type == 1 && scope.row.type !== 3"
              >
                审核通过
              </el-button>

              <el-button
                  @click="openFailReasonDialog(scope.row)"
                  size="small"
                  type="primary"
                  v-if="scope.row.type == 1 && scope.row.type !== 3"
              >
                审核失败
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[3, 5, 10, 20]"
                     :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"/>
    </el-card>

    <!-- 审核失败原因对话框 -->
    <el-dialog v-model="failReasonDialogVisible" title="审核失败原因" width="30%">
      <el-input v-model="failReason" type="textarea" :rows="4" placeholder="请输入失败原因"></el-input>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="failReasonDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmFailReason">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from "vue";
import pageHook from "@/hooks/pageHook";
import {getYMDHMS, getYMD} from "@/utils/date"
import {cancelRechargeVideo_api, video_pay_api} from "@/api/index";

let {totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange} =
    pageHook(querySubmit);
let faceData = ref([]); // 表格数据
let opNull = ref<any>(null)
const queryForm = reactive<any>({
  userId: null,
  date: [],
  // status: opNull,
  startTime: null,
  endTime: null,
});
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

const failReasonDialogVisible = ref(false);
const failReason = ref("");
const currentRow = ref<any>(null);

const openFailReasonDialog = (row: any) => {
  currentRow.value = row;
  failReasonDialogVisible.value = true;
};

const confirmFailReason = async () => {
  await faceEdit(currentRow.value, 3, currentRow.value.userId, failReason.value);
  failReasonDialogVisible.value = false;
  failReason.value = "";
};

// 请求列表数据
async function getfaceList() {
  let res = await video_pay_api({
    pageSize: pageSize.value,
    pageNum: currentPage.value,
    status: queryForm.status
  });

  let {data, total} = res.data;
  data.forEach((v: any) => {
    v.operatorTime = getYMDHMS('-', ':', v.operatorTime);
    v.uploadTime = getYMDHMS('-', ':', v.uploadTime);
    if (v.type == 1) {
      v.status = "审核中";
    } else if (v.type == 2) {
      v.status = "审核通过";
    } else if (v.type == 3) {
      v.status = "审核失败";
    }
  });
  faceData.value = data;
  totalNum = total;
}

getfaceList();

// 编辑
const faceEdit = async (row: any, type: any, userId: any, desc?: string) => {
  await cancelRechargeVideo_api({id: row.id, type, userId, desc});
  querySubmit();
};

const timeChange = (data: any) => {
  queryForm.startTime = data[0];
  queryForm.endTime = data[1];
};

// 查询
async function querySubmit() {
  let {userId, status, startTime, endTime} = queryForm;
  let res = await video_pay_api({
    userId,
    status,
    beginDate: startTime ? getYMD('-', startTime) : null,
    endDate: endTime ? getYMD('-', endTime) : null,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let {data, total} = res.data;
  data.forEach((v: any) => {
    v.operatorTime = getYMDHMS('-', ':', v.operatorTime);
    v.uploadTime = getYMDHMS('-', ':', v.uploadTime);
    if (v.type == 1) {
      v.status = "审核中";
    } else if (v.type == 2) {
      v.status = "审核通过";
    } else if (v.type == 3) {
      v.status = "审核失败";
    }
  });
  faceData.value = data;
  totalNum = total;
};

const onreset = () => {
  queryForm.userId = null;
  queryForm.date = [];
  queryForm.startTime = null;
  queryForm.endTime = null;
  queryForm.type = 0;
  getfaceList();
};
</script>

<style scoped lang="scss">
.face {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }

    .card-header {
      display: flex;
      align-items: center;
      width: 100%;

      .el-form {
        width: 100%;
      }
    }

    .none {
      display: none;
    }

    .image-slot {
      font-size: 30px;
    }

    .image-slot .el-icon {
      font-size: 30px;
    }

    .el-image {
      width: 100%;
    }

    :deep(.el-dialog) {
      width: 35%;
    }

    :deep(.dia-ipt) {
      width: 215px;
    }
  }
}
</style>