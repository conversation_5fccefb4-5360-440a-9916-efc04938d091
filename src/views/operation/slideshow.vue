<template>
  <div class="slideshow">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <majaSelect :applicationId="applicationId" @changeSelect="changeSelect"/>
          <el-button :icon="Plus" @click="addReqlylist">增加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="reqlyListTab" style="width: 100%; height: 100%">
        <el-table-column prop="id" label="ID" width="80" fixed></el-table-column>
        <el-table-column prop="bannerImg" label="图片" width="350" fixed>
          <template #default="scope">
            <el-image style="width: 300px; height: 70px" :src="scope.row.bannerImg" :preview-src-list="idcaBa"
              :initial-index="0" fit="cover" :preview-teleported="true" :hide-on-click-modal="true"
              @click="resIdcaBa(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" width="120" fixed></el-table-column>
        <el-table-column prop="bannerPosition" label="位置" width="80" fixed>
          <template #default="scope">
            {{ scope.row.bannerPosition == 1 ? "我的页面" : scope.row.bannerPosition === 2 ? "消息会话页" :  scope.row.bannerPosition === 3?"首页推荐页": scope.row.bannerPosition === 4?"礼物墙":"IM聊天页面" }}
          </template>
        </el-table-column>
        <el-table-column prop="gmtCreated" label="创建时间" width="200" fixed></el-table-column>
        <el-table-column prop="link" label="h5跳转地址" width="260" fixed></el-table-column>
        <el-table-column prop="sort" label="排序" width="80" fixed></el-table-column>
        <el-table-column prop="status" label="状态" width="80" fixed>
          <template #default="scope">
            {{ scope.row.status ? "下架" : "上架" }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="80" fixed>
          <template #default="scope">
            {{ scope.row.type == 1 ? "原生" : "h5" }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="200">
          <template #default="scope">
            <el-button @click="editreqly(scope.row)" size="small" type="primary" :icon="Edit">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="isShow" :title="addForm.id ? '修改轮播图' : '添加轮播图'" destroy-on-close @close="close" width="35%">
        <el-form ref="addFormRef" :model="addForm" label-width="100px" :rules="rules">
          <el-form-item label="马甲包">
              <majaSelect :applicationId="addForm.applicationId" @changeSelect="changeSelect1" :disabled="addForm.id"/>
            </el-form-item>
          <el-form-item label="轮播图片" prop="bannerImg">
            <div class="avatar-uploader" @click="upload">
              <input type="file" @change="getFile" ref="upImg" class="upImg">
              <img v-if="addForm.bannerImg" :src="addForm.bannerImg" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </div>
          </el-form-item>
          <el-form-item prop="title" label="标题" fixed>
            <el-input v-model="addForm.title" style="width: 200px;" />
          </el-form-item>
          <el-form-item prop="bannerPosition" label="位置" fixed>
            <el-select v-model="addForm.bannerPosition" placeholder="请选择" style="width: 200px;">
              <el-option label="我的页面" :value="1" />
              <el-option label="消息会话页" :value="2" />
              <el-option label="首页推荐页" :value="3" />
              <el-option label="礼物墙页" :value="4" />
              <el-option label="IM聊天页面" :value="5" />
            </el-select>
          </el-form-item>
          <el-form-item prop="type" label="类型" fixed>
            <el-select v-model="addForm.type" placeholder="请选择" style="width: 200px;">
              <el-option label="原生" :value="1" />
              <el-option label="h5" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item prop="link" label="h5跳转地址" fixed>
            <el-input v-model="addForm.link" style="width: 300px;" />
          </el-form-item>
          <el-form-item prop="status" label="状态" fixed>
            <el-radio-group v-model="addForm.status" class="ml-4">
              <el-radio :label="0">上架</el-radio>
              <el-radio :label="1">下架</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="sort" label="排序" fixed>
            <el-input-number v-model="addForm.sort" style="width: 300px;" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="close">取消</el-button>
            <el-button type="primary" @click="addreqly(addFormRef)">确认</el-button>
          </span>
        </template>
      </el-dialog>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { Edit, Plus } from "@element-plus/icons-vue";
import pageHook from "@/hooks/pageHook";
import COS from 'cos-js-sdk-v5'
import axios from "axios";
import { query_banner_list, add_banner_info, modify_banner_info } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(getreqlylist);
let reqlyListTab = ref([]);
let addFormRef = ref(null);
let upImg = ref<any>(null)
let isShow = ref(false)
let idcaBa = ref<any>([])
  let  applicationId=ref('com.dongxin.app')
let addForm = reactive<any>({
  status: 0,
  link: null,
  type: null,
  bannerPosition: null,
  title: null,
  bannerImg: null,
  sort: null,
  applicationId:'com.dongxin.app',
}
)
let Cos = reactive<any>({
  tmpSecretId: "",
  tmpSecretKey: "",
  sessionToken: "",
  createTime: null,
  expireTime: null
})
const rules = reactive<any>({
  bannerPosition: [{ required: true, message: "不能为空", trigger: "blur" }],
  type: [{ required: true, message: "不能为空", trigger: "change" }],
  status: [{ required: true, message: "不能为空", trigger: "change" }],
  link: [
    // 自定义校验规则
    {
      validator(_rule: any, value: any, callback: any) {
        if (addForm.type === 2 && (value === '' || value == null)) {
          // 校验不通过
          return callback(new Error('请输入h5地址'))
        } else {
          // 校验通过
          callback()
        }
      }
    }
  ],
  bannerImg: [{ required: true, message: "请上传图片", trigger: "change" }]
});
const upload = () => {
  upImg.value?.click()
}
const resIdcaBa = (row: any) => {
  idcaBa.value = []
  if (idcaBa.value.length == 0) {
    idcaBa.value.push(row.bannerImg)
  }
}


// 上传图片
const getFile = (e: any) => {
  const file = e.target.files[0];
  handleFileInUploading(file)
}

async function getreqlylist() {
  let res = await query_banner_list({
    applicationId:applicationId.value,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;

  reqlyListTab.value = data;
  totalNum.value = total;
}

getreqlylist();

const close = () => {
  isShow.value = false;
  addForm = reactive<any>({
    status: 0,
    link: null,
    type: null,
    bannerPosition: null,
    title: null,
    bannerImg: null,
  })
}

const getCos = () => {
  axios.post('https://api.gxdkkj.xyz/uaa/access_credentials').then((res: any) => {
    const { data } = res.data
    Cos.tmpSecretId = data.tmpSecretId
    Cos.tmpSecretKey = data.tmpSecretKey
    Cos.sessionToken = data.sessionToken
    Cos.createTime = data.createTime
    Cos.expireTime = data.expireTime
  })
}
getCos()


// 修改
const editreqly = async (row: any) => {
  addForm = row;
  isShow.value = true;
};



// 增加
const addReqlylist = () => {
  isShow.value = true
}
const addreqly = async (_addFormRef: any) => {
  _addFormRef.validate(async (_valid: any) => {
    if(_valid){
      if (!addForm.id) {
      await add_banner_info({ ...addForm })
      getreqlylist();
      isShow.value = false
    } else {
      await modify_banner_info({ ...addForm })
      getreqlylist();
      isShow.value = false
    }
    }
  })
}



const cos = new COS({
  getAuthorization: (_options, callback) => {
    const obj: any = {
      TmpSecretId: Cos.tmpSecretId,
      TmpSecretKey: Cos.tmpSecretKey,
      XCosSecurityToken: Cos.sessionToken,
      StartTime: Cos.createTime, // 开始时间戳，单位秒
      ExpiredTime: Cos.expireTime // 过期时间戳，单位秒
    }
    callback(obj)
  }
})

// cos上传
const handleFileInUploading = (file: any) => {
  cos.putObject({
    Bucket: 'prod-1309639790', /* 填写自己的 bucket，必须字段 */
    Region: 'ap-guangzhou',     /* 存储桶所在地域，必须字段 */
    Key: 'banner/' + file.name,              /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */
    StorageClass: 'STANDARD', //上传模式，标准
    Body: file, // 上传文件对象
    // SliceSize: 1024 * 1024 * 5,     /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */
    onProgress: function (progressData: any) {
      console.log(JSON.stringify(progressData));
    }
  }, function (err: any, data: any) {
    if (err) {
      console.log('上传失败', err);
    } else {
      addForm.bannerImg = `https://${data.Location}`
      console.log(addForm.bannerImg);

    }
  });
}
const changeSelect=(e:string)=>{
  applicationId.value=e;
  getreqlylist();
}
const changeSelect1=(e:string)=>{
  addForm.applicationId=e;

}
</script>

<style lang="scss" scoped>
.slideshow {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }
  :deep(.el-dialog) {
    width: 35%;

    .el-form {
      width: 100%;

      .el-select {
        width: 100%;
      }
    }
  }

  .avatar-uploader {
    width: 100px;
    height: 100px;
    box-sizing: border-box;
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }

    .upImg {
      display: none;
    }


    img {
      width: 100px;
      height: 100px;
    }

    .el-upload:hover {
      border-color: var(--el-color-primary);
    }
  }

  .el-pagination {
    margin-top: auto;
    margin-bottom: 0;
    background-color: #fff;
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
    z-index: 10;
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
  }
}
</style>
