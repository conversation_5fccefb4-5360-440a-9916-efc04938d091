<template>
  <div class="dailyRecall">
    <el-card class="box-card">
      <template #header>
        <el-form :inline="true" :model="queryForm" class="demo-form-inline">
          <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="时间选择">
              <el-date-picker v-model="date" :clearable="false" unlink-panels value-format="x"
                format="YYYY-MM-DD" start-placeholder="开始日期"
                size="default" @change="timeChange" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
      </template>
      <div class="table-container">
        <el-table :data="TableLists" style="width: 100%; height: 100%" v-loading="Loading">
        <el-table-column prop="time" label="召回日期" min-width="60"></el-table-column>
        <el-table-column prop="category" label="召回类型" min-width="60">
          <template #default="{row}">
            {{ row.category?'短信召回':'系统召回' }}
          </template>
        </el-table-column>
        <el-table-column prop="days" label="召回条件" min-width="60">
          <template #default="{row}">
            {{row.days}}天未登录
          </template>
        </el-table-column>
        <el-table-column prop="sendUserCount" label="发送用户" min-width="60"></el-table-column>
        <el-table-column prop="recallCount" label="回归数量" min-width="60"></el-table-column>
        <el-table-column prop="rechargeAmount" label="充值金额" min-width="60">
          <template #default="{row}">
            {{row.rechargeAmount?row.rechargeAmount:'0'}}
          </template>
        </el-table-column>
      </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {query_recall_data} from '@/api/index'
import { ref ,reactive} from 'vue';
import {getYMD} from '@/utils/date'
import type { FormInstance } from "element-plus";
let toData = new Date(new Date().toLocaleDateString()).getTime();

const date=ref<any>(getYMD('-',toData));
const Loading=ref<boolean>(false);
const queryForm = reactive<any>({
  sendDate: getYMD('-',toData),
  applicationId:'com.dongxin.app'
});
const queryFormRef = ref<FormInstance>();
let TableLists = ref([]);

const onreset = (_formEl: FormInstance | undefined) => {
  queryForm.sendDate=getYMD('-',toData);
  date.value=getYMD('-',toData)
  query();
};

async function query() {
  Loading.value=true;
  query_recall_data({...queryForm}).then(res=>{
    TableLists.value=res.data.data;
    Loading.value=false;
  })
}

const timeChange = (data: any) => {
  queryForm.sendDate=getYMD('-',data)
  
};
query();



const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}

</script>

<style lang="scss" scoped>
.dailyRecall {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    width: 100%;
  }
}
</style>