<template>
  <div class="feedback">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-form ref="queryFormRef" :inline="true" :model="queryForm" class="demo-form-inline">
            <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select v-model="queryForm.type" placeholder="请选择">
                <el-option label="全部" value="null" />
                <el-option label="产品建议" :value="1" />
                <el-option label="功能效率" :value="2" />
                <el-option label="隐私问题" :value="3" />
                <el-option label="其他问题" :value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="电话" prop="mobile">
              <el-input style="width: 200px;" v-model="queryForm.mobile" />
            </el-form-item>
            <el-form-item label="反馈类容" prop="content">
              <el-input style="width: 200px;" v-model="queryForm.content" />
            </el-form-item>
            <el-form-item label="选择时间">
              <el-date-picker v-model="date" :clearable="false" type="daterange" unlink-panels value-format="x"
                  format="YYYY-MM-DD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  :shortcuts="shortcuts" size="default" @change="timeChange" />
            </el-form-item>
            <el-form-item class="el-form-itembtn">
              <el-button type="primary" @click="query">查询</el-button>
              <el-button @click="onreset(queryFormRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="TableLists" style="width: 100%; height: 100%">
        <el-table-column prop="userId" label="用户ID" min-width="60"></el-table-column>
        <el-table-column prop="time" label="反馈时间" min-width="120"></el-table-column>
        <el-table-column prop="nickname" label="用户名" min-width="60"></el-table-column>
        <el-table-column prop="avatar" label="头像" min-width="60">
          <template #default="scope">
            <el-image style="width: 70px; height: 70px" :src="scope.row.avatar" :zoom-rate="1.2"
              :preview-src-list="faceList" :initial-index="0" fit="cover" :preview-teleported="true"
              :hide-on-click-modal="true" @click="fdimg(scope.row.avatar)"  />
          </template>
        </el-table-column>
        <el-table-column label="反馈类型" prop="type">
          <template #default="scope">
            {{ scope.row.type === 1 ? "产品建议" :
              scope.row.type === 2 ? "功能效率" : scope.row.type === 3 ? "隐私问题" : scope.row.type === 4 ? "其他问题" : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="photos" label="反馈图片" min-width="60" >
          <template #default="scope">
            <div style="display: flex; flex-wrap: wrap;" >
            <el-image style="width: 70px; height: 70px; margin: 5px;" :src="item" :zoom-rate="1.2"
            v-for=" (item,index) in scope.row.photos" :key="index"
              :preview-src-list="faceList" :initial-index="0" fit="cover" :preview-teleported="true"
              :hide-on-click-modal="true" @click="fdimg(item)" /> 
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="反馈类容" min-width="60"></el-table-column>
        <el-table-column prop="mobile" label="联系电话" min-width="60"></el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
            <template #default="scope">
              <el-popconfirm title="你确定删除吗?" @confirm="deleUser(scope.row)">
                <template #reference>
                  <el-button type="danger" size="small" :icon="Delete" />
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
      </el-table>
      </div>

      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
        :small="true" layout="total, sizes, prev, pager, next, jumper" :total="totalNum" :background="true"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { query_reedback_record ,delete_reedback_record} from '@/api/index'
import { ref, reactive } from 'vue'
import { Delete } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import pageHook from "@/hooks/pageHook";
import {getYMDHMS} from '@/utils/date'
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData - 7 * 3600 * 24 * 1000; //开始时间
let todayEnd = toData + 24 * 60 * 60 * 1000 - 1; //结束时间
let queryForm = reactive<any>({
  applicationId:'com.dongxin.app'
})
let date = ref<[Date, Date]>([
  new Date(todayStart),
  new Date(todayEnd),
])
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } =
  pageHook(query);

let faceList = ref<any>([]);
let TableLists = ref([]);

let queryFormRef = ref<any>(null);
const fdimg = (img: any) => {
  faceList.value = [img];
};
const timeChange = (data: any) => {
  queryForm.begin = data[0];
  queryForm.end = data[1];
};
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];


async function query() {
  let { data: { data, total } } = await query_reedback_record({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  data.forEach((item:any)=>{
    item.time=getYMDHMS('-',':',item.time*1000);
  })
  TableLists.value = data;
  totalNum = total;
}

query();

const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}

const deleUser=(e:any)=>{
delete_reedback_record({id:e.id}).then(_res=>{
  query();
})
}
const onreset = (formEl: FormInstance) => {
  date.value = [
    new Date(todayStart),
    new Date(todayEnd),
  ]
  queryForm.begin = todayStart
  queryForm.end = todayEnd
  if (!formEl) return

  formEl.resetFields()
  query();
}

</script>

<style lang="scss" scoped>
.feedback {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    width: 100%;

    .el-form {
      width: 100%;
    }
  }
}
</style>