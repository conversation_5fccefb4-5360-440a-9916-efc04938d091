<template>
  <div class="goldsstatistics">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <majaSelect :applicationId="applicationId" @changeSelect="changeSelect"/>
          <el-button @click="open" :icon="Plus">添加</el-button>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="tabData" style="width: 100%; height: 100%">
        <el-table-column prop="incomePoint" label="今日收益" min-width="120" fixed></el-table-column>
        <el-table-column prop="replyRate" label="三分钟回复率" min-width="120">
          <template #default="{row}">
        {{ (row.replyRate*100).toFixed(2)+'%' }}
        </template>
        </el-table-column>
        <el-table-column prop="replyRate30" label="30秒回复率" min-width="120">
          <template #default="{row}">
        {{ row.replyRate30?(row.replyRate30*100).toFixed(2)+'%' :'0%'}}
        </template>
        </el-table-column>
        <el-table-column prop="onlineTime" label="在线时长" min-width="120">
          <template #default="{row}">
          {{secondsToHms(row.onlineTime)}}
        </template>
        </el-table-column>
        <el-table-column prop="callRate" label="被动视频接通率" min-width="120">
        <template #default="{row}">
        {{ (row.callRate*100).toFixed(2)+'%' }}
        </template>
        </el-table-column>
        <el-table-column prop="maxIncomePoint" label="平台最高收入" min-width="120"></el-table-column>
        <el-table-column prop="callSeconds" label="视频通话时长" min-width="120">
          <template #default="{row}">
          {{secondsToHms(row.callSeconds)}}
        </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleEid(scope.row)" >编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <el-dialog v-model="addShow" :title="eidShow?'编辑':'添加'" class="add" width="500px" @close="handleClose()">
        <el-form ref="addFormRef" :model="addForm" status-icon class="demo-ruleForm" :rules="rules" label-width="150px">
          <el-form-item label="马甲包">
              <majaSelect :applicationId="addForm.applicationId" @changeSelect="changeSelect1" :disabled="eidShow"/>
            </el-form-item>
          <el-form-item label="今日收益" prop="incomePoint">
            <el-input v-model="addForm.incomePoint"> </el-input>
          </el-form-item>
          <el-form-item label="三分钟回复率" prop="replyRate">
            <el-input-number v-model="addForm.replyRate" :controls="false" :max="1.00" :min="0.00"> </el-input-number >
          </el-form-item>
          <el-form-item label="30秒回复率" prop="replyRate30">
            <el-input-number  v-model="addForm.replyRate30" :controls="false"  :max="1.00" :min="0.00"> </el-input-number>
          </el-form-item>
          <el-form-item label="在线时长(秒)" prop="onlineTime">
            <el-input-number v-model="addForm.onlineTime" :controls="false" autocomplete="off"> </el-input-number>
          </el-form-item>
          <el-form-item label="被动视频接通率" prop="callRate">
            <el-input-number  v-model="addForm.callRate" :controls="false"  :max="1.00" :min="0.00"> </el-input-number>
          </el-form-item>
          <el-form-item label="平台最高收入" prop="maxIncomePoint">
            <el-input-number  v-model="addForm.maxIncomePoint" :controls="false"> </el-input-number>
          </el-form-item>
          <el-form-item label="视频通话时长(秒)" prop="callSeconds">
            <el-input-number  v-model="addForm.callSeconds" :controls="false"> </el-input-number>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addUser(addFormRef)">添加</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from 'element-plus'
import { make_friends_api ,save_make_freiends_api} from "@/api/index"
import { Plus } from "@element-plus/icons-vue";
import {secondsToHms} from "@/utils/date";
let applicationId=ref('com.dongxin.app')
let addFormRef=ref<FormInstance>();
let tabData = ref<any>([])
let addShow=ref(false);
let eidShow=ref(false);
let addForm =ref<any>({
  incomePoint:null,
  replyRate:null,
  onlineTime:null,
  callRate:null,
  maxIncomePoint:null,
  callSeconds:null,
  applicationId:'com.dongxin.app',
  replyRate30:null,
})

const rules = reactive({
  incomePoint: [{ required: true, message: "不能为空", trigger: "blur" }],
  replyRate: [{ required: true, message: "不能为空", trigger: "blur" }],
  onlineTime: [{ required: true, message: "不能为空", trigger: "blur" }],
  callRate: [{ required: true, message: "不能为空", trigger: "blur" }],
  maxIncomePoint: [{ required: true, message: "不能为空", trigger: "blur" }],
  callSeconds: [{ required: true, message: "不能为空", trigger: "blur" }],
  replyRate30: [{ required: true, message: "不能为空", trigger: "blur" }],
});


async function query() {
  let res = await make_friends_api({applicationId:applicationId.value})
  let { data } = res.data
 tabData.value[0]=data;
}
query()

const handleEid= (e:any)=>{
  addForm.value=JSON.parse(JSON.stringify(e));
  eidShow.value=true;
  addShow.value=true;
}

const open = () => {
  addShow.value = true;
};


const addUser = (addFormRef: FormInstance | undefined) => {
  if (!addFormRef) return;
  addFormRef.validate(async (valid: any) => {
    if (valid) {
      await save_make_freiends_api({ ...addForm.value });
      query()
      addShow.value = false;
      eidShow.value = false      ;
    } else {
      return false;
    }
  });
};

const handleClose=()=>{
  addForm.value={};
  addShow.value=false;
  eidShow.value=false;
}


const changeSelect=(e: string)=>{
  applicationId.value=e;
  query();
}
const changeSelect1=(e:string)=>{
  addForm.value.applicationId=e;

}
</script>

<style lang="scss" scoped>
.goldsstatistics {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }
  }

  .hasErrMark-on {
    color: #f00;
  }
}
</style>