<template>
  <div class="pointsstatistics">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item>
                <el-button :icon="Plus" @click="addShow = true">增加</el-button>
              </el-form-item>
              <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
              <el-form-item label="用户ID" prop="userId">
                <el-input v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item label="等级选择">
                <el-select v-model="queryForm.dataLevel" placeholder="请选择">
                  <el-option label="全部" :value="opNull" />
                  <el-option label="优" :value="1" />
                  <el-option label="良" :value="2" />
                  <el-option label="中" :value="3" />
                  <el-option label="差" :value="4" />
                  <el-option label="极差" :value="5" />
                </el-select>
              </el-form-item>
              <el-form-item label="排序">
                <el-select v-model="queryForm.sortType" placeholder="请选择">
                  <el-option label="默认排序" :value="opNull" />
                  <el-option label="收益排序" :value="0" />
                  <el-option label="周平均分排序" :value="1" />
                </el-select>
              </el-form-item>
              <el-form-item class="qubtn">
                <el-button type="primary" @click="query">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="tabData" style="width: 100%; height: 100%" v-loading="loading">
          <el-table-column prop="userId" label="id" min-width="120"></el-table-column>
          <el-table-column prop="femaleLevel" label="评级" min-width="120">
            <template #default="{ row }">
              <span style="cursor: pointer" @click="onEditFemaleLevel(row)" v-if="showFemaleLevelId !== row.id">{{ row.femaleLevel }}</span>
              <el-select v-else v-model="row.femaleLevel" placeholder="请选择" @change="(e) => handleChangeFemaleLevel(e, row)">
                <el-option label="S" value="S" />
                <el-option label="A" value="A" />
                <el-option label="B1" value="B1" />
                <el-option label="B2" value="B2" />
                <el-option label="C" value="C" />
                <el-option label="D" value="D" />
                <el-option label="E" value="E" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="dataLevel" label="交友数据评级" min-width="120">
            <template #default="{ row }">
              <span style="cursor: pointer"
              @click="onEdit(row)"
               v-if="showId !== row.id">{{
                row.dataLevel
              }}</span>
              <el-select
                v-model="row.dataLevel"
                placeholder="请选择"
                v-else
                @change="(e) => handleChange(e, row)"
              >
                <el-option label="优" value="优" />
                <el-option label="良" value="良" />
                <el-option label="中" value="中" />
                <el-option label="差" value="差" />
                <el-option label="极差" value="极差" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="dataAvgScore"
            label="周平均分"
            min-width="120"
          >
          <template #default="{ row }">
          {{row.dataAvgScore?row.dataAvgScore:0}}
          </template>
          </el-table-column>
          <el-table-column
            prop="incomePoint"
            label="今日收益"
            min-width="120"
          ></el-table-column>
          <el-table-column prop="replyRate" label="3分钟回复率" min-width="120">
            <template #default="{ row }">
              {{ (row.replyRate * 100).toFixed(2) + "%" }}
            </template>
          </el-table-column>
          <el-table-column prop="replyRate30" label="30秒回复率" min-width="120">
            <template #default="{ row }">
              {{ (row.replyRate30 * 100).toFixed(2) + "%" }}
            </template>
          </el-table-column>
          <el-table-column prop="onlineTime" label="在线时长" min-width="120">
            <template #default="{ row }">
              {{ secondsToHms(row.onlineTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="callRate" label="被动视频接通率" min-width="120">
            <template #default="{ row }">
              {{ (row.callRate * 100).toFixed(2) + "%" }}
            </template>
          </el-table-column>
          <el-table-column prop="callSeconds" label="视频通话时长" min-width="120">
            <template #default="{ row }">
              {{ secondsToHms(row.callSeconds) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 30, 50]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog v-model="addShow" title="添加评级" style="max-width: 600px;" >
        <el-form ref="addFormRef" :model="addForm" label-width="100px" label-position="right">
          <el-form-item label="用户ID">
          <el-input   v-model="addForm.userId"  :autosize="{ minRows: 20, maxRows: 25 }"
    type="textarea" style="width: 350px; "/>
          </el-form-item>
          <el-form-item label="用户评级">
            <el-select  v-model="addForm.dataLevel"  placeholder="请选择" style="width: 350px;">
                  <el-option label="优" :value="1" />
                  <el-option label="良" :value="2" />
                  <el-option label="中" :value="3" />
                  <el-option label="差" :value="4" />
                  <el-option label="极差" :value="5" />
                </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="addShow = false">取消</el-button>
            <el-button type="primary" @click="addLevel">确认</el-button>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import pageHook from "@/hooks/pageHook";
import {  secondsToHms } from "@/utils/date";
import {  Plus } from "@element-plus/icons-vue";
import { friends_level_api, modifyLevel_api,save_or_update_api, save_level } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  queryPlatformPoints
);
let showFemaleLevelId = ref()
const onEditFemaleLevel = async (row: any) => {
  showFemaleLevelId.value = row.id;
}
const handleChangeFemaleLevel = async (e: string, row: any) => {
  await save_level({ level: e, userId: row.userId });
  showFemaleLevelId.value = 0;
}
let loading = ref(false);
let opNull = ref<any>(null);
const queryFormRef = ref<FormInstance>();
let queryForm = reactive<any>({
  userId:null,
  dataLevel: null,
  sortType: null,
  applicationId:''
});
let addForm = reactive<any>({
  dataLevel:null,
  userId:null,
});
let addShow=ref<boolean>(false);
let showId = ref();
let tabData = ref<any>([]);
async function queryPlatformPoints() {
  let res = await friends_level_api({
    ...queryForm,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;

  data.forEach((item: any) => {
    item.isShow = false;
    switch (item.dataLevel) {
      case 1:
        item.dataLevel = "优";
        break;
      case 2:
        item.dataLevel = "良";
        break;
      case 3:
        item.dataLevel = "中";
        break;
      case 4:
        item.dataLevel = "差";
        break;
      case 5:
        item.dataLevel = "极差";
        break;
      default:
        break;
    }
  });
  tabData.value = data;
  totalNum.value = total;
}

queryPlatformPoints();

const query = () => {
  queryPlatformPoints();
};
const handleChange = async (e: string, row: any) => {
  let dataLevel;
  switch (e) {
    case "优":
      dataLevel = 1;
      break;
    case "良":
      dataLevel = 2;
      break;
    case "中":
      dataLevel = 3;
      break;
    case "差":
      dataLevel = 4;
      break;
    case "极差":
      dataLevel = 5;
      break;
    default:
      break;
  }
  await modifyLevel_api({ dataLevel, userId: row.userId });
  showId.value = 0;
};

const onEdit = async (row: any) => {
  showId.value = row.id;
};
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  queryForm.dataLevel = null;
  formEl.resetFields();
  queryPlatformPoints();
};

const addLevel=()=>{
  addShow.value=false;
  addForm.userId =addForm.userId.replace(/\n/g, ",");
  save_or_update_api({...addForm}).then(()=>{
    query();
    addForm.dataLevel=null;
    addForm.userId=null;
  })
}


const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}
</script>

<style lang="scss" scoped>
.pointsstatistics {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0; /* 确保flex子项可以收缩 */
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .hasAbnormal-on {
    color: #f00;
  }
}
</style>
