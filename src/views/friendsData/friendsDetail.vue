<template>
  <div class="pointsstatistics">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="query">
            <el-form ref="queryFormRef" :inline="true" :model="queryForm">
              <el-form-item label="马甲包">
              <majaSelect :applicationId="queryForm.applicationId" @changeSelect="changeSelect"/>
            </el-form-item>
              <el-form-item label="用户ID" prop="userId">
                <el-input v-model="queryForm.userId" />
              </el-form-item>
              <el-form-item label="选择日期">
                <el-date-picker
                  v-model="date"
                  value-format="x"
                  :clearable="false"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :shortcuts="shortcuts"
                  size="default"
                  @change="timeChange"
                />
              </el-form-item>
              <el-form-item label="等级选择">
                <el-select v-model="queryForm.dataLevel" placeholder="请选择">
                  <el-option label="全部" :value="opNull" />
                  <el-option label="优" :value="1" />
                  <el-option label="良" :value="2" />
                  <el-option label="中" :value="3" />
                  <el-option label="差" :value="4" />
                  <el-option label="极差" :value="5" />
                </el-select>
              </el-form-item>
              <el-form-item label="排序">
                <el-select v-model="queryForm.sortType" placeholder="请选择">
                  <el-option label="默认排序" :value="opNull" />
                  <el-option label="收益排序" :value="0" />
                  <el-option label="日平均排序" :value="1" />
                </el-select>
              </el-form-item>
              <el-form-item class="qubtn">
                <el-button type="primary" @click="query">查询</el-button>
                <el-button @click="onreset(queryFormRef)">重置</el-button>
                <el-button class="ml" type="primary" @click="handleExportExcel">
                  <el-icon style="vertical-align: middle">
                    <Download />
                  </el-icon>
                  <span style="vertical-align: middle">导出Excel</span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>
      <div class="table-container">
        <el-table :data="tabData" style="width: 100%; height: 100%" v-loading="loading">
        <el-table-column
          prop="recordDate"
          label="日期"
          min-width="120"
          fixed
        ></el-table-column>
        <el-table-column prop="userId" label="id" min-width="120"></el-table-column>
        <el-table-column
          prop="femaleLevel"
          label="评级"
          min-width="120"
        ></el-table-column>

        <el-table-column prop="dataLevel" label="交友数据评级" min-width="120">
          <template #default="{ row }">
            <span v-if="showId !== row.id">{{
              row.dataLevel
            }}</span>
            <el-select
              v-model="row.dataLevel"
              placeholder="请选择"
              v-else
              @change="(e) => handleChange(e, row)"
            >
              <el-option label="优" value="优" />
              <el-option label="良" value="良" />
              <el-option label="中" value="中" />
              <el-option label="差" value="差" />
              <el-option label="极差" value="极差" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          prop="dataScore"
          label="日评分"
          min-width="120"
        ><template #default="{ row }">
        {{row.dataScore?row.dataScore:0}}
        </template>
        </el-table-column>
        <el-table-column
          prop="incomePoint"
          label="今日收益"
          min-width="120"
        ></el-table-column>
        <el-table-column prop="replyRate" label="3分钟回复率" min-width="120">
          <template #default="{ row }">
            {{ (row.replyRate * 100).toFixed(2) + "%" }}
          </template>
        </el-table-column>
        <el-table-column prop="onlineTime" label="在线时长" min-width="120">
          <template #default="{ row }">
            {{ secondsToHms(row.onlineTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="callRate" label="被动视频接通率" min-width="120">
          <template #default="{ row }">
            {{ (row.callRate * 100).toFixed(2) + "%" }}
          </template>
        </el-table-column>
        <el-table-column prop="callSeconds" label="视频通话时长" min-width="120">
          <template #default="{ row }">
            {{ secondsToHms(row.callSeconds) }}
          </template>
        </el-table-column>
        <el-table-column prop="replyRate30" label="30秒回复率" min-width="120">
          <template #default="{ row }">
            {{ (row.replyRate30 * 100).toFixed(2) + "%" }}
          </template>
        </el-table-column>

      </el-table>
      </div>

      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[20, 30, 50]"
        :small="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance } from "element-plus";
import pageHook from "@/hooks/pageHook";
import { getYMD, secondsToHms } from "@/utils/date";
import { freiends_detail_api, modifyLevel_api } from "@/api/index";
let { totalNum, currentPage, pageSize, handleSizeChange, handleCurrentChange } = pageHook(
  queryPlatformPoints
);
import { concurrencyRequest } from "@/utils/concurrencyRequest";
import { flattenArray } from "@/utils/list";
import { aoaToSheetXlsx } from "@/utils/excel";
let loading = ref(false);
let toData = new Date(new Date().toLocaleDateString()).getTime();
//今天
let todayStart = toData; //开始时间
let todayEnd = todayStart + 24 * 60 * 60 * 1000 - 1; //结束时间
//最近7天
let past7daysStart = toData - 7 * 3600 * 24 * 1000;
let opNull = ref<any>(null);
let date = ref<[Date, Date]>([new Date(past7daysStart), new Date(todayEnd)]);
const queryFormRef = ref<FormInstance>();
let queryForm = reactive<any>({
  beginDate: getYMD("-", past7daysStart),
  endDate: getYMD("-", todayEnd),
  userId:null,
  dataLevel: null,
  sortType: null,
  applicationId:'com.dongxin.app'
});
let showId = ref();
let tabData = ref<any>([]);
const shortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

const timeChange = (data: any) => {
  queryForm.beginDate = data[0];
  queryForm.endDate = data[1];
};

async function queryPlatformPoints() {
  let res = await freiends_detail_api({
    ...queryForm,
    beginDate: queryForm?.beginDate ? getYMD("-", queryForm?.beginDate) : null,
    endDate: queryForm?.endDate ? getYMD("-", queryForm?.endDate) : null,
    pageSize: pageSize.value,
    pageNum: currentPage.value,
  });
  let { data, total } = res.data;

  data.forEach((item: any) => {
    item.isShow = false;
    switch (item.dataLevel) {
      case 1:
        item.dataLevel = "优";
        break;
      case 2:
        item.dataLevel = "良";
        break;
      case 3:
        item.dataLevel = "中";
        break;
      case 4:
        item.dataLevel = "差";
        break;
      case 5:
        item.dataLevel = "极差";
        break;
      default:
        break;
    }
  });
  tabData.value = data;
  totalNum.value = total;
}

queryPlatformPoints();

const query = () => {
  queryPlatformPoints();
};

const handleChange = async (e: string, row: any) => {
  let dataLevel;
  switch (e) {
    case "优":
      dataLevel = 1;
      break;
    case "良":
      dataLevel = 2;
      break;
    case "中":
      dataLevel = 3;
      break;
    case "差":
      dataLevel = 4;
      break;
    case "极差":
      dataLevel = 5;
      break;
    default:
      break;
  }
  await modifyLevel_api({ dataLevel, userId: row.userId });
  showId.value = 0;
};

// const onEdit = async (row: any) => {
//   showId.value = row.id;
// };
const onreset = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  queryForm.beginDate = null;
  queryForm.endDate = null;
  queryForm.dataLevel = null;
  date.value = [new Date(past7daysStart), new Date(todayEnd)];
  formEl.resetFields();
  queryPlatformPoints();
};

async function getKeys() {
  let queue: any = [];
  let pageNum = Math.ceil(totalNum.value / 10000);
  for (let i = 1; i <= pageNum; i++) {
    let urls = {
      data: {
        ...queryForm,
        beginDate: queryForm?.beginDate ? getYMD("-", queryForm?.beginDate) : null,
        endDate: queryForm?.endDate ? getYMD("-", queryForm?.endDate) : null,
        pageNum: i,
        pageSize: 10000,
      },
      url: "/admin/make/friends/data/detail",
    };

    queue.push(urls);
  }
  let res = await concurrencyRequest(queue, 2);
  return flattenArray(res);
}

const handleExportExcel = async () => {
  loading.value = true;
  let res = await getKeys();
  res.forEach((v: any) => {
    v.replyRate = v.replyRate ? (v.replyRate * 100).toFixed(2) + "%" : "0%";
    v.callRate = v.callRate ? (v.callRate * 100).toFixed(2) + "%" : "0%";
    v.callSeconds = v.callSeconds ? secondsToHms(v.callSeconds) : "00:00:00";
    v.onlineTime = v.onlineTime ? secondsToHms(v.onlineTime) : "00:00:00";
    v.replyRate30=v.replyRate30?(v.replyRate30 * 100).toFixed(2) + "%":'0.00%'
    switch (v.dataLevel) {
      case 1:
        v.dataLevel = "优";
        break;
      case 2:
        v.dataLevel = "良";
        break;
      case 3:
        v.dataLevel = "中";
        break;
      case 4:
        v.dataLevel = "差";
        break;
      case 5:
        v.dataLevel = "极差";
        break;
      default:
        break;
    }
  });
  loading.value = false;
  //标题数组
  let titleArr = [
    "日期",
    "id",
    "评级",
    "交友数据评级",
    "今日收益",
    "3分钟回复率",
    "在线时长",
    "被动视频接通率",
    "视频通话时长",
    "30秒回复率"
  ];
  let header = titleArr;
  let data = res.map((item: any, _index: any) => {
    let {
      recordDate,
      userId,
      femaleLevel,
      dataLevel,
      incomePoint,
      replyRate,
      onlineTime,
      callRate,
      callSeconds,
      replyRate30
    } = item;
    return [
      recordDate,
      userId,
      femaleLevel,
      dataLevel,
      incomePoint,
      replyRate,
      onlineTime,
      callRate,
      callSeconds,
      replyRate30
    ];
  });
  aoaToSheetXlsx({
    data,
    header,
    filename: `用户交友数据统计.xlsx`,
  });
};
const changeSelect=(e:string)=>{
  queryForm.applicationId=e;
  query();
}
</script>

<style lang="scss" scoped>
.pointsstatistics {
  height: calc(100vh - 60px);
  width: 100%;
  display: flex;
  flex-direction: column;

  .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 0;
      position: relative;
      overflow: hidden;
    }

    .table-container {
      flex: 1;
      overflow: auto;
      min-height: 0;
    }

    .el-pagination {
      margin-top: auto;
      margin-bottom: 0;
      background-color: #fff;
      padding: 10px 20px;
      border-top: 1px solid #ebeef5;
      z-index: 10;
      flex-shrink: 0;
      position: sticky;
      bottom: 0;
    }
  }

  .hasAbnormal-on {
    color: #f00;
  }
}
</style>
