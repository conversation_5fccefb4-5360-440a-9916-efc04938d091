
export function man_leave_data(e:any=[],index:number){
  let data='0%';
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,retentionRate }=item;
    if(  index+1===days){
      data= (retentionRate * 100).toFixed(2) + "%";  
    }
  })
  if(day>index){
    return data;
  }else{
    return null;
  }
}


export function register_leave_data(e:any=[],index:number){
  let data='0';
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,loginUserCount }=item;
    if(  index+1===days){
      data= loginUserCount;
    }
  })
  
  if(day>index){
    return data;
  }else{
    return null;
  }
}


export function roi_data(e:any=[],index:number){
  let data='';
  e.map((item:any)=>{
    let{ days ,roiRate }=item;
    if(  index+1===days){
      data= (roiRate*100).toFixed(2) + "%";
    }
  })
  
    return data;
}

export function roi_active(e:any=[],index:number){
  let data=false;
  e.map((item:any)=>{
    let{ days ,roiRate }=item;
    if(  index+1===days){
      roiRate>=1?data=true:'';
    }
  })
  
    return data;
}



export function paySumAmount_num(e:any=[],index:number){
  let data='';
  e.map((item:any)=>{
    let{ days ,paySumAmount }=item;
    if(  index+1===days){
       data= paySumAmount;
    }
  })
    return data;
}



//付费人数
export function payMan_num(e:any=[],index:number){
  let data=0;
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,todayPayUserSize }=item;
    if(  index+1===days){
       data= todayPayUserSize;
    }
  })
  
  if(day>index){
    return data;
  }else{
    return null;
  }
}

//新增付费人数
export function addPayMan_num(e:any=[],index:number){
  let data=0;
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,newPayUserSize }=item;
    if(  index+1===days){
       data= newPayUserSize;
    }
  })
  if(day>index){
    return data;
  }else{
    return null;
  }
}


//付费总人数
export function addPayMan_sum(e:any=[],index:number){
  let data=0;
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,currentPayUserSize }=item;
    if(  index+1===days){
       data= currentPayUserSize;
    }
  })
  if(day>index){
    return data;
  }else{
    return null;
  }
}
//付费留存率
export function addPayMan_retention(e:any=[],index:number){
  let data='0%';
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,retention }=item;
    if(  index+1===days){
      data= (retention * 100).toFixed(2) + "%";  
    }
  })
  if(day>index){
    return data;
  }else{
    return null;
  }
}

//建联总人数
export function jianlian_hook(e:any=[],index:number){
  let data=0;
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,jianlianCnt }=item;
    if(  index+1===days){
       data= jianlianCnt;
    }
  })
  if(day>index){
    return data;
  }else{
    return 0;
  }
}

//私信人数
export function sixinCnt_hook(e:any=[],index:number){
  let data=0;
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,sixinCnt }=item;
    if(  index+1===days){
       data= sixinCnt;
    }
  })
  if(day>index){
    return data;
  }else{
    return 0;
  }
}


export function callVideoCnt_hook(e:any=[],index:number){
  let data=0;
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,callVideoCnt }=item;
    if(  index+1===days){
       data= callVideoCnt;
    }
  })
  if(day>index){
    return data;
  }else{
    return 0;
  }
}

export function videoConnectCnt_hook(e:any=[],index:number){
  let data=0;
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,videoConnectCnt }=item;
    if(  index+1===days){
       data= videoConnectCnt;
    }
  })
  if(day>index){
    return data;
  }else{
    return 0;
  }
}


export function replyRate_hooks(e:any=[],index:number){
  let data='0%';
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,replyRate }=item;
    if(  index+1===days){
      data=replyRate?replyRate+ "%":"0.00%";  
    }
  })
  if(day>index){
    return data;
  }else{
    return null;
  }
}

export function onlineMinterCnt_hook(e:any=[],index:number){
  let data=0;
  let day = e[e.length-1]?.days;
  e.map((item:any)=>{
    let{ days ,onlineMinterCnt }=item;
    if(  index+1===days){
       data= onlineMinterCnt?onlineMinterCnt:0;
    }
  })
  if(day>index){
    return data;
  }else{
    return 0;
  }
}
