// 分页
import { ref} from "vue";
let pageHook = (getData:any)=>{
    let totalNum = ref<number>(0) //总页数
    let currentPage = ref<number>(1) //当前页
    let pageSize = ref<number>(20) //当前条数

    // 分页 每条页数更改
    const handleSizeChange = (val: number) => {
        pageSize.value = val
        getData();
    };
    // 当前页码改变
    const handleCurrentChange = (val: number) => {
        currentPage.value = val
        getData();
    };
    return {
        totalNum,
        currentPage,
        pageSize,
        handleSizeChange,
        handleCurrentChange,
    }
}

export default pageHook