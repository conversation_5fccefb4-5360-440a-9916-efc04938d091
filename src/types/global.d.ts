declare module 'element-plus/dist/locale/zh-cn.mjs'

// 登录
interface userFom {
  account: string;
  password: string;
}


// 角色
interface role {
  roleName?: string;
  id?: Number;
  pageSize?: Number;
  pageNum?: Number;
  roleId?: any;
  resourceIds?: Array;
}

// 后台用户
interface userManage {
  account?: any;
  password?: string;
  userName?: string;
  mobile?: any;
  roleIds?: Array;
  id?: any;
  status?: any;
  pageSize?: Number;
  pageNum?: Number;
}
// 权限目录资源
interface roleMenu {
  id?: any;
  name?: string | null;
  sn?: any;
  url?: string | null;
  uriName?: string | null;
  permission?: string | null;
  icon?: string | null;
  type?: any;
  parentId?: any;
  component?: string | null;
  redirectionUrl?: string | null;
  pageSize?: Number;
  pageNum?: Number;
}

// 统计
interface statistics {
  time?: Number;
  startTime?: Number;
  endTime?: Number;
  lineOneType?: Number;
  lineTwoType?: Number;
}

// 认证
interface authentication {
  userId?: any;
  startTime?: any;
  endTime?: any;
  id?: Number;
  idCard?: string | null;
  realName?: string | null;
  state?: any;
  pageSize?: Number;
  pageNum?: Number;
  applicationId?:String|null
}

// 动态审核,相册
interface dynamic {
  userId?: Number;
  id?: Number;
  type?: Number;
  displayStatus?: Number;
  status?: Number;
  startTime?: Number;
  endTime?: Number;
  pageSize?: Number;
  pageNum?: Number;
  operatorDesc?: string;
}

// 前台用户
interface foregUser {
  id?: Number | null;
  userId?: Number | null;
  inviteCode?: string | null;
  mobile?: Number | null;
  gender?: Number | null;
  status?: Number | null;
  realAvatarAuthState?: Number | null;
  realNameAuthState?: Number | null;
  inviteCode?: string | null;
  startTime?: Number | null;
  endTime?: Number | null;
  relieveBannedTime?: any;
  bindUserId?: Number | null;
  muteStatus?: Number | null;
  relieveMuteTime?: any;
  pageSize?: Number;
  pageNum?: Number;
  operatorDesc?: string;
}

// 流水
interface flowingwater {
  userId?: Number | null;
  operateType?: Number | null;
  businessType?: Number | null;
  startTime?: Number | null;
  endTime?: Number | null;
  pageSize?: Number;
  pageNum?: Number;
  id?: Number | null;
  orderNum?: string | null;
  status?: string | null;
  businessStatus?: string | null;
  orderType?: string | null;
  productId?: Number | null;
  paymentMethod?: string | null;
  received?: string;
  apply_pass?: string;
  reject?: string;
  date?: any;
  applicationId?:string|null;
  inviteUserType?: string | null;
}

//礼物
interface gifts {
  id?: number | null;
  status?: string | null;
  title?: string;
  price?: number;
  picUrl?: string;
  svga?: string;
  remark?: string;
  sort?: number;
  giftType?: number | null;
  pageSize?: Number;
  pageNum?: Number;
}

//签到
interface sign {
  id?: any;
  days?: any;
  awardId?: any;
  awardNum?: any;
  awardType?: any;
  description?: any;
  awardResourceUrl?: any;
  status?: string | null;
  pageSize?: Number;
  pageNum?: Number;
  gender?:Number|null;
  applicationId?: string|null;
}
// 钱包 
interface goldspoints {
  id?: Number | null;
  userId?: Number | null;
  golds?: Number | null;
  goldsFree?: Number | null;
  points?: string | null;
  hasAdd?: Boolean | null;
  hasPointsSort?: Boolean | null;
  pageSize?: Number;
  pageNum?: Number;
  applicationId?:string | null;
  pointsBusinessType?: string | null;
}

// 举报
interface report {
  id?: Number | null;
  userId?: Number | null;
  targetUserId?: Number | null;
  contentId?: Number | null;
  businessType?: Number | null;
  type?: Number | null;
  handleStatus?: Number | null;
  startTime?: Number | null;
  endTime?: Number | null;
  pageSize?: Number;
  pageNum?: Number;
  operatorDesc?: string;
  desc?:number;
}

//邀请白名单
interface whitelist {
  id?: Number | null;
  userId?: Number | null;
  proportion?: any;
  toUserId?: Number | null;
  status?: Number | null;
  dateBegin?: any;
  dateEnd?: any;
  pageSize?: Number;
  pageNum?: Number;
  text?:String |null;
  price?:number | null;
  level?:String | null;
  applicationId?:String | null
}

//RTC房间管理
interface rtcRoom {
  id?: Number | null;
  closeStreamUserId?: Number | null;
  type?: Number | null;
  userId?: Number | null;
  toUserId?: Number | null;
  roomId?: string | null;
  callStatus?: Number | null;
  roomStatus?: Number | null;
  startTime?: Number | null;
  endTime?: Number | null;
  pageSize?: Number;
  pageNum?: Number;
  applicationId?: string | null
}

//EXcel 数据详情
interface dataDetail {
  id?: Number | null;
  oneLevel?: string;
  twoLevel?: string;
  threeLevel?: string;
  popularize?: string;
  gender?: Number | null;
  downNum?: Number | null;
  hasSort?: Boolean;
  hasMarket?: Boolean;
  startTime?: any;
  dateTime?: any;
  channel?: string | null;
  endTime?: any;
  applicationId?: string;
}

//app版本管理
interface version {
  id?: Number | null;
  clientType?: Number | null;
  title?: string | null;
  content?: string | null;
  forceUpdate?: Boolean;
  downloadUrl?: string | null;
  versionCode?: Number | null;
  versionName?: string | null;
  description?: string | null;
  remark?: string | null;
  channelUrl?: string | null;
  channelName?: string | null;
  marketName?: string | null;
  code?: Number | null;
  pageSize?: Number;
  pageNum?: Number;
  applicationId?: string | null;
}

//违规 、封禁列表
interface userViolation {
  id?: Number | null;
  info?: string | null;
  bannedType?: Number | null;
  userId?: Number | null;
  startTime?: Number | null;
  endTime?: Number | null;
  pageSize?: Number;
  pageNum?: Number;
  fromUserId?: String | null;
  toUserId?: String | null;
  type?:Number|null;
  applicationId?:string|null;
}
interface retention {
  oneLevel?: string | null;
  twoLevel?: string | null;
  threeLevel?: string | null;
  pageSize?: Number;
  pageNum?: Number;
}

interface sums{
  registerDate: string,
    channel:string,
    newActiveRegisterPayCost: any ,
    newRegisterPayCost: any,
    newActiveRegisterPayCnt: number|string,
    newRegisterPayCnt: number|string,
    investmentAmount: any,
    downloadCnt: number|string,
    downloadCost: any,
    maleLoginCnt: number|string,
    femaleLoginCnt: number|string,
    sumLoginCht: number|string,
    activeCnt: number|string,
    activeCost: any,
    downloadActiveConversion: number,
    registerCnt: number|string,
    registerCost: any,
    maleRegisterCnt: number|string,
    femaleRegisterCnt: number|string,
    activeRegisterConversion: number,
    newUserPayAmount: number|string,
    oldUserPayAmount: number|string,
    payAmount: number|string,
    newUserPayCnt:any,
    oldUserPayCnt: number|string,
    payCnt: number|string,
    newPayRate: number,
    firstPayAmount:number|string,
    firstPayCnt:number|string,
    newPayCost: any,
    newUserArpu: any,
    newUserArppu: any,
    oldUserArppu: any,
    oldUserArpu:any,
    newActiveUserPayAmount:number|string,
    secondPayCnt:any,
    avgFirstPayMinutes:any,
    avgSecondPayMinutes:any,
}