{
  "compilerOptions": {
    "target": "ES2020",
    // 允许编译器编译JS，JSX文件
    "allowJs": true,
    // 允许在JS文件中报错，通常与allowJS一起使用
    "checkJs": false,
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    /* Bundler mode */
    "moduleResolution": "node",
    "types":["element-plus/global"],
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "paths": {
      "@/*": ["./src/*"]
    },
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/utils/uploadCos.js"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
