import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from "path"
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    })
  ],
  resolve: {
    alias: {
      "@": resolve(__dirname, "src")
    }
  },
  server: {
    port: 5000,
    open: true,
    proxy: {
      '/apipro': {
        target: 'https://devapi.ge0.cc/',
        changeOrigin: true,  // 开启代理
        rewrite: (path) => path.replace(/^\/apipro/, ''),  // 重写路径
      }
    }
  }
  //  server: {
  //   port: 5000,
  //   open: true,
  //   proxy: {
  //     '/apipro': {
  //       target: 'https://api.gxdkkj.xyz/',
  //       changeOrigin: true,  // 开启代理
  //       rewrite: (path) => path.replace(/^\/apipro/, ''),  // 重写路径
  //     }
  //   }
  // }
})
